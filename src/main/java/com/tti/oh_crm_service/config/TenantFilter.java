package com.tti.oh_crm_service.config;

import java.io.File;
import java.nio.file.Paths;
import java.util.List;

import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;

import reactor.core.publisher.Mono;

@Component
@Order(1)
class TenantFilter implements WebFilter {

    @SuppressWarnings("null")
    @Override
    public Mono<Void> filter(ServerWebExchange serverWebExchange, 
      WebFilterChain webFilterChain) {
        
        System.out.println("----- Request path: " + serverWebExchange.getRequest().getPath().toString());
        
        // Allow "swagger" request
        if (serverWebExchange.getRequest().getPath().toString().contains("swagger")
        || serverWebExchange.getRequest().getPath().toString().contains("api-docs")) {
            return webFilterChain.filter(serverWebExchange);
        }

        List<String> tenantNames = serverWebExchange.getRequest().getHeaders().get("Organization-Id");

        if (tenantNames != null && tenantNames.size() > 0) {
            final String tenantName = tenantNames.get(0);
            System.out.println("-------> Org: " + tenantName);
            File[] files = Paths.get("org_dbs").toFile().listFiles();
            boolean isFound = false;
            for (File fileName : files) {
                if (fileName.getName().equalsIgnoreCase("org_" + tenantName + "_db.properties")) {
                    isFound = true;
                    break;
                }
            }
            if (!isFound) {
                System.out.println("Org not found!!!");
                serverWebExchange.getResponse().setStatusCode(HttpStatusCode.valueOf(403));
                return serverWebExchange.getResponse().setComplete();
            }
            TenantContextHolder.setCurrentTenant("org_" + tenantName + "_db");

            // Store tenant in Reactor Context for reactive chains
            return webFilterChain.filter(serverWebExchange)
                    .contextWrite(context -> TenantContextHolder.withTenant(context, "org_" + tenantName + "_db"));
        } else {
            System.out.println("Org is not provided!!!");
            serverWebExchange.getResponse().setStatusCode(HttpStatusCode.valueOf(403));
            return serverWebExchange.getResponse().setComplete();
        }
    }
}
