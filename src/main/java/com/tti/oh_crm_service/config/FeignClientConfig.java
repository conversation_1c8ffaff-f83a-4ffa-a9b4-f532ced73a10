package com.tti.oh_crm_service.config;

import feign.Logger;
import feign.RequestInterceptor;
import feign.RetryableException;
import org.slf4j.LoggerFactory;
import reactivefeign.ReactiveOptions;
import reactivefeign.client.log.DefaultReactiveLogger;
import reactivefeign.client.log.ReactiveLoggerListener;
import reactivefeign.client.statushandler.ReactiveStatusHandler;
import reactivefeign.client.statushandler.ReactiveStatusHandlers;
import reactivefeign.spring.config.EnableReactiveFeignClients;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Clock;
import java.time.Instant;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.web.reactive.config.EnableWebFlux;

import feign.Request;
import reactivefeign.webclient.WebReactiveOptions;

@Configuration
@EnableWebFlux
@EnableReactiveFeignClients
public class FeignClientConfig {
    @Bean
    public Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }
    @Bean
    public ReactiveLoggerListener loggerListener() {
        return new DefaultReactiveLogger(Clock.systemUTC(), LoggerFactory.getLogger(FeignClientConfig.class.getName()));
    }

    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            // requestTemplate.header(UserContext.CORRELATION_ID, UserContextHolder.getContext().getCorrelationId());
        };
    }

    @Bean
    public static Request.Options requestOptions(ConfigurableEnvironment env) {
        int ribbonReadTimeout = env.getProperty("ribbon.ReadTimeout", int.class, 300000);
        int ribbonConnectionTimeout = env.getProperty("ribbon.ConnectTimeout", int.class, 300000);

        return new Request.Options(ribbonConnectionTimeout, TimeUnit.MILLISECONDS, ribbonReadTimeout, TimeUnit.MILLISECONDS, false);
    }

    @Bean
    public ReactiveStatusHandler reactiveStatusHandler() {
        return ReactiveStatusHandlers.throwOnStatus(
                (status) -> (status == 500),
                (methodKey, response) ->
                        new RetryableException(response.status(), "", null, Date.from(Instant.EPOCH), null));
    }

    @Bean
    public ReactiveOptions reactiveOptions() {
        return new WebReactiveOptions.Builder()
                .setReadTimeoutMillis(300000)
                .setWriteTimeoutMillis(30000)
                .setResponseTimeoutMillis(300000)
                .setConnectTimeoutMillis(300000)
                .build();
    }
    
}

