package com.tti.oh_crm_service.config;

import reactor.core.publisher.Mono;
import reactor.util.context.Context;

/**
 * A unified tenant context holder that works seamlessly with both blocking and reactive APIs.
 * It automatically manages tenant context from Organization-Id header without requiring manual intervention.
 */
public class TenantContextHolder {

    private static final String TENANT_KEY = "tenant";
    private static final ThreadLocal<String> CURRENT_TENANT = new ThreadLocal<>();

    /**
     * Get the current tenant for blocking operations
     */
    public static String getCurrentTenant() {
        return CURRENT_TENANT.get();
    }

    /**
     * Set the current tenant for blocking operations
     */
    public static void setCurrentTenant(String tenant) {
        CURRENT_TENANT.set(tenant);
    }

    /**
     * Clear the current tenant context
     */
    public static void clear() {
        CURRENT_TENANT.remove();
    }

    /**
     * Get the current tenant from Reactor context for reactive operations
     */
    public static Mono<String> getCurrentTenantReactive() {
        return Mono.deferContextual(contextView -> {
            String tenant = contextView.getOrDefault(TENANT_KEY, null);
            if (tenant != null) {
                return Mono.just(tenant);
            }
            // Fallback to ThreadLocal if not in context
            String threadLocalTenant = getCurrentTenant();
            return threadLocalTenant != null ? Mono.just(threadLocalTenant) : Mono.empty();
        });
    }

    /**
     * Execute a Mono with automatic tenant context propagation.
     * This ensures that the tenant from Reactor Context is available in ThreadLocal during execution.
     */
    public static <T> Mono<T> withTenantContext(Mono<T> mono) {
        return Mono.deferContextual(contextView -> {
            String tenant = contextView.getOrDefault(TENANT_KEY, null);
            if (tenant == null) {
                // If no tenant in context, just execute as-is
                return mono;
            }

            // Wrap the mono to set tenant context on the actual execution thread
            return mono
                .transformDeferred(originalMono ->
                    Mono.fromCallable(() -> {
                        // Set ThreadLocal on the execution thread
                        setCurrentTenant(tenant);
                        return tenant;
                    })
                    .then(originalMono)
                    .doFinally(signalType -> {
                        // Clean up ThreadLocal after execution
                        clear();
                    })
                );
        });
    }

    /**
     * Add tenant to Reactor context
     */
    public static Context withTenant(Context context, String tenant) {
        return context.put(TENANT_KEY, tenant);
    }

    /**
     * Get the tenant key for Reactor context
     */
    public static String getTenantKey() {
        return TENANT_KEY;
    }
}
