package com.tti.oh_crm_service.config;

// import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

// import com.tti.oh_crm_service.client.IdAccountFeignClient;
// import com.tti.oh_crm_service.constant.StatusCode;
// import com.tti.oh_crm_service.entity.OrganizationView;
// import com.tti.oh_crm_service.entity.Response;

import javax.sql.DataSource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.HashMap;
// import java.util.List;
import java.util.Map;
import java.util.Properties;

@Configuration
// @ComponentScan(value = "com.tti.oh_crm_service")
public class MultitenantConfiguration {

    @Value("${defaultTenant}")
    private String defaultTenant;

    // @Autowired
    // private IdAccountFeignClient idAccountFeignClient;

    @Bean
    @Primary
    @RefreshScope
    public DataSource dataSource() {
        // Response<List<OrganizationView>> orgs = idAccountFeignClient.getOrganizations();
        // if (orgs.getCode() == StatusCode.OK) {
        //     System.out.println(orgs.getData().size());
        // }

        File[] files = Paths.get("org_dbs").toFile().listFiles();
        Map<Object, Object> resolvedDataSources = new HashMap<>();
        System.out.println("+++++++++");
        for (File propertyFile : files) {
            Properties tenantProperties = new Properties();
            @SuppressWarnings("rawtypes")
            DataSourceBuilder dataSourceBuilder = DataSourceBuilder.create();

            try {
                tenantProperties.load(new FileInputStream(propertyFile));
                String tenantId = tenantProperties.getProperty("name");

                dataSourceBuilder.driverClassName(tenantProperties.getProperty("datasource.driver-class-name"));
                dataSourceBuilder.username(tenantProperties.getProperty("datasource.username"));
                dataSourceBuilder.password(tenantProperties.getProperty("datasource.password"));
                dataSourceBuilder.url(tenantProperties.getProperty("datasource.url"));
                resolvedDataSources.put(tenantId, dataSourceBuilder.build());
            } catch (IOException exp) {
                throw new RuntimeException("Problem in tenant datasource:" + exp);
            }
        }

        AbstractRoutingDataSource dataSource = new MultitenantDataSource();
        dataSource.setDefaultTargetDataSource(resolvedDataSources.get(defaultTenant));
        dataSource.setTargetDataSources(resolvedDataSources);

        dataSource.afterPropertiesSet();
        return dataSource;
    }

}
