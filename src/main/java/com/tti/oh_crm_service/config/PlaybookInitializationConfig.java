package com.tti.oh_crm_service.config;

import com.tti.oh_crm_service.enumeration.EPlaybookStatus;
import com.tti.oh_crm_service.model.GroupedModulePlaybookProcess;
import com.tti.oh_crm_service.model.ModuleStagePlaybook;
import com.tti.oh_crm_service.model.LeadStatusSettings;
import com.tti.oh_crm_service.model.OpportunityStageSettings;
import com.tti.oh_crm_service.repository.GroupedModulePlaybookProcessRepository;
import com.tti.oh_crm_service.repository.ModuleStagePlaybookRepository;
import com.tti.oh_crm_service.repository.LeadStatusSettingsRepository;
import com.tti.oh_crm_service.repository.OpportunityStageSettingsRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Configuration
public class PlaybookInitializationConfig {

    @Autowired
    private GroupedModulePlaybookProcessRepository groupedModulePlaybookProcessRepository;
    @Autowired
    private ModuleStagePlaybookRepository moduleStagePlaybookRepository;
    @Autowired
    private LeadStatusSettingsRepository leadStatusSettingsRepository;
    @Autowired
    private OpportunityStageSettingsRepository opportunityStageSettingsRepository;

    @Value("${playbook.initialization.enabled:true}")
    private boolean initializationEnabled;

    @Value("${playbook.initialization.modules:LEAD,OPPORTUNITY}")
    private String[] modulesToInitialize;

    @PostConstruct
    @Transactional
    public void initializePlaybookData() {
        if (!initializationEnabled) {
            log.info("Playbook data initialization is disabled via configuration");
            return;
        }

        log.info("Starting playbook data initialization...");
        log.info("Modules to initialize: {}", Arrays.toString(modulesToInitialize));
        
        try {
            List<String> modules = Arrays.asList(modulesToInitialize);
            
            for (String moduleCode : modules) {
                String normalizedModuleCode = moduleCode.trim().toUpperCase();
                GroupedModulePlaybookProcess groupedProcess = initializeModulePlaybookProcess(normalizedModuleCode);

                // Initialize ModuleStagePlaybook records for this module
                if (groupedProcess != null) {
                    initializeModuleStagePlaybooks(groupedProcess, normalizedModuleCode);
                }
            }
            
            log.info("Playbook data initialization completed successfully!");
            
        } catch (Exception e) {
            log.error("Error during playbook data initialization: ", e);
            // Don't throw exception to prevent application startup failure
        }
    }

    private GroupedModulePlaybookProcess initializeModulePlaybookProcess(String moduleCode) {
        log.info("Checking if GroupedModulePlaybookProcess exists for module: {}", moduleCode);
        
        Optional<GroupedModulePlaybookProcess> existingProcess = 
            groupedModulePlaybookProcessRepository.findByModuleCode(moduleCode);
        
        if (existingProcess.isPresent()) {
            log.info("GroupedModulePlaybookProcess for module '{}' already exists with ID: {}",
                    moduleCode, existingProcess.get().getId());
            return existingProcess.get();
        } else {
            log.info("Creating new GroupedModulePlaybookProcess for module: {}", moduleCode);

            GroupedModulePlaybookProcess newProcess = new GroupedModulePlaybookProcess();
            newProcess.setModuleCode(moduleCode);
            newProcess.setViewCount(0);
            newProcess.setStatus(EPlaybookStatus.PUBLISHED);

            GroupedModulePlaybookProcess savedProcess = groupedModulePlaybookProcessRepository.save(newProcess);

            log.info("Successfully created GroupedModulePlaybookProcess for module '{}' with ID: {}",
                    moduleCode, savedProcess.getId());
            return savedProcess;
        }
    }

    private void initializeModuleStagePlaybooks(GroupedModulePlaybookProcess groupedProcess, String moduleCode) {
        log.info("Initializing ModuleStagePlaybook records for module: {}", moduleCode);

        try {
            if ("LEAD".equals(moduleCode)) {
                initializeLeadStagePlaybooks(groupedProcess);
            } else if ("OPPORTUNITY".equals(moduleCode)) {
                initializeOpportunityStagePlaybooks(groupedProcess);
            } else {
                log.warn("Unknown module code for ModuleStagePlaybook initialization: {}", moduleCode);
            }
        } catch (Exception e) {
            log.error("Error initializing ModuleStagePlaybook records for module '{}': ", moduleCode, e);
        }
    }

    private void initializeLeadStagePlaybooks(GroupedModulePlaybookProcess groupedProcess) {
        log.info("Loading all lead status values from lead_status_settings table...");

        List<LeadStatusSettings> leadStatusSettings = leadStatusSettingsRepository.findAll();
        log.info("Found {} lead status settings", leadStatusSettings.size());

        for (LeadStatusSettings statusSetting : leadStatusSettings) {
            String statusValue = statusSetting.getLeadStatusValue();

            // Check if ModuleStagePlaybook already exists for this combination
            Optional<ModuleStagePlaybook> existingPlaybook = moduleStagePlaybookRepository
                .findByModuleCodeAndModuleStageValue("LEAD", statusValue);

            if (existingPlaybook.isPresent()) {
                log.debug("ModuleStagePlaybook already exists for LEAD status: {}", statusValue);
            } else {
                log.info("Creating ModuleStagePlaybook for LEAD status: {}", statusValue);

                ModuleStagePlaybook newPlaybook = new ModuleStagePlaybook();
                newPlaybook.setModuleCode("LEAD");
                newPlaybook.setModuleStageValue(statusValue);
                newPlaybook.setGuideForSuccessContent(""); // Default empty content
                // Note: createdBy and lastModifiedBy will be null for system-initialized records
                // This is acceptable for initialization data

                ModuleStagePlaybook savedPlaybook = moduleStagePlaybookRepository.save(newPlaybook);

                log.info("Successfully created ModuleStagePlaybook for LEAD status '{}' with ID: {}",
                        statusValue, savedPlaybook.getId());
            }
        }
    }

    private void initializeOpportunityStagePlaybooks(GroupedModulePlaybookProcess groupedProcess) {
        log.info("Loading all opportunity stage values from opportunity_stage_settings table...");

        List<OpportunityStageSettings> opportunityStageSettings = opportunityStageSettingsRepository.findAll();
        log.info("Found {} opportunity stage settings", opportunityStageSettings.size());

        for (OpportunityStageSettings stageSetting : opportunityStageSettings) {
            String stageValue = stageSetting.getOpportunityStageValue();

            // Check if ModuleStagePlaybook already exists for this combination
            Optional<ModuleStagePlaybook> existingPlaybook = moduleStagePlaybookRepository
                .findByModuleCodeAndModuleStageValue("OPPORTUNITY", stageValue);

            if (existingPlaybook.isPresent()) {
                log.debug("ModuleStagePlaybook already exists for OPPORTUNITY stage: {}", stageValue);
            } else {
                log.info("Creating ModuleStagePlaybook for OPPORTUNITY stage: {}", stageValue);

                ModuleStagePlaybook newPlaybook = new ModuleStagePlaybook();
                newPlaybook.setModuleCode("OPPORTUNITY");
                newPlaybook.setModuleStageValue(stageValue);
                newPlaybook.setGuideForSuccessContent(""); // Default empty content
                // Note: createdBy and lastModifiedBy will be null for system-initialized records
                // This is acceptable for initialization data

                ModuleStagePlaybook savedPlaybook = moduleStagePlaybookRepository.save(newPlaybook);

                log.info("Successfully created ModuleStagePlaybook for OPPORTUNITY stage '{}' with ID: {}",
                        stageValue, savedPlaybook.getId());
            }
        }
    }
}
