package com.tti.oh_crm_service.client;

import java.util.List;
import java.util.Map;

import com.tti.oh_crm_service.entity.CreateIdAccountRemoteRequest;
import com.tti.oh_crm_service.entity.IdAccountRemoteView;
import org.springframework.cloud.openfeign.FeignClient;

import org.springframework.web.bind.annotation.*;

import com.tti.oh_crm_service.config.FeignClientConfig;
import com.tti.oh_crm_service.entity.OrganizationView;
import com.tti.oh_crm_service.entity.Response;

import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;


@ReactiveFeignClient(name = "id-service", configuration = FeignClientConfig.class)
public interface IdAccountFeignClient {
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/account/organizations/all",
        consumes = "application/json"
    )
    Mono<Response<List<OrganizationView>>> getOrganizations();
    
    // check account via email from tti-id-service
    // /account/check_via_email?email=
    // response is accountId/null
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/account/check_via_email",
        consumes = "application/json"
    )
    Mono<Response<Long>> checkAccountViaEmail(
            @RequestHeader Map<String, String> header,
            @RequestParam(name = "email", required = true) String email
    );
    
    // create id account from tti-id-service
    // /account/create with CreateIdAccountRemoteRequest
    @RequestMapping(
        method = RequestMethod.POST,
        value = "/account/create",
        consumes = "application/json"
    )
    Mono<Response<IdAccountRemoteView>> createAccount(
            @RequestHeader Map<String, String> header,
            @RequestBody CreateIdAccountRemoteRequest request);
}
