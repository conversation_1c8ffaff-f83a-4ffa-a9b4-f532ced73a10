package com.tti.oh_crm_service.client;

import java.util.List;
import java.util.Map;

import com.tti.oh_crm_service.entity.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import com.tti.oh_crm_service.config.FeignClientConfig;

import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;


@ReactiveFeignClient(name = "common-service", configuration = FeignClientConfig.class)
//@FeignClient(name = "common-service", configuration = FeignClientConfig.class)
public interface CommonFeignClient {
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/countryByCode",
        consumes = "application/json"
    )
    Mono<Response<CountryView>> getCountryByCode(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "code", required = true) String code
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/countryByName",
        consumes = "application/json"
    )
    Mono<Response<CountryView>> getCountryByName(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "name", required = true) String name
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/provinceByCode",
        consumes = "application/json"
    )
    Mono<Response<ProvinceView>> getProvinceByCode(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "code", required = true) String code
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/provinceByName",
        consumes = "application/json"
    )
    Mono<Response<ProvinceView>> getProvinceByName(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "name", required = true) String name
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/districtByCode",
        consumes = "application/json"
    )
    Mono<Response<DistrictView>> getDistrictByCode(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "code", required = true) String code
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/districtByName",
        consumes = "application/json"
    )
    Mono<Response<DistrictView>> getDistrictByName(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "name", required = true) String name
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/wardByCode",
        consumes = "application/json"
    )
    Mono<Response<WardView>> getWardByCode(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "code", required = true) String code
    );

    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/wardByName",
        consumes = "application/json"
    )
    Mono<Response<WardView>> getWardByName(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "name", required = true) String name
    );
    
    @RequestMapping(
        method = RequestMethod.GET,
        value = "/common/address/commonAddressByNames",
        consumes = "application/json"
    )
    Mono<Response<CommonAddressView>> getCommonAddressByNames(
        @RequestHeader Map<String, String> header,
        @RequestParam(name = "countryName", required = true) String countryName,
        @RequestParam(name = "provinceName", required = false) String provinceName,
        @RequestParam(name = "districtName", required = false) String districtName,
        @RequestParam(name = "wardName", required = false) String wardName
    );
}
