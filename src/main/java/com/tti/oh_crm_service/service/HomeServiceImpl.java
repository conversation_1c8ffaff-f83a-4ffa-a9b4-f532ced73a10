package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.Constants;
import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.entity.CreateFilterRequest;
import com.tti.oh_crm_service.entity.CreateTagRequest;
import com.tti.oh_crm_service.entity.FilterView;
import com.tti.oh_crm_service.entity.HomeView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.TagView;
import com.tti.oh_crm_service.entity.UpdateFilterRequest;
import com.tti.oh_crm_service.enumeration.EGender;
import com.tti.oh_crm_service.enumeration.EListType;
import com.tti.oh_crm_service.mapper.FilterMapper;
import com.tti.oh_crm_service.mapper.ModuleMapper;
import com.tti.oh_crm_service.mapper.TagMapper;
import com.tti.oh_crm_service.mapper.UserMapper;
import com.tti.oh_crm_service.model.Filter;
import com.tti.oh_crm_service.model.Module;
import com.tti.oh_crm_service.model.Profile;
import com.tti.oh_crm_service.model.Role;
import com.tti.oh_crm_service.model.Tag;
import com.tti.oh_crm_service.model.TblList;
import com.tti.oh_crm_service.model.TblListValue;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.FilterRepository;
import com.tti.oh_crm_service.repository.ModuleRepository;
import com.tti.oh_crm_service.repository.ProfileRepository;
import com.tti.oh_crm_service.repository.RoleRepository;
import com.tti.oh_crm_service.repository.TagRepository;
import com.tti.oh_crm_service.repository.TblListRepository;
import com.tti.oh_crm_service.repository.TblListValueRepository;
import com.tti.oh_crm_service.repository.UserRepository;

import io.jsonwebtoken.Claims;

@Service("homeService")
public class HomeServiceImpl implements HomeService {

    @Autowired
    private ModuleRepository moduleRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProfileRepository profileRepository;
    @Autowired
    private TblListRepository tblListRepository;
    @Autowired
    private TblListValueRepository tblListValueRepository;
    @Autowired
    private TagRepository tagRepository;
    @Autowired
    private FilterRepository filterRepository;

    @Autowired
    private ModuleMapper moduleMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private TagMapper tagMapper;
    @Autowired
    private FilterMapper filterMapper;

    @Override
    public Response<HomeView> iniOrg(Claims data, Long orgId) {
        // Check account ID is existed
        Boolean isAccountExisted = userRepository.existsByAccountId(Long.valueOf(data.get("accountId").toString()));
        if (isAccountExisted) {
            return new Response<>(StatusCode.BAD_REQUEST, "Organization is initialized!!!");
        }

        System.out.println("-- Init Organization <" + orgId + "> --");

        // Create default ROLES: CEO
        Role ceoRole = new Role();
        ceoRole.setName("CEO");
        ceoRole.setDescription("Chief Executive Officer role");
        ceoRole = roleRepository.save(ceoRole);
        
        System.out.println("1. Created role!!!");

        // Create default PROFILE: SUPER ADMIN
        Profile superAdminProfile = new Profile();
        superAdminProfile.setName("Super Admin");
        superAdminProfile.setDescription("Super Admin profile");
        superAdminProfile = profileRepository.save(superAdminProfile);

        System.out.println("2. Created profile!!!");

        // Create SUPER ADMIN user for account
        User superAdminUser = new User();
        superAdminUser.setAccountId(Long.valueOf(data.get("accountId").toString()));
        superAdminUser.setRole(ceoRole);
        superAdminUser.setProfile(superAdminProfile);
        superAdminUser.setCountry(data.get("countryCode").toString());
        superAdminUser.setEmail(data.get("email").toString());
        superAdminUser.setFirstName(data.get("fullName").toString());
        superAdminUser.setLastName(data.get("displayName").toString());
        if (data.get("gender") != null) {
            superAdminUser.setGender(EGender.valueOf(data.get("gender").toString()));
        }
        superAdminUser.setPhone(data.get("phone").toString());
        superAdminUser = userRepository.save(superAdminUser);

        System.out.println("3. Created user!!!");

        // Create default MODULES
        List<Module> modules = new ArrayList<>();

        Module module = new Module();
        module.setCode("HOME");
        module.setName("Home");
        module.setDisplayName("Home");
        module.setDescription("Home");
        module.setLanguage("en");
        module.setActive(true);
        module.setPosition(0);
        module = moduleRepository.save(module);
        modules.add(module);

        module = new Module();
        module.setCode("HOME");
        module.setName("Bàn làm việc");
        module.setDisplayName("Bàn làm việc");
        module.setDescription("Bàn làm việc");
        module.setLanguage("vi");
        module.setActive(true);
        module.setPosition(0);
        module = moduleRepository.save(module);
        modules.add(module);

        module = new Module();
        module.setCode("LEAD");
        module.setName("Leads");
        module.setDisplayName("Leads");
        module.setDescription("Leads");
        module.setLanguage("en");
        module.setActive(true);
        module.setPosition(1);
        module = moduleRepository.save(module);
        modules.add(module);

        module = new Module();
        module.setCode("LEAD");
        module.setName("Tiềm năng");
        module.setDisplayName("Tiềm năng");
        module.setDescription("Tiềm năng");
        module.setLanguage("vi");
        module.setActive(true);
        module.setPosition(1);
        module = moduleRepository.save(module);
        modules.add(module);

        module = new Module();
        module.setCode("PRODUCT");
        module.setName("Products");
        module.setDisplayName("Products");
        module.setDescription("Products");
        module.setLanguage("en");
        module.setActive(true);
        module.setPosition(2);
        module = moduleRepository.save(module);
        modules.add(module);

        module = new Module();
        module.setCode("PRODUCT");
        module.setName("Sản phẩm");
        module.setDisplayName("Sản phẩm");
        module.setDescription("Sản phẩm");
        module.setLanguage("vi");
        module.setActive(true);
        module.setPosition(2);
        module = moduleRepository.save(module);
        modules.add(module);

        System.out.println("4. Created modules!!!");

        // Create list types
        TblList tblList = new TblList();
        tblList.setType(EListType.COMMON);
        tblList.setCode("LST_COMMON_DAY_OF_WEEK");
        tblList.setName("Ngày trong tuần");
        tblList.setLanguage("vi");
        tblListRepository.save(tblList);

        tblList = new TblList();
        tblList.setType(EListType.COMMON);
        tblList.setCode("LST_COMMON_DAY_OF_WEEK");
        tblList.setName("Days of week");
        tblList.setLanguage("en");
        tblListRepository.save(tblList);

        tblList = new TblList();
        tblList.setType(EListType.COMMON);
        tblList.setCode("LST_COMMON_MONTH_OF_YEAR");
        tblList.setName("Tháng trong năm");
        tblList.setLanguage("vi");
        tblListRepository.save(tblList);

        tblList = new TblList();
        tblList.setType(EListType.COMMON);
        tblList.setCode("LST_COMMON_MONTH_OF_YEAR");
        tblList.setName("Months of year");
        tblList.setLanguage("en");
        tblListRepository.save(tblList);

        TblListValue tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("monday");
        tblListValue.setLabel("Thứ 2");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("monday");
        tblListValue.setLabel("Monday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("tuesday");
        tblListValue.setLabel("Thứ 3");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("tuesday");
        tblListValue.setLabel("Tuesday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("wednesday");
        tblListValue.setLabel("Thứ 4");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("wednesday");
        tblListValue.setLabel("Wednesday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("thursday");
        tblListValue.setLabel("Thứ 5");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("thursday");
        tblListValue.setLabel("Thursday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("friday");
        tblListValue.setLabel("Thứ 6");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("friday");
        tblListValue.setLabel("Friday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("saturday");
        tblListValue.setLabel("Thứ 7");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("saturday");
        tblListValue.setLabel("Saturday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("sunday");
        tblListValue.setLabel("Chủ Nhật");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_DAY_OF_WEEK");
        tblListValue.setValue("sunday");
        tblListValue.setLabel("Sunday");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        // Month of year
        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_1");
        tblListValue.setLabel("Month 1");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_1");
        tblListValue.setLabel("Tháng 1");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_2");
        tblListValue.setLabel("Month 2");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_2");
        tblListValue.setLabel("Tháng 2");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_3");
        tblListValue.setLabel("Month 3");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_3");
        tblListValue.setLabel("Tháng 3");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_4");
        tblListValue.setLabel("Month 4");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_4");
        tblListValue.setLabel("Tháng 4");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_5");
        tblListValue.setLabel("Month 5");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_5");
        tblListValue.setLabel("Tháng 5");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_6");
        tblListValue.setLabel("Month 6");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_6");
        tblListValue.setLabel("Tháng 6");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_7");
        tblListValue.setLabel("Month 7");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_7");
        tblListValue.setLabel("Tháng 7");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_8");
        tblListValue.setLabel("Month 8");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_8");
        tblListValue.setLabel("Tháng 8");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_9");
        tblListValue.setLabel("Month 9");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_9");
        tblListValue.setLabel("Tháng 9");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_10");
        tblListValue.setLabel("Month 10");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_10");
        tblListValue.setLabel("Tháng 10");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_11");
        tblListValue.setLabel("Month 11");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_11");
        tblListValue.setLabel("Tháng 11");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_12");
        tblListValue.setLabel("Month 12");
        tblListValue.setLanguage("en");
        tblListValueRepository.save(tblListValue);

        tblListValue = new TblListValue();
        tblListValue.setListCode("LST_COMMON_MONTH_OF_YEAR");
        tblListValue.setValue("month_12");
        tblListValue.setLabel("Tháng 12");
        tblListValue.setLanguage("vi");
        tblListValueRepository.save(tblListValue);

        HomeView homeView = new HomeView();
        homeView.setModules(moduleMapper.toModuleView(modules));
        homeView.setUser(userMapper.toUserView(superAdminUser));

        return new Response<HomeView>(StatusCode.OK, "Organization is initialized!!!", homeView);
    }
    
    @Override
    public Response<HomeView> home(Long accountId, Locale locale) {
        HomeView homeView = new HomeView();

        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isPresent()) {
            homeView.setUser(userMapper.toUserView(optUser.get()));
        }

        List<Module> modules = moduleRepository.findByLanguage(locale != null ? locale.getLanguage().toLowerCase() : Constants.DEFAULT_LANGUAGE);
        homeView.setModules(moduleMapper.toModuleView(modules));

        return new Response<HomeView>(StatusCode.OK, "Home", homeView);
    }

    @Override
    public Response<TagView> createTag(CreateTagRequest request, Long accountId) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<TagView>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Tag tag = new Tag();
        tagMapper.create(request, tag);
        tag.setUserId(optUser.get().getId());
        tag = tagRepository.save(tag);
        return new Response<TagView>(StatusCode.OK, "Create tag success!!!", tagMapper.toTagView(tag));
    }

    @Override
    public Response<List<TagView>> getTags(String search, Long accountId) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        List<Tag> tags = new ArrayList<>();
        if (search == null || search.trim().equalsIgnoreCase("")) {
           tags = tagRepository.findByUserId(optUser.get().getId());
        } else {
            tags = tagRepository.findByTagLikeAndUserId(search, optUser.get().getId());
        }
        
        return new Response<List<TagView>>(StatusCode.OK, "Get tags success!!!", tagMapper.toTagView(tags));
    }

    @Override
    public Response<FilterView> createFilter(CreateFilterRequest request, Long accountId) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<FilterView>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Filter filter = new Filter();
        filterMapper.create(request, filter);
        filter.setUserId(optUser.get().getId());
        filter = filterRepository.save(filter);
        return new Response<FilterView>(StatusCode.OK, "Create filter success!!!", filterMapper.toFilterView(filter));
    }

    @Override
    public Response<List<FilterView>> getFilters(String moduleCode, String search, Long accountId) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        List<Filter> filters = new ArrayList<>();
        if (search == null || search.trim().equalsIgnoreCase("")) {
            filters = filterRepository.findByModuleCodeAndUserId(moduleCode, optUser.get().getId());
        } else {
            filters = filterRepository.findByNameLikeAndModuleCodeAndUserId(search, moduleCode, optUser.get().getId());
        }
        
        return new Response<List<FilterView>>(StatusCode.OK, "Get filters success!!!", filterMapper.toFilterView(filters));
    }

    @Override
    public Response<FilterView> updateFilter(UpdateFilterRequest request, Long accountId) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<FilterView>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Optional<Filter> optFilter = filterRepository.findById(request.getId());
        if (!optFilter.isPresent()) {
            return new Response<FilterView>(StatusCode.BAD_REQUEST, "Filter not found!!!");
        }
        Filter filter = optFilter.get();
        filterMapper.update(request, filter);
        filter = filterRepository.save(filter);
        return new Response<FilterView>(StatusCode.OK, "Update filter success!!!", filterMapper.toFilterView(filter));  
    }

    @Override
    public Response<Boolean> deleteTag(Long tagId) {
        Optional<Tag> optTag = tagRepository.findById(tagId);
        if (!optTag.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Tag not found!!!", false);
        }

        tagRepository.delete(optTag.get());
        return new Response<>(StatusCode.OK, "Delete tag success!!!", true);
    }

    @Override
    public Response<Boolean> deleteFilter(Long filterId) {
        Optional<Filter> optFilter = filterRepository.findById(filterId);
        if (!optFilter.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Filter not found!!!", false);
        }

        filterRepository.delete(optFilter.get());
        return new Response<>(StatusCode.OK, "Delete filter success!!!", true);
    }
    
}
