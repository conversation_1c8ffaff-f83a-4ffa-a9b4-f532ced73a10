package com.tti.oh_crm_service.service;

import java.util.List;

import com.tti.oh_crm_service.entity.OrganizationLevelLabelView;
import com.tti.oh_crm_service.entity.Response;

public interface OrganizationLevelLabelService {
    
    List<OrganizationLevelLabelView> getOrganizationLevelLabels(String language);
    
    OrganizationLevelLabelView getOrganizationLevelLabel(Integer level, String language);
    
    OrganizationLevelLabelView createOrganizationLevelLabel(Integer level, String language, String label);
    
    OrganizationLevelLabelView updateOrganizationLevelLabel(Integer level, String language, String label);
    
    String deleteOrganizationLevelLabel(Integer level, String language);
}
