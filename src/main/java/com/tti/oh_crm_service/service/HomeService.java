package com.tti.oh_crm_service.service;

import java.util.List;
import java.util.Locale;

import com.tti.oh_crm_service.entity.CreateFilterRequest;
import com.tti.oh_crm_service.entity.CreateTagRequest;
import com.tti.oh_crm_service.entity.FilterView;
import com.tti.oh_crm_service.entity.HomeView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.TagView;
import com.tti.oh_crm_service.entity.UpdateFilterRequest;

import io.jsonwebtoken.Claims;

public interface HomeService {
    public Response<HomeView> iniOrg(Claims data, Long orgId);
    public Response<HomeView> home(Long accountId, Locale locale);
    public Response<TagView> createTag(CreateTagRequest request, Long accountId);
    public Response<Boolean> deleteTag(Long tagId);
    public Response<List<TagView>> getTags(String search, Long accountId);
    public Response<FilterView> createFilter(CreateFilterRequest request, Long accountId);
    public Response<FilterView> updateFilter(UpdateFilterRequest request, Long accountId);
    public Response<Boolean> deleteFilter(Long filterId);
    public Response<List<FilterView>> getFilters(String moduleCode, String search, Long accountId);
}