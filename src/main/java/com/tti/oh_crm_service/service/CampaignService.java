package com.tti.oh_crm_service.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Campaign;

public interface CampaignService {
    public Response<CampaignTableResponse> getCampaignsTable(
        String filters, 
        String groupBy, 
        String groupValue, 
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit, 
        Long accountId, 
        String language
    );
    public Response<CampaignView> createCampaign(CreateCampaignRequest request, Long accountId, String language);
    public Response<CampaignView> updateCampaign(UpdateCampaignRequest request, Long accountId, String language);
    public Response<Integer> deleteCampaigns(List<Long> ids);
    public Response<CampaignDetails> getCampaignDetails(Long campaignId, Long accountId, String language);
    public Response<CampaignKanbanResponse> getCampaignsKanban(
        String filters, 
        String groupBy, 
        String search, 
        Long accountId, 
        String language
    );
    public Response<CampaignSplitViewResponse> getCampaignsSplitView(
        String filters, 
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, int limit, 
        Long accountId, 
        String language
    );
    public Response<Boolean> updateKanbanCampaign(
        Long campaignId, 
        String fieldName, 
        String fieldValue, 
        Long accountId, 
        String language
    );

    public Response<Page<CampaignLeadView>> getLeads(
        String filters,
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit, 
        Long accountId, 
        String language
    );

    public Response<Page<CampaignContactView>> getContacts(
        String filters,
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit, 
        Long accountId, 
        String language
    );

    public Response<Boolean> addLeadToCampaign(
        CreateCampaignLeadRequest request, 
        Long accountId, 
        String language
    );

    public Response<Boolean> addContactToCampaign(
        CreateCampaignContactRequest request, 
        Long accountId, 
        String language
    );

    public Response<CampaignRelated> getCampaignRelated(Long campaignId, Long accountId, String language);
    public Campaign findById(Long id);
    public CampaignShortView getCampaignShortView(Long campaignId);
    public Response<Page<CampaignLeadMemberView>> getLeadsOfCampaign(
        Long campaignId,
        int page, 
        int limit,
        Long accountId, 
        String language
    );
    public Response<Boolean> updateLeadMemberStatus(
        UpdateCampaignLeadRequest request, 
        Long accountId, 
        String language
    );
}
