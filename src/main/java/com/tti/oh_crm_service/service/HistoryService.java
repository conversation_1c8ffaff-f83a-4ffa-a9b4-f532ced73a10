package com.tti.oh_crm_service.service;

import org.springframework.data.domain.Page;

import com.tti.oh_crm_service.entity.CreateHistoryRequest;
import com.tti.oh_crm_service.entity.HistoryView;
import com.tti.oh_crm_service.entity.HistoryViewWithFieldLabel;
import com.tti.oh_crm_service.entity.Response;

public interface HistoryService {
    public Response<HistoryViewWithFieldLabel> getHistoryDetail(Long id, String language);
    public Response<Page<HistoryViewWithFieldLabel>> getHistoriesByRelated(String relatedModule, Long relatedId, String sortBy, String sortDirection, int page, int limit, String language);
    public Response<HistoryView> createHistory(CreateHistoryRequest createHistoryRequest, Long accountId);
}
