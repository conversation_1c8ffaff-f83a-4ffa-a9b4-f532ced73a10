package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.CampaignContactView;
import com.tti.oh_crm_service.entity.CampaignDetails;
import com.tti.oh_crm_service.entity.CampaignKanbanResponse;
import com.tti.oh_crm_service.entity.CampaignKanbanView;
import com.tti.oh_crm_service.entity.CampaignLeadMemberView;
import com.tti.oh_crm_service.entity.CampaignLeadView;
import com.tti.oh_crm_service.entity.CampaignMemberView;
import com.tti.oh_crm_service.entity.CampaignRelated;
import com.tti.oh_crm_service.entity.CampaignShortView;
import com.tti.oh_crm_service.entity.ICampaignShortView;
import com.tti.oh_crm_service.entity.LookUpView;
import com.tti.oh_crm_service.entity.CampaignSplitView;
import com.tti.oh_crm_service.entity.CampaignSplitViewResponse;
import com.tti.oh_crm_service.entity.CampaignTableResponse;
import com.tti.oh_crm_service.entity.CampaignView;
import com.tti.oh_crm_service.entity.CreateCampaignContactRequest;
import com.tti.oh_crm_service.entity.CreateCampaignLeadRequest;
import com.tti.oh_crm_service.entity.CreateCampaignRequest;
import com.tti.oh_crm_service.entity.GenericTableResponse;
import com.tti.oh_crm_service.entity.GroupItem;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateCampaignLeadRequest;
import com.tti.oh_crm_service.entity.UpdateCampaignRequest;
import com.tti.oh_crm_service.enumeration.EMemberType;
import com.tti.oh_crm_service.mapper.CampaignMapper;
import com.tti.oh_crm_service.mapper.ContactMapper;
import com.tti.oh_crm_service.mapper.LeadMapper;
import com.tti.oh_crm_service.model.Campaign;
import com.tti.oh_crm_service.model.CampaignContact;
import com.tti.oh_crm_service.model.CampaignLead;
import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.LayoutSettings;
import com.tti.oh_crm_service.model.Lead;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.CampaignContactRepository;
import com.tti.oh_crm_service.repository.CampaignLeadRepository;
import com.tti.oh_crm_service.repository.CampaignRepository;
import com.tti.oh_crm_service.repository.ContactRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import com.tti.oh_crm_service.repository.LeadRepository;
// import com.tti.oh_crm_service.repository.TblListValueRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service("campaignService")
public class CampaignServiceImpl implements CampaignService {

    @Autowired
    private CampaignMapper campaignMapper;
    @Autowired
    private LeadMapper leadMapper;
    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private CampaignRepository campaignRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;
    // @Autowired
    // private TblListValueRepository tblListValueRepository;
    @Autowired
    private LeadRepository leadRepository;
    @Autowired
    private CampaignLeadRepository campaignLeadRepository;
    @Autowired
    private CampaignContactRepository campaignContactRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private CoreQueryService<Campaign> coreQueryService;

    @Override
    public Response<CampaignTableResponse> getCampaignsTable(
        String filters, 
        String groupBy, 
        String groupValue,
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit, 
        Long accountId, 
        String language
    ) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "name",
                "description"
        );

        GenericTableResponse<Campaign> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "campaigns", searchFields, "CAMPAIGN",
                campaignRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }
        
        Page<Campaign> campaignsPage = tableResponse.getData();
        List<CampaignView> campaignViews = campaignMapper.toCampaignView(campaignsPage.getContent());
        Page<CampaignView> campaignViewPage = new PageImpl<>(campaignViews, campaignsPage.getPageable(), campaignsPage.getTotalElements());

        CampaignTableResponse campaignTableResponse = new CampaignTableResponse();
        campaignTableResponse.setGroupItems(tableResponse.getGroupItems());
        campaignTableResponse.setCampaigns(campaignViewPage);
        return new Response<CampaignTableResponse>(StatusCode.OK, "Get campaigns table success!!!", campaignTableResponse);
    }

    @Override
    @Transactional
    public Response<CampaignView> createCampaign(CreateCampaignRequest request, Long accountId, String language) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Campaign CREATE");

        Campaign newCampaign = new Campaign();
        campaignMapper.create(request, newCampaign);

        // Get "owner"
        if (request.getOwnerId() == null) {
            return new Response<CampaignView>(StatusCode.BAD_REQUEST, "Owner is required!!!");
        }

        Optional<User> optOwner = userRepository.findById(request.getOwnerId());
        if (optOwner.isPresent()) {
            newCampaign.setOwner(optOwner.get());
        } else {
            return new Response<CampaignView>(StatusCode.BAD_REQUEST, "Owner not found!!!");
        }

        // Get "campaign_parent_id" campaign
        if (request.getCampaignParentId() != null) {
            Optional<Campaign> optCampaign = campaignRepository.findById(request.getCampaignParentId());
            if (optCampaign.isPresent()) {
                newCampaign.setCampaignParent(optCampaign.get());
            }
        }

        newCampaign.setCreatedBy(optUser.get());
        newCampaign.setLastModifiedBy(optUser.get());

        // Save campaign - HistoryEntityListener will automatically handle CREATE history
        newCampaign = campaignRepository.save(newCampaign);
        log.info("Campaign saved, transaction will commit soon");

        CampaignView campaignView = campaignMapper.toCampaignView(newCampaign);
        return new Response<CampaignView>(StatusCode.OK, "Create campaign success!!!", campaignView);
    }

    @Override
    @Transactional
    public Response<CampaignView> updateCampaign(UpdateCampaignRequest request, Long accountId, String language) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Optional<Campaign> optCampaign = campaignRepository.findById(request.getId());
        if (optCampaign.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Campaign not found!!!");
        }

        Campaign campaign = optCampaign.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());


        // Step 1: Clone the original entity for history tracking before any modifications
        Campaign originalCampaign = EntityCloneUtils.cloneEntityForHistory(campaign);
        EntityContext.setOriginalEntity(originalCampaign);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Campaign UPDATE");

        // Step 2: Apply updates to the entity
        campaignMapper.update(request, campaign);

        // Handle relationship updates
        updateCampaignRelationships(request, campaign);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(campaign);

        campaign.setLastModifiedBy(optUser.get());

        // Save campaign - HistoryEntityListener will automatically handle UPDATE history with field changes
        campaign = campaignRepository.save(campaign);

        CampaignView campaignView = campaignMapper.toCampaignView(campaign);
        return new Response<>(StatusCode.OK, "Update campaign success!!!", campaignView);
    }

    @Override
    @Transactional
    public Response<Integer> deleteCampaigns(List<Long> ids) {
        // Check lead exist
        for (Long id : ids) {
            Optional<Campaign> optCampaign = campaignRepository.findById(id);
            if (!optCampaign.isPresent()) {
                return new Response<Integer>(StatusCode.BAD_REQUEST, "Campaign not found!!!");
            }
        }
        Integer result = campaignRepository.deleteByIdIn(ids);
        return new Response<Integer>(StatusCode.OK, "Delete campaigns success!!!", result);
    }

    @Override
    public Response<CampaignDetails> getCampaignDetails(Long campaignId, Long accountId, String language) {
        Optional<Campaign> optCampaign = campaignRepository.findById(campaignId);
        if (optCampaign.isPresent()) {
            Campaign campaign = optCampaign.get();
            CampaignDetails campaignDetails = campaignMapper.toCampaignDetails(campaign);
            LookUpView owner = new LookUpView();
            owner.setId(campaign.getOwner().getId());
            owner.setName(campaign.getOwner().getFirstName() + " " + campaign.getOwner().getLastName());
            campaignDetails.setOwner(owner);
            return new Response<CampaignDetails>(StatusCode.OK, "Get campaign details success!!!", campaignDetails);
        } else {
            return new Response<CampaignDetails>(StatusCode.BAD_REQUEST, "Campaign not found!!!");
        }
    }

    @Override
    public Response<CampaignKanbanResponse> getCampaignsKanban(String filters, String groupBy, String search,
            Long accountId, String language) {
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("name");
        searchFields.put("description");

        // Get campaigns in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "campaigns");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("name");
        viewFields.put("active");
        viewFields.put("status");
        viewFields.put("campaign_parent_id");
        viewFields.put("type");
        jsonFilter.put("view_fields", viewFields);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        Long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("CAMPAIGN");
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }
                List<Object[]> groupValues = campaignRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        List<Long> countByFilter = campaignRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
       
        System.out.println(jsonFilter.toString());
        
        List<CampaignKanbanView> campaigns = new ArrayList<>();
        try {
            List<Object[]> campaignObjects = campaignRepository.findKanban(jsonFilter.toString());
            for (Object[] campaignObject : campaignObjects) {
                CampaignKanbanView campaignKanbanView = new CampaignKanbanView();
                campaignKanbanView.setId((Long) campaignObject[0]);
                campaignKanbanView.setName((String) campaignObject[1]);
                campaignKanbanView.setActive((Boolean) campaignObject[2]);
                campaignKanbanView.setStatus((String) campaignObject[3]);
                Long campaignParentId = (Long) campaignObject[4];
                List<ICampaignShortView> parents = campaignRepository.getCampaignById(campaignParentId);
                if (parents.size() > 0) {
                    campaignKanbanView.setCampaignParent((CampaignShortView) parents.get(0));
                }
                campaignKanbanView.setType((String) campaignObject[5]);
                campaignKanbanView.setGroupField((String) campaignObject[6]);
                campaigns.add(campaignKanbanView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<CampaignKanbanResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        CampaignKanbanResponse campaignKanbanResponse = new CampaignKanbanResponse();
        campaignKanbanResponse.setGroupItems(groupItems);
        campaignKanbanResponse.setCampaigns(campaigns);
        return new Response<CampaignKanbanResponse>(StatusCode.OK, "Get leads kanban success!!!", campaignKanbanResponse);
    }

    @Override
    public Response<CampaignSplitViewResponse> getCampaignsSplitView(String filters, String search, String sortBy,
            String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("name");
        searchFields.put("description");

        // Get leads in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "campaigns");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("name");
        viewFields.put("active");
        viewFields.put("status");
        viewFields.put("campaign_parent_id");
        viewFields.put("type");
        jsonFilter.put("view_fields", viewFields);
       
        System.out.println(jsonFilter.toString());
        
        List<CampaignSplitView> leads = new ArrayList<>();
        try {
            List<Object[]> campaignObjects = campaignRepository.findSplitViewData(jsonFilter.toString());
            for (Object[] campaignObject : campaignObjects) {
                CampaignSplitView campaignSplitView = new CampaignSplitView();
                campaignSplitView.setId((Long) campaignObject[0]);
                campaignSplitView.setName((String) campaignObject[1]);
                campaignSplitView.setActive((Boolean) campaignObject[2]);
                campaignSplitView.setStatus((String) campaignObject[3]);
                Long campaignParentId = (Long) campaignObject[4];
                List<ICampaignShortView> parents = campaignRepository.getCampaignById(campaignParentId);
                if (parents.size() > 0) {
                    campaignSplitView.setCampaignParent((CampaignShortView) parents.get(0));
                }
                campaignSplitView.setType((String) campaignObject[5]);
                leads.add(campaignSplitView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<CampaignSplitViewResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        // Get Total rows
        List<Long> countByFilter = campaignRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        Page<CampaignSplitView> campaignsPage = new PageImpl<>(leads, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        CampaignSplitViewResponse campaignSplitViewResponse = new CampaignSplitViewResponse();
        campaignSplitViewResponse.setCampaigns(campaignsPage);
        return new Response<CampaignSplitViewResponse>(StatusCode.OK, "Get campaigns split view success!!!", campaignSplitViewResponse);
    }

    @Override
    public Response<Boolean> updateKanbanCampaign(Long campaignId, String fieldName, String fieldValue, Long accountId,
            String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "campaigns");
        jsonFilter.put("id", campaignId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);
        
        campaignRepository.updateKanbanData(jsonFilter.toString());

        return new Response<Boolean>(StatusCode.OK, "Update campaign kanban success!!!", true);
    }

    @Override
    public Response<Page<CampaignLeadView>> getLeads(String filters, String sortBy, String sortDirection,
            int page, int limit, Long accountId, String language) {

        // Get leads
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "leads");

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);
       
        System.out.println(jsonFilter.toString());
        
        List<Lead> leads = new ArrayList<>();
        try {
            leads = leadRepository.findByFilter(jsonFilter.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<Long> countByFilter = leadRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        List<CampaignLeadView> campaignLeadViews = leadMapper.toCampaignLeadView(leads);
        Page<CampaignLeadView> campaignLeadsPage = new PageImpl<>(campaignLeadViews, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        return new Response<>(StatusCode.OK, "Get leads table success!!!", campaignLeadsPage);
    }

    @Override
    public Response<Page<CampaignContactView>> getContacts(String filters, String sortBy,
            String sortDirection, int page, int limit, Long accountId, String language) {
        // Create JSON filter object
        JSONObject jsonFilter = new JSONObject();

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        jsonFilter.put("table_name", "contacts");
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        List<Long> countByFilter = contactRepository.countByFilter(jsonFilter.toString());
        long totalRowsByGroup = !countByFilter.isEmpty() ? (Long) countByFilter.get(0) : 0L;

        List<Contact> contacts = new ArrayList<>();
        try {
            contacts = contactRepository.findByFilter(jsonFilter.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<CampaignContactView> campaignContactViews = contactMapper.toCampaignContactView(contacts);
        Page<CampaignContactView> campaignContactsPage = new PageImpl<>(campaignContactViews, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        return new Response<Page<CampaignContactView>>(StatusCode.OK, "Get contacts table success!!!", campaignContactsPage);
    }

    @Override
    @Transactional
    public Response<Boolean> addLeadToCampaign(CreateCampaignLeadRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        for (Long leadId : request.getLeadIds()) {
            Optional<CampaignLead> optCampaignLead = campaignLeadRepository.findByCampaignIdAndLeadId(request.getCampaignId(), leadId);
            if (optCampaignLead.isPresent()) { // Update member status
                if (request.getIsForceUpdateStatus()) {
                    optCampaignLead.get().setMemberStatus(request.getMemberStatus());
                    if (optUser.isPresent()) {
                        optCampaignLead.get().setLastModifiedBy(optUser.get());
                    }
                    campaignLeadRepository.save(optCampaignLead.get());
                }
            } else {
                CampaignLead campaignLead = new CampaignLead();
                campaignLead.setCampaignId(request.getCampaignId());
                campaignLead.setLeadId(leadId);
                campaignLead.setMemberStatus(request.getMemberStatus());
                if (optUser.isPresent()) {
                    campaignLead.setCreatedBy(optUser.get());
                    campaignLead.setLastModifiedBy(optUser.get());
                }
                campaignLeadRepository.save(campaignLead);
            }
        }

        return new Response<Boolean>(StatusCode.OK, "Add leads to campaign success!!!", true);
    }

    @Override
    @Transactional
    public Response<Boolean> addContactToCampaign(CreateCampaignContactRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        for (Long contactId : request.getContactIds()) {
            Optional<CampaignContact> optCampaignContact = campaignContactRepository.findByCampaignIdAndContactId(request.getCampaignId(), contactId);
            if (optCampaignContact.isPresent()) { // Update member status
                if (request.getIsForceUpdateStatus()) {
                    optCampaignContact.get().setMemberStatus(request.getMemberStatus());
                    if (optUser.isPresent()) {
                        optCampaignContact.get().setLastModifiedBy(optUser.get());
                    }
                    campaignContactRepository.save(optCampaignContact.get());
                }
            } else {
                CampaignContact campaignContact = new CampaignContact();
                campaignContact.setCampaignId(request.getCampaignId());
                campaignContact.setContactId(contactId);
                campaignContact.setMemberStatus(request.getMemberStatus());
                if (optUser.isPresent()) {
                    campaignContact.setCreatedBy(optUser.get());
                    campaignContact.setLastModifiedBy(optUser.get());
                }
                campaignContactRepository.save(campaignContact);
            }
        }

        return new Response<Boolean>(StatusCode.OK, "Add contacts to campaign success!!!", true);
    }

    @Override
    public Response<CampaignRelated> getCampaignRelated(Long campaignId, Long accountId, String language) {
        CampaignRelated campaignRelatedView = new CampaignRelated();
        List<CampaignMemberView> campaignMemberViews = new ArrayList<>();
        //Get Lead member
        List<CampaignLead> campaignLeads = campaignLeadRepository.findByCampaignId(campaignId);
        for (CampaignLead campaignLead : campaignLeads) {
            Optional<Lead> optLead = leadRepository.findById(campaignLead.getLeadId());
            if (!optLead.isPresent()) {
                continue;
            }
            CampaignMemberView campaignMemberView = leadMapper.toCampaignMemberView(optLead.get());
            campaignMemberView.setType(EMemberType.LEAD);
            campaignMemberView.setMemberStatus(campaignLead.getMemberStatus());
            campaignMemberViews.add(campaignMemberView);
        }

        //Get Contact member
        List<CampaignContact> campaignContacts = campaignContactRepository.findByCampaignId(campaignId);
        for (CampaignContact campaignContact : campaignContacts) {
            Optional<Contact> optContact = contactRepository.findById(campaignContact.getContactId());
            if (!optContact.isPresent()) {
                continue;
            }
            CampaignMemberView campaignMemberView = leadMapper.toCampaignMemberView(optContact.get());
            campaignMemberView.setType(EMemberType.CONTACT);
            campaignMemberView.setMemberStatus(campaignContact.getMemberStatus());
            campaignMemberViews.add(campaignMemberView);
        }
        campaignRelatedView.setMembers(campaignMemberViews);
        return new Response<CampaignRelated>(StatusCode.OK, "Get campaign related success!!", campaignRelatedView);
    }

    @Override
    public Campaign findById(Long id) {
        Optional<Campaign> optCampaign = campaignRepository.findById(id);
        return optCampaign.orElse(null);
    }

    @Override
    public CampaignShortView getCampaignShortView(Long campaignId) {
        Optional<Campaign> optCampaign = campaignRepository.findById(campaignId);
        return optCampaign.map(campaign -> new CampaignShortView(campaign.getId(), campaign.getName())).orElse(null);
    }

    @Override
    public Response<Page<CampaignLeadMemberView>> getLeadsOfCampaign(
        Long campaignId, 
        int page,
        int limit,
        Long accountId, 
        String language
    ) {
        //Get Lead members
        PageRequest pageRequest = PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"));
        Page<CampaignLead> campaignLeads = campaignLeadRepository.findByCampaignId(campaignId, pageRequest);
        List<CampaignLeadMemberView> campaignLeadMemberViews = new ArrayList<>();
        for (CampaignLead campaignLead : campaignLeads) {
            Optional<Lead> optLead = leadRepository.findById(campaignLead.getLeadId());
            if (!optLead.isPresent()) {
                continue;
            }
            CampaignLeadMemberView campaignLeadMemberView = leadMapper.toCampaignLeadMemberView(optLead.get());
            campaignLeadMemberView.setMemberStatus(campaignLead.getMemberStatus());
            campaignLeadMemberViews.add(campaignLeadMemberView);
        }
        Page<CampaignLeadMemberView> campaignLeadMemberViewsPage = new PageImpl<>(campaignLeadMemberViews, campaignLeads.getPageable(), campaignLeads.getTotalElements());
        return new Response<Page<CampaignLeadMemberView>>(StatusCode.OK, "Get leads of campaign success!!", campaignLeadMemberViewsPage);
    }

    @Override
    public Response<Boolean> updateLeadMemberStatus(UpdateCampaignLeadRequest request, Long accountId,
            String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<CampaignLead> optCampaignLead = campaignLeadRepository.findByCampaignIdAndLeadId(request.getCampaignId(), request.getLeadId());
        if (!optCampaignLead.isPresent()) { // Update member status
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Lead is not existed in campaign!!!", false);
        }

        optCampaignLead.get().setMemberStatus(request.getMemberStatus());
        if (optUser.isPresent()) {
            optCampaignLead.get().setLastModifiedBy(optUser.get());
        }
        campaignLeadRepository.save(optCampaignLead.get());

        return new Response<Boolean>(StatusCode.OK, "Add leads to campaign success!!!", true);
    }


    /**
     * Update campaign relationships based on the provided IDs in the request
     * Handles ownerId and campaignParentId relationship fields
     */
    private void updateCampaignRelationships(UpdateCampaignRequest request, Campaign campaign) {
        if (request.getOwnerId() != null) {
            Optional<User> optOwner = userRepository.findById(request.getOwnerId());
            if (optOwner.isPresent()) {
                campaign.setOwner(optOwner.get());
            }
        }

        if (request.getCampaignParentId() != null) {
            Optional<Campaign> optCampaignParent = campaignRepository.findById(request.getCampaignParentId());
            if (optCampaignParent.isPresent()) {
                campaign.setCampaignParent(optCampaignParent.get());
            }
        }

    }

}
