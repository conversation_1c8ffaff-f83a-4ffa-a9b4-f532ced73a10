package com.tti.oh_crm_service.service;

import java.util.List;

import org.springframework.data.domain.Page;

import com.tti.oh_crm_service.entity.CreateNoteRequest;
import com.tti.oh_crm_service.entity.NoteView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateNoteRequest;

public interface NoteService {
    public Response<NoteView> getNoteDetail(Long id);
    public Response<Page<NoteView>> getNotesByRelated(String relatedModule, Long relatedId, String sortBy, String sortDirection, int page, int limit, String language);
    public Response<NoteView> createNote(CreateNoteRequest createNoteRequest, Long accountId);
    public Response<NoteView> updateNote(UpdateNoteRequest updateNoteRequest, Long accountId);
    public Response<List<Long>> deleteNotes(List<Long>  noteIds);
}
