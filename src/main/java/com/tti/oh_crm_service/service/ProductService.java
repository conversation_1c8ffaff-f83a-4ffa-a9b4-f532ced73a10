package com.tti.oh_crm_service.service;

import java.util.List;
import java.util.Locale;

import com.tti.oh_crm_service.entity.CreateProductRequest;
import com.tti.oh_crm_service.entity.ProductKanbanResponse;
import com.tti.oh_crm_service.entity.ProductSplitViewResponse;
import com.tti.oh_crm_service.entity.ProductTableResponse;
import com.tti.oh_crm_service.entity.ProductView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateProductRequest;

import io.jsonwebtoken.Claims;

public interface ProductService {
    public Response<ProductView> getProductDetail(Claims claims, Locale locale, Long id);
    public Response<ProductTableResponse> getProducts(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<ProductView> createProduct(<PERSON>laims claims, CreateProductRequest createProductRequest, Locale locale);
    public Response<ProductView> updateProduct(Claims claims, UpdateProductRequest updateProductRequest, Locale locale);
    public Response<List<Long>> deleteProducts(List<Long> productIds);
    public Response<ProductKanbanResponse> getProductKanban(String filters,String groupBy, String search, Long accountId, String language);
    public Response<Boolean> updateKanbanProduct(Long productId, String fieldName, String fieldValue, Long accountId, String language);
    public Response<ProductSplitViewResponse> getProductsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
}
