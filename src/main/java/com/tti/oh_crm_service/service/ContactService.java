package com.tti.oh_crm_service.service;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Contact;


public interface ContactService {
    public Response<ContactTableResponse> getContactsTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<ContactView> createContact(CreateContactRequest request, Long accountId, String language);
    public Response<ContactView> updateContact(UpdateContactRequest request, Long accountId, String language);
    public Response<Integer> deleteContacts(List<Long> ids);
    public Response<ContactDetails> getContactDetails(Long contactId, Long accountId, String language);
    public Response<ContactKanbanResponse> getContactsKanban(String filters, String groupBy, String search, Long accountId, String language);
    public Response<ContactSplitViewResponse> getContactsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<Boolean> updateKanbanContact(Long contactId, String fieldName, String fieldValue, Long accountId, String language);
    public Response<GenericTableResponse<OpportunityContactRoleView>> getAttachedOpportunityForContact(Long contactId, int page, int limit, Long accountId, String language);
    public Response<GenericTableResponse<QuoteAttachedView>> getAttachedQuoteForContact(Long contactId, int page, int limit, Long accountId, String language);
    public List<ContactView> getContactsByAccountId(Long accountId);
    public Contact findById(Long id);
}