package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.entity.CreateHistoryRequest;
import com.tti.oh_crm_service.entity.HistoryView;
import com.tti.oh_crm_service.entity.HistoryViewWithFieldLabel;
import com.tti.oh_crm_service.entity.LookUpView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import com.tti.oh_crm_service.mapper.HistoryMapper;
import com.tti.oh_crm_service.model.History;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.HistoryRepository;
import com.tti.oh_crm_service.repository.UserRepository;

@Service("HistoryService")
public class HistoryServiceImpl implements HistoryService {

    @Autowired
    private HistoryMapper historyMapper;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private HistoryRepository historyRepository;
    
    @Override
    public Response<HistoryViewWithFieldLabel> getHistoryDetail(Long id, String language) {
        // Use optimized native query to get history with field labels in one database call
        List<Object[]> historyResults = historyRepository.findHistoryWithFieldLabelsById(id, language);

        if (historyResults == null || historyResults.isEmpty()) {
            return new Response<HistoryViewWithFieldLabel>(StatusCode.BAD_REQUEST, "History not found!!!");
        }

        // Get the first (and should be only) result
        Object[] historyResult = historyResults.get(0);
        HistoryViewWithFieldLabel historyWithFieldLabel = new HistoryViewWithFieldLabel();

        // Map fields from the native query result with safe casting
        historyWithFieldLabel.setId(historyResult[0] != null ? Long.valueOf(historyResult[0].toString()) : null);
        historyWithFieldLabel.setFieldName((String) historyResult[1]);
        historyWithFieldLabel.setFieldType(historyResult[2] != null ? EFieldType.valueOf((String) historyResult[2]) : null);
        historyWithFieldLabel.setFieldOriginalValue((String) historyResult[3]);
        historyWithFieldLabel.setFieldNewValue((String) historyResult[4]);
        historyWithFieldLabel.setRelatedToModule((String) historyResult[5]);
        historyWithFieldLabel.setActionType(historyResult[6] != null ? EHistoryActionType.valueOf((String) historyResult[6]) : null);
        historyWithFieldLabel.setHistoryDate((java.util.Date) historyResult[7]);

        // Map user information
        if (historyResult[8] != null) { // user_id
            LookUpView user = new LookUpView();
            user.setId(Long.valueOf(historyResult[8].toString()));
            user.setName(((String) historyResult[9]) + " " + ((String) historyResult[10])); // first_name + last_name
            historyWithFieldLabel.setUser(user);
        }

        // Set field labels (these will be null for non-LIST field types, which is what we want)
        historyWithFieldLabel.setFieldOriginalValueLabel((String) historyResult[11]);
        historyWithFieldLabel.setFieldNewValueLabel((String) historyResult[12]);

        return new Response<HistoryViewWithFieldLabel>(StatusCode.OK, "Get history detail success!!!", historyWithFieldLabel);
    }

    @Override
    public Response<Page<HistoryViewWithFieldLabel>> getHistoriesByRelated(String relatedModule, Long relatedId, String sortBy,
            String sortDirection, int page, int limit, String language
    ) {
        Sort sort = Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy);
        Pageable pageable = PageRequest.of(page, limit, sort);

        // Use optimized native query to get all data in one database call
        Page<Object[]> historyResults = historyRepository.findHistoriesWithFieldLabels(
            relatedId, relatedModule, language, sortBy, sortDirection, pageable);

        List<HistoryViewWithFieldLabel> historyWithFieldLabels = new ArrayList<>();

        for (Object[] row : historyResults.getContent()) {
            HistoryViewWithFieldLabel historyWithFieldLabel = new HistoryViewWithFieldLabel();

            // Map fields from the native query result with safe casting
            historyWithFieldLabel.setId(row[0] != null ? Long.valueOf(row[0].toString()) : null);
            historyWithFieldLabel.setFieldName((String) row[1]);
            historyWithFieldLabel.setFieldType(row[2] != null ? EFieldType.valueOf((String) row[2]) : null);
            historyWithFieldLabel.setFieldOriginalValue((String) row[3]);
            historyWithFieldLabel.setFieldNewValue((String) row[4]);
            historyWithFieldLabel.setRelatedToModule((String) row[5]);
            historyWithFieldLabel.setActionType(row[6] != null ? EHistoryActionType.valueOf((String) row[6]) : null);
            historyWithFieldLabel.setHistoryDate((java.util.Date) row[7]);

            // Map user information
            if (row[8] != null) { // user_id
                LookUpView user = new LookUpView();
                user.setId(Long.valueOf(row[8].toString()));
                user.setName(((String) row[9]) + " " + ((String) row[10])); // first_name + last_name
                historyWithFieldLabel.setUser(user);
            }

            // Set field labels (these will be null for non-LIST field types, which is what we want)
            historyWithFieldLabel.setFieldOriginalValueLabel((String) row[11]);
            historyWithFieldLabel.setFieldNewValueLabel((String) row[12]);

            historyWithFieldLabels.add(historyWithFieldLabel);
        }

        Page<HistoryViewWithFieldLabel> historyWithFieldLabelPage = new PageImpl<>(
            historyWithFieldLabels, pageable, historyResults.getTotalElements());
        return new Response<Page<HistoryViewWithFieldLabel>>(StatusCode.OK, "Get histories by related success!!!", historyWithFieldLabelPage);
    }

    @Override
    public Response<HistoryView> createHistory(CreateHistoryRequest createHistoryRequest, Long accountId) {
        Optional<User> optUser = userRepository.findById(createHistoryRequest.getUserId());
        History newHistory = new History();
        newHistory = historyMapper.create(createHistoryRequest);
        if (optUser.isPresent()) {
            newHistory.setUser(optUser.get());
        }
        newHistory = historyRepository.save(newHistory);
        return new Response<HistoryView>(StatusCode.OK, "Create history success!!!");
    }
    
}
