package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.LayoutSettings;
import com.tti.oh_crm_service.repository.CoreStoreProcedureRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class CoreQueryServiceImpl<T> implements CoreQueryService<T> {
    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;
    
    @Override
    public GenericTableResponse<T> getTable(
            String filters,
            String groupBy,
            String groupValue,
            String search,
            String sortBy,
            String sortDirection,
            int page,
            int limit,
            Long accountId,
            String language,
            String tableName,
            List<String> searchFields,
            String moduleCode,
            CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    ) {
        // Define search fields
        JSONArray searchFieldsJson = new JSONArray(searchFields);

        // Create JSON filter object
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", tableName);
        jsonFilter.put("module_code", moduleCode);
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFieldsJson);
        }
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode(moduleCode);
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            boolean isGroupByActivity = false;
            if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                jsonFilter.put("field_name", groupBy);
                isGroupByActivity = groupBy.trim().equals("GROUP_BY_ACTIVITY");
            } else {
                if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupByField);
                    isGroupByActivity = groupByField.trim().equals("GROUP_BY_ACTIVITY");
                } else {
                    jsonFilter.put("field_name", "GROUP_BY_ACTIVITY");
                    isGroupByActivity = true;
                }
            }

            // Group by ACTIVITY
            if (isGroupByActivity) {
                JSONObject dataJson = new JSONObject();
                dataJson.put("table_name", tableName);
                dataJson.put("module_code", moduleCode);
                List<Object[]> groupByActivity = coreStoreProcedureRepository.groupByActivity(dataJson.toString());
                for (Object[] obj : groupByActivity) {
                    GroupItem groupItem = new GroupItem("no_activity", (long) obj[0]);
                    groupItems.add(groupItem);
                    groupItem = new GroupItem("idle", (long) obj[1]);
                    groupItems.add(groupItem);
                    groupItem = new GroupItem("upcoming", (long) obj[2]);
                    groupItems.add(groupItem);
                    groupItem = new GroupItem("no_upcoming", (long) obj[3]);
                    groupItems.add(groupItem);
                    groupItem = new GroupItem("overdue", (long) obj[4]);
                    groupItems.add(groupItem);
                    groupItem = new GroupItem("due_today", (long) obj[5]);
                    groupItems.add(groupItem);
                }

                if (groupValue != null && !groupValue.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", "GROUP_BY_ACTIVITY");
                    jsonFilter.put("group_value", groupValue);
                }
            } else {// Group by field
                totalRows = 0L;
                List<Object[]> groupValues = coreStoreProcedureRepository.getGroupValues(jsonFilter.toString());
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupValue != null && !groupValue.trim().equalsIgnoreCase("")) {
                    if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                        jsonFilter.put("group_field", groupBy);
                    } else {
                        jsonFilter.put("group_field", groupByField);
                    }
                    
                    if (groupValue.trim().equalsIgnoreCase("UNKNOWN")) {
                        jsonFilter.put("group_value", "");
                    } else {
                        jsonFilter.put("group_value", groupValue);
                    }
                }
            }
        }

        // Count by GROUP
        List<Long> countByFilter = coreStoreProcedureRepository.countByFilter(jsonFilter.toString());
        long totalRowsByGroup = !countByFilter.isEmpty() ? (Long) countByFilter.get(0) : 0L;

        // Count ALL
        List<Long> countAll = coreStoreProcedureRepository.countAll(jsonFilter.toString());
        Long totalRowsAll = countAll.size() > 0 ? (Long) countAll.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsAll));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }

        List<T> items = new ArrayList<>();
        try {
            System.out.println(jsonFilter.toString());
            items = coreStoreProcedureRepository.findByFilter(jsonFilter.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        Page<T> itemsPage = new PageImpl<>(items, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        return new GenericTableResponse<T>(itemsPage, groupItems);
    }

    @Override
    public GenericKanbanResponse getKanban(
            String filters,
            String groupBy,
            String search,
            Long accountId,
            String language,
            String tableName,
            List<String> searchFields,
            List<String> viewFields,
            String moduleCode,
            CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    ) {
        // Define search fields
        JSONArray searchFieldsJson = new JSONArray(searchFields);

        // Create JSON filter object
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", tableName);
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFieldsJson);
        }

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFieldsJson = new JSONArray(viewFields);
        jsonFilter.put("view_fields", viewFieldsJson);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode(moduleCode);
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }

                List<Object[]> groupValues = coreStoreProcedureRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        List<Long> countByFilter = coreStoreProcedureRepository.countByFilter(jsonFilter.toString());
        long totalRowsByGroup = !countByFilter.isEmpty() ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
        System.out.println(jsonFilter.toString());
        List<Object[]> itemObjects = new ArrayList<>();
        try {
            itemObjects = coreStoreProcedureRepository.findKanban(jsonFilter.toString());
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        return new GenericKanbanResponse(groupItems, itemObjects);
    }

    @Override
    public GenericSplitViewResponse<Object[]> getSplitView(
            String filters,
            String search,
            String sortBy,
            String sortDirection,
            int page,
            int limit,
            String tableName,
            List<String> searchFields,
            List<String> viewFields,
            CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    ) {
        // Define search fields
        JSONArray searchFieldsJson = new JSONArray(searchFields);

        // Create JSON filter object
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", tableName);
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFieldsJson);
        }
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFieldsJson = new JSONArray(viewFields);
        jsonFilter.put("view_fields", viewFieldsJson);

        List<Object[]> itemObjects = new ArrayList<>();
        try {
            itemObjects = coreStoreProcedureRepository.findSplitViewData(jsonFilter.toString());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        // Get Total rows
        List<Long> countByFilter = coreStoreProcedureRepository.countByFilter(jsonFilter.toString());
        long totalRowsByGroup = !countByFilter.isEmpty() ? countByFilter.get(0) : 0L;

        Page<Object[]> itemsPage = new PageImpl<>(itemObjects,
                PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)),
                totalRowsByGroup);

        return new GenericSplitViewResponse<>(itemsPage);
    }
}
