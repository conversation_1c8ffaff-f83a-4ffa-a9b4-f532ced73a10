package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Opportunity;


import java.util.List;

public interface OpportunityService {
    public Response<OpportunityTableResponse> getOpportunitiesTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<OpportunityView> createOpportunity(CreateOpportunityRequest request, Long accountId, String language);
    public Response<OpportunityView> updateOpportunity(UpdateOpportunityRequest request, Long accountId, String language);
    public Response<Integer> deleteOpportunities(List<Long> ids);
    public Response<OpportunityDetails> getOpportunityDetails(Long opportunityId, Long accountId, String language);
    public Response<OpportunityKanbanResponse> getOpportunitiesKanban(String filters, String groupBy, String search, Long accountId, String language);
    public Response<OpportunitySplitViewResponse> getOpportunitiesSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<Boolean> updateKanbanOpportunity(Long opportunityId, String fieldName, String fieldValue, Long accountId, String language);
    public List<OpportunityView> getOpportunitiesByAccountId(Long accountId);
    public Opportunity findById(Long id);
    public Response<List<OpportunityContactRoleView>> bulkCreateContactRolesForOpportunity(List<CreateOpportunityContactRoleAttachedRequest> contactRoleList, Long opportunityId, Long accountId, String language);
    public Response<List<OpportunityContactRoleView>> bulkUpdateContactRolesForOpportunity(List<UpdateOpportunityContactRoleAttachedRequest> contactRoleList, Long opportunityId, Long accountId, String language);
    public Response<String> bulkDeleteContactRoleForOpportunity(List<Long> contactRoleIds, Long opportunityId, Long accountId, String language);
    public Response<GenericTableResponse<OpportunityContactRoleView>> getContactRolesForOpportunity(Long opportunityId, int page, int limit, Long accountId, String language);

    public Response<GenericTableResponse<QuoteAttachedView>> getAttachedQuoteForOpportunity(Long opportunityId, int page, int limit, Long accountId, String language);
}
