package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.List;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapper.*;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;

@Service("lookUpService")
public class LookUpServiceImpl implements LookUpService {

    @Autowired
    private UserMapper userMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private ContactMapper contactMapper;
    @Autowired
    private ContractMapper contractMapper;
    @Autowired
    private OpportunityMapper opportunityMapper;
    @Autowired
    private LeadMapper leadMapper;
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private CampaignRepository campaignRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private OpportunityRepository opportunityRepository;
    @Autowired
    private LeadRepository leadRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private RoleRepository roleRepository;


    @Override
    public Response<Page<LookUpUserView>> lookUpUsers(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<User> users = userRepository.findByKeyword(keywordSearch, pageable);
        Page<LookUpUserView> lookUpUserViewsPage = mappingPageUserToLookUpUserView(users);
        return new Response<>(StatusCode.OK, "lookup users pagination successfully", lookUpUserViewsPage);
    }

    @Override
    public Response<Page<LookUpAccountView>> lookUpAccounts(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Account> accounts = accountRepository.findByNameContainingIgnoreCase(keywordSearch, pageable);
        Page<LookUpAccountView> lookUpAccountViewPage = mappingPageAccountToLookupAccountView(accounts);
        return new Response<>(StatusCode.OK, "lookup accounts pagination successfully", lookUpAccountViewPage);
    }

    @Override
    public Response<Page<LookUpContactView>> lookUpContacts(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Contact> contacts = contactRepository.findByKeyword(keywordSearch, pageable);
        Page<LookUpContactView> lookUpContactViewPage = mappingPageContactToLookupContactView(contacts);
        return new Response<>(StatusCode.OK, "lookup contacts pagination successfully", lookUpContactViewPage);
    }

    @Override
    public Response<Page<LookUpContractView>> lookUpContracts(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Contract> contracts = contractRepository.findByContractNumberContainingIgnoreCase(keywordSearch, pageable);
        Page<LookUpContractView> lookUpContractViewPage = mappingPageContractToLookupContractView(contracts);
        return new Response<>(StatusCode.OK, "lookup contracts pagination successfully", lookUpContractViewPage);
    }
    
    @Override
    // lookup orders by keyword search
    public Response<Page<LookUpOrderView>> lookUpOrders(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Order> orders = orderRepository.findByCodeContainingIgnoreCase(keywordSearch, pageable);
        Page<LookUpOrderView> lookUpOrderViewPage = mappingPageOrderToLookupOrderView(orders);
        return new Response<>(StatusCode.OK, "lookup orders pagination successfully", lookUpOrderViewPage);
    }
    

    @Override
    public Response<Page<LookupModuleShortView>> lookUpCampaigns(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Campaign> campaigns = campaignRepository.findByNameContainingIgnoreCase(keywordSearch, pageable);
        Page<LookupModuleShortView> lookupModuleShortViews = mappingPageCampaignToLookupModuleShortView(campaigns);
        return new Response<>(StatusCode.OK, "lookup campaigns pagination successfully", lookupModuleShortViews);
    }
    
    @Override
    public Response<Page<LookUpOpportunityView>> lookUpOpportunities(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Opportunity> opportunities = opportunityRepository.findByNameContainingIgnoreCase(keywordSearch, pageable);
        Page<LookUpOpportunityView> lookUpOpportunityViewPage = mappingPageOpportunityToLookupOpportunityView(opportunities);
        return new Response<>(StatusCode.OK, "lookup opportunities pagination successfully", lookUpOpportunityViewPage);
    }

    @Override
    public Response<Page<LookUpLeadView>> lookUpLeads(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Lead> leads = leadRepository.findByKeyword(keywordSearch, pageable);
        Page<LookUpLeadView> lookUpLeadViewPage = mappingPageLeadToLookupLeadView(leads);
        return new Response<>(StatusCode.OK, "lookup leads pagination successfully", lookUpLeadViewPage);
    }

    @Override
    public Response<Page<RoleView>> lookUpRoles(String keywordSearch, int page, int limit) {
        Pageable pageable = PageRequest.of(page, limit);
        Page<Role> roles = roleRepository.findByNameContainingIgnoreCase(keywordSearch, pageable);
        Page<RoleView> roleViewPage = mappingPageRoleToLookupRoleView(roles);
        return new Response<>(StatusCode.OK, "lookup roles pagination successfully", roleViewPage);
    }

    private Page<LookUpUserView> mappingPageUserToLookUpUserView(Page<User> users) {
        List<LookUpUserView> lookUpUserViews = userMapper.toLookUpUserView(users.getContent());
        return new PageImpl<>(lookUpUserViews, users.getPageable(), users.getTotalElements());
    }
    
    private Page<LookUpAccountView> mappingPageAccountToLookupAccountView(Page<Account> accounts) {
        List<LookUpAccountView> lookupModuleShortViews = accountMapper.toLookUpAccountView(accounts.getContent());
        return new PageImpl<>(lookupModuleShortViews, accounts.getPageable(), accounts.getTotalElements());
    }
    
    private Page<LookUpContactView> mappingPageContactToLookupContactView(Page<Contact> contacts) {
        List<LookUpContactView> lookupModuleShortViews = contactMapper.toLookUpContactView(contacts.getContent());
        return new PageImpl<>(lookupModuleShortViews, contacts.getPageable(), contacts.getTotalElements());
    }
    
    private Page<LookUpContractView> mappingPageContractToLookupContractView(Page<Contract> contracts) {
        List<LookUpContractView> lookupModuleShortViews = contractMapper.toLookUpContractView(contracts.getContent());
        return new PageImpl<>(lookupModuleShortViews, contracts.getPageable(), contracts.getTotalElements());
    }
    
    private Page<LookUpOrderView> mappingPageOrderToLookupOrderView(Page<Order> orders) {
        List<LookUpOrderView> lookupModuleShortViews = orderMapper.toLookUpOrderView(orders.getContent());
        return new PageImpl<>(lookupModuleShortViews, orders.getPageable(), orders.getTotalElements());
    }
    

    private Page<LookUpOpportunityView> mappingPageOpportunityToLookupOpportunityView(Page<Opportunity> opportunities) {
        List<LookUpOpportunityView> lookupModuleShortViews = opportunityMapper.toLookUpOpportunityView(opportunities.getContent());
        return new PageImpl<>(lookupModuleShortViews, opportunities.getPageable(), opportunities.getTotalElements());
    }
    
    private Page<RoleView> mappingPageRoleToLookupRoleView(Page<Role> roles) {
        List<RoleView> roleViews = roleMapper.toRoleView(roles.getContent());
        return new PageImpl<>(roleViews, roles.getPageable(), roles.getTotalElements());
    }
    
    private Page<LookUpLeadView> mappingPageLeadToLookupLeadView(Page<Lead> leads) {
        List<LookUpLeadView> lookupModuleShortViews = leadMapper.toLookUpLeadView(leads.getContent());
        return new PageImpl<>(lookupModuleShortViews, leads.getPageable(), leads.getTotalElements());
    }
    
    private Page<LookupModuleShortView> mappingPageCampaignToLookupModuleShortView(Page<Campaign> campaigns) {
        List<LookupModuleShortView> lookupModuleShortViews = new ArrayList<>();
        for (Campaign campaign : campaigns.getContent()) {
            lookupModuleShortViews.add(new LookupModuleShortView(campaign.getId(), campaign.getName()));
        }
        return new PageImpl<>(lookupModuleShortViews, campaigns.getPageable(), campaigns.getTotalElements());
    }
    
}
