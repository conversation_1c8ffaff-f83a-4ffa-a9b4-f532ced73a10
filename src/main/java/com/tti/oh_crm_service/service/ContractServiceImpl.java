package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;

import com.tti.oh_crm_service.mapper.ContractMapper;
import com.tti.oh_crm_service.mapper.OrderMapper;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.AccountRepository;
import com.tti.oh_crm_service.repository.ContractRepository;
import com.tti.oh_crm_service.repository.OrderRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;

@Service("contractService")
@Slf4j
public class ContractServiceImpl implements ContractService {
    @Autowired
    private ContractMapper contractMapper;
    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private OrderRepository orderRepository;
    
    @Autowired
    private CoreQueryService<Contract> coreQueryService;
    
    
    @Autowired
    private AccountService accountService;
    @Autowired
    private ContactService contactService;
    @Autowired
    private ActivityService activityService;

    @Override
    public Response<ContractTableResponse> getContractsTable(
            String filters, 
            String groupBy, 
            String groupValue, 
            String search, 
            String sortBy, 
            String sortDirection, 
            int page, 
            int limit, 
            Long accountId, 
            String language
    ) {
        List<String> searchFields = Arrays.asList(
                "contract_name",
                "description"
        );

        GenericTableResponse<Contract> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "contracts", searchFields, "CONTRACT",
                contractRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        Page<Contract> contractPage = tableResponse.getData();
        List<ContractView> contractViews = contractMapper.toContractView(contractPage.getContent());
        ContractTableResponse contractTableResponse = new ContractTableResponse();
        contractTableResponse.setGroupItems(tableResponse.getGroupItems());
        contractTableResponse.setContracts(new PageImpl<>(contractViews, contractPage.getPageable(), contractPage.getTotalElements()));
        return new Response<>(StatusCode.OK, "Get contacts table success!!!", contractTableResponse);
    }

    @Override
    @Transactional
    public Response<ContractView> createContract(CreateContractRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<User> optOwner = userRepository.findById(request.getOwnerId());
        if (optOwner.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Owner not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Contract CREATE");

        Contact signedByContact = null;
        if (request.getSignedByContactId() != null) {
            signedByContact = contactService.findById(request.getSignedByContactId());
        }

        User signedByUser = null;
        if (request.getSignedByUserId() != null) {
            signedByUser = userRepository.findById(request.getSignedByUserId()).orElse(null);
        }

        Account account = null;
        if (request.getAccountId() != null) {
            account = accountService.findById(request.getAccountId());
        }

        Contract contract = new Contract();
        contractMapper.create(request, contract, new UserMapStructContext(optUser.get(), optOwner.get()));
        contract.setSignedByContact(signedByContact);
        contract.setSignedByUser(signedByUser);
        contract.setAccount(account);

        // Save contract - HistoryEntityListener will automatically handle CREATE history
        contractRepository.save(contract);

        ContractView contractView = contractMapper.toContractView(contract);
        return new Response<>(StatusCode.OK, "Create contract success!!!", contractView);
    }

    @Override
    @Transactional
    public Response<ContractView> updateContract(UpdateContractRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<Contract> optContract = contractRepository.findById(request.getId());
        if (optContract.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contract not found!!!");
        }

        Contract contract = optContract.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Contract originalContract = EntityCloneUtils.cloneEntityForHistory(contract);
        EntityContext.setOriginalEntity(originalContract);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Contract UPDATE");

        // Step 2: Apply updates to the entity
        contractMapper.update(request, contract, optUser.get());

        // Handle relationship updates (if any)
        updateContractRelationships(request, contract);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(contract);

        // Save contract - HistoryEntityListener will automatically handle UPDATE history with field changes
        contractRepository.save(contract);

        ContractView contractView = contractMapper.toContractView(contract);
        return new Response<>(StatusCode.OK, "Update contract success!!!", contractView);
    }

    @Override
    public Response<Integer> deleteContracts(List<Long> ids) {
        // Check contracts exist
        for (Long id : ids) {
            Optional<Contract> optContract = contractRepository.findById(id);
            if (optContract.isEmpty()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Contract not found!!!");
            }
        }
        Integer result = contractRepository.deleteByIdIn(ids);
        return new Response<>(StatusCode.OK, "Delete contracts success!!!", result);
    }

    @Override
    public Response<ContractDetails> getContractDetails(Long contractId, Long accountId, String language) {
        Optional<Contract> optContract = contractRepository.findById(contractId);
        if (optContract.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contract not found!!!");
        }
        ContractDetails contractDetails = contractMapper.toContractDetails(optContract.get());

        // Get attached activities
        List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(
                contractId, "CONTRACT", null, accountId, language
        );
        contractDetails.setAttachedActivities(attachedActivities);

        // Get related orders count and top 2 orders
        Long countRelatedOrders = orderRepository.countByContractId(contractId);
        contractDetails.setCountRelatedOrders(countRelatedOrders);
        Pageable topTwoOrders = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "lastModifiedAt"));
        List<OrderAttachedView> attachedOrders = orderRepository.findByContractIdOrderByLastModifiedAtDesc(contractId, topTwoOrders)
                .stream()
                .map(orderMapper::toOrderAttachedView)
                .toList();
        contractDetails.setAttachedOrders(attachedOrders);
        
        return new Response<>(StatusCode.OK, "Get contract details success!!!", contractDetails);
    }

    @Override
    public Response<ContractKanbanResponse> getContractsKanban(String filters, String groupBy, String search, Long accountId, String language) throws ParseException {
        List<String> searchFields = Arrays.asList(
                "contract_number", 
                "description"
        );

        List<String> viewFields = Arrays.asList(
                "id",
                "contract_number",
                "start_date",
                "end_date",
                "duration_by_month",
                "status",
                "description"
        );

        GenericKanbanResponse kanbanResponse = coreQueryService.getKanban(
                filters, groupBy, search, accountId, language, "contracts", searchFields, viewFields, "CONTRACT", contractRepository
        );

        if (kanbanResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<ContractKanbanView> contractKanbanViews = new ArrayList<>();
        for (Object[] itemObject : kanbanResponse.getItemObjects()) {
            ContractKanbanView contractKanbanView = new ContractKanbanView();
            contractKanbanView.setId((Long) itemObject[0]);
            contractKanbanView.setContractNumber((String) itemObject[1]);
            contractKanbanView.setStartDate((Date) itemObject[2]);
            contractKanbanView.setEndDate((Date) itemObject[3]);
            contractKanbanView.setDurationByMonth((Integer) itemObject[4]);
            contractKanbanView.setStatus((String) itemObject[5]);
            contractKanbanView.setDescription((String) itemObject[6]);
            contractKanbanView.setGroupField((String) itemObject[7]);
            
            contractKanbanViews.add(contractKanbanView);
        }
        ContractKanbanResponse contractKanbanResponse = new ContractKanbanResponse();
        contractKanbanResponse.setGroupItems(kanbanResponse.getGroupItems());
        contractKanbanResponse.setContracts(contractKanbanViews);
        return new Response<>(StatusCode.OK, "Get contracts kanban success!!!", contractKanbanResponse);
    }

    @Override
    public Response<ContractSplitViewResponse> getContractsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = List.of(
                "contract_number",
                "description"
        );
        
        // Define view fields
        List<String> viewFields = List.of(
                "id",
                "contract_number",
                "start_date",
                "end_date",
                "duration_by_month",
                "status",
                "notification_days_before_end",
                "special_terms",
                "description",
                "signed_content",
                "signed_date",
                "user_signed_date",
                "owner_id",
                "account_id"
        );

        GenericSplitViewResponse<Object[]> genericResponse = coreQueryService.getSplitView(
                filters, search, sortBy, sortDirection, page, limit,
                "contracts", searchFields, viewFields, contractRepository
        );

        if (genericResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<ContractSplitView> contracts = new ArrayList<>();
        for (Object[] contractObject : genericResponse.getData().getContent()) {
            ContractSplitView contractSplitView = new ContractSplitView();
            // Set contract fields based on the returned data
            contractSplitView.setId((Long) contractObject[0]);
            contractSplitView.setContractNumber((String) contractObject[1]);
            contractSplitView.setStartDate((Date) contractObject[2]);
            contractSplitView.setEndDate((Date) contractObject[3]);
            contractSplitView.setDurationByMonth((Integer) contractObject[4]);
            contractSplitView.setStatus((String) contractObject[5]);
            contractSplitView.setNotificationDaysBeforeEnd((Integer) contractObject[6]);
            contractSplitView.setSpecialTerms((String) contractObject[7]);
            contractSplitView.setDescription((String) contractObject[8]);
            contractSplitView.setSignedContent((String) contractObject[9]);
            contractSplitView.setSignedDate((Date) contractObject[10]);
            contractSplitView.setUserSignedDate((Date) contractObject[11]);
            Long ownerId = (Long) contractObject[12];
            if (ownerId != null) {
                Optional<User> optOwner = userRepository.findById(ownerId);
                if (optOwner.isPresent()) {
                    LookUpView ownerLookUpView = new LookUpView();
                    ownerLookUpView.setId(optOwner.get().getId());
                    ownerLookUpView.setName(optOwner.get().getFirstName() + " " + optOwner.get().getLastName());
                    contractSplitView.setOwner(ownerLookUpView);
                }

            }
            Long linkedAccountId = (Long) contractObject[13];
            if (linkedAccountId != null) {
                Optional<Account> optLinkedAccount = accountRepository.findById(linkedAccountId);
                if (optLinkedAccount.isPresent()) {
                    LookUpView accountLookUpView = new LookUpView();
                    accountLookUpView.setId(optLinkedAccount.get().getId());
                    accountLookUpView.setName(optLinkedAccount.get().getName());
                    contractSplitView.setAccount(accountLookUpView);
                }
            }
            contracts.add(contractSplitView);
        }

        Page<ContractSplitView> contractsPage = new PageImpl<>(
                contracts,
                genericResponse.getData().getPageable(),
                genericResponse.getData().getTotalElements()
        );

        ContractSplitViewResponse response = new ContractSplitViewResponse();
        response.setContracts(contractsPage);
        return new Response<>(StatusCode.OK, "Get contracts split view success!!!", response);
    }

    @Override
    public Response<Boolean> updateKanbanContract(Long contractId, String fieldName, String fieldValue, Long accountId, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "contracts");
        jsonFilter.put("id", contractId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);

        contractRepository.updateKanbanData(jsonFilter.toString());

        return new Response<>(StatusCode.OK, "Update contract kanban success!!!", true);
    }

    @Override
    public Response<GenericTableResponse<OrderAttachedView>> getAttachedOrdersForContract(Long contractId, int page, int limit, Long accountId, String language) {
        if (contractId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contract ID is null!!!");
        }
        Optional<Contract> optContract = contractRepository.findById(contractId);
        if (optContract.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contract not found!!!");
        }
        Pageable pageable = PageRequest.of(page, limit);
        Page<Order> attachedOrders = orderRepository.findByContractIdOrderByLastModifiedAtDesc(contractId, pageable);
        Page<OrderAttachedView> orderPageViews = attachedOrders.map(orderMapper::toOrderAttachedView);
        return new Response<>(StatusCode.OK, "Get attached orders for contract success!!!", new GenericTableResponse<>(orderPageViews, null));
    }

    @Override
    public List<ContractView> getContractsByAccountId(Long accountId) {
        List<Contract> contracts = contractRepository.findByAccountId(accountId);
        return contractMapper.toContractView(contracts);
    }

    @Override
    public Contract findById(Long id) {
        Contract contract = contractRepository.findById(id).orElse(null);
        return contract;
    }

    /**
     * Update contract relationships based on the provided IDs in the request
     */
    private void updateContractRelationships(UpdateContractRequest request, Contract contract) {
        if (request.getAccountId() != null) {
            Account account = accountService.findById(request.getAccountId());
            if (account == null) {
                return;
            }
            contract.setAccount(account);
        }

        if (request.getSignedByContactId() != null) {
            Contact contact = contactService.findById(request.getSignedByContactId());
            if (contact == null) {
                return;
            }
            contract.setSignedByContact(contact);
        }

        if (request.getSignedByUserId() != null) {
            User user = userRepository.findById(request.getSignedByUserId()).orElse(null);
            if (user == null) {
                return;
            }
            contract.setSignedByUser(user);
        }
    }
}
