package com.tti.oh_crm_service.service;

import java.util.List;

import com.tti.oh_crm_service.entity.*;

public interface ActivityService {

    public Response<ActivityView> getActivityDetail(Long id, Long accountId, String language);

    public Response<ActivityTableResponse> getActivities(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, String language);

    public Response<ActivityView> createActivity(CreateActivityRequest createActivityRequest, Long accountId, String language);

    public Response<ActivityView> updateActivity(UpdateActivityRequest updateActivityRequest, Long accountId, String language);

    public Response<List<Long>> deleteActivities(List<Long> activityIds);

    public Response<ActivityKanbanResponse> findKanban(String filters, String groupBy, String search, Long accountId, String language);

    public Response<ActivitySplitViewResponse> findSplitViewData(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    
    public Response<Boolean> updateKanbanActivity(Long accountId, String fieldName, String fieldValue, String language);
    public Response<AttachedActivitiesViewResponse> getAttachedActivities(Long moduleId, String moduleCode, String type, int page, int limit, Long accountId, String language);
    public List<AttachedActivityShortView> getAttachedActivitiesByModuleAndType(Long moduleId, String moduleCode, String type, Long accountId, String language);
}
