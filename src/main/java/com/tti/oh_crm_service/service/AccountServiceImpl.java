package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Optional;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.UserRepository;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.mapper.AccountMapper;
import com.tti.oh_crm_service.mapper.ContactMapper;
import com.tti.oh_crm_service.mapper.OpportunityMapper;
import com.tti.oh_crm_service.repository.AccountRepository;
import com.tti.oh_crm_service.repository.ContactRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import com.tti.oh_crm_service.repository.OpportunityRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;

import io.jsonwebtoken.Claims;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AccountServiceImpl implements AccountService{

    @Autowired 
    private AccountRepository accountRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private OpportunityRepository opportunityRepository;

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private ContactMapper contactMapper;
    @Autowired
    private OpportunityMapper opportunityMapper;

    @Autowired
    @Lazy
    private ContactService contactService;
    @Autowired
    @Lazy
    private OpportunityService opportunityService;
    @Autowired
    private ActivityService activityService;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;

    @Autowired
    private CoreQueryService<Account> coreQueryService;

    @Override
    public Response<AccountTableResponse> getAccounts(
        String filters, 
        String groupBy, 
        String groupValue, 
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit, 
        Long accountId, 
        String language
    ) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "name",
                "description",
                "mobile",
                "email",
                "phone",
                "website"
        );

        GenericTableResponse<Account> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "accounts", searchFields, "ACCOUNT",
                accountRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }
        
        Page<Account> accountsPage = tableResponse.getData();
        List<AccountView> listAccountView = accountMapper.toAccountViews(accountsPage.getContent());

        Page<AccountView> accountViewPage = new PageImpl<>(listAccountView, accountsPage.getPageable(), accountsPage.getTotalElements());
        AccountTableResponse accountTableResponse = new AccountTableResponse();
        accountTableResponse.setGroupItems(tableResponse.getGroupItems());
        accountTableResponse.setAccounts(accountViewPage);

        return new Response<AccountTableResponse>(StatusCode.OK,  "Get accounts successfully", accountTableResponse);
    }

    @Override
    @Transactional
    public Response<AccountView> createAccount(Claims claims, CreateAccountRequest createAccountRequest, Locale locale) {
        // Get user from claims
        Long accountId = TokenUtils.getAccountId(claims);
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Account CREATE");

        Account account = accountMapper.create(createAccountRequest);

        // Fetch and set the owner
        if (createAccountRequest.getOwnerId() != null) {
            Optional<User> owner = userRepository.findById(createAccountRequest.getOwnerId());
            if (owner.isPresent()) {
                account.setOwner(owner.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Owner not found", null);
            }
        }

        account.setCreatedBy(optUser.get());
        account.setLastModifiedBy(optUser.get());

        // Save account - HistoryEntityListener will automatically handle CREATE history
        Account savedAccount = accountRepository.save(account);

        AccountView accountView = accountMapper.toAccountView(savedAccount);
        return new Response<>(StatusCode.OK, "Account created successfully", accountView);
    }

    @Override
    @Transactional
    public Response<AccountView> updateAccount(Claims claims, UpdateAccountRequest updateAccountRequest, Locale locale) {
        // Get user from claims
        Long accountId = TokenUtils.getAccountId(claims);
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Optional<Account> optAccount = accountRepository.findById(updateAccountRequest.getId());
        if (optAccount.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account not found");
        }

        Account account = optAccount.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Account originalAccount = EntityCloneUtils.cloneEntityForHistory(account);
        EntityContext.setOriginalEntity(originalAccount);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Account UPDATE");

        // Step 2: Apply updates to the entity
        accountMapper.updateAccount(updateAccountRequest, account);

        // Handle relationship updates
        updateAccountRelationships(updateAccountRequest, account);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(account);

        account.setLastModifiedBy(optUser.get());

        // Save account - HistoryEntityListener will automatically handle UPDATE history with field changes
        account = accountRepository.save(account);

        AccountView accountViewResult = accountMapper.toAccountView(account);
        return new Response<>(StatusCode.OK, "Update account successfully", accountViewResult);
    }

    @Override
    public Response<List<Long>> deleteAccount(List<Long>  accountIds) {
        List<Long> listNotFound = new ArrayList<>();
        for(Long accountId : accountIds){
            Optional<Account> optAccount = accountRepository.findById(accountId);
            if(optAccount.isEmpty()){
                listNotFound.add(accountId);
            }
        }

        if(!listNotFound.isEmpty()){
            return new Response<List<Long>>(StatusCode.BAD_REQUEST,  "Account not found", listNotFound);
        }

        for(Long accountId : accountIds){
            accountRepository.deleteById(accountId);
        }
            

        return new Response<List<Long>>(StatusCode.OK,  "Delete account successfully", accountIds);
    }

    @Override
    @Transactional
    public Response<AccountKanbanResponse> findKanban(String filters, String groupBy, String search, Long accountId, String language) {
        
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("name");
        searchFields.put("description");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            searchFields.put(groupBy);
        }else{
            searchFields.put("type");
        }

        // Get accounts in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "accounts");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("name");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            viewFields.put(groupBy);
        }else{
            viewFields.put("type");
        }   
        jsonFilter.put("view_fields", viewFields);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        Long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("ACCOUNT");
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }
                List<Object[]> groupValues = accountRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        System.out.println("-----JsonFilter-----" + jsonFilter.toString());

        List<Long> countByFilter = accountRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
       
        System.out.println(jsonFilter.toString());
        
        List<AccountKanbanView> products = new ArrayList<>();
        try {
            List<Object[]> accountObjects = accountRepository.findKanban(jsonFilter.toString());
            for (Object[] accountObject : accountObjects) {
                AccountKanbanView accountKanbanView = new AccountKanbanView();
                accountKanbanView.setId((Long) accountObject[0]);
                accountKanbanView.setName((String) accountObject[2]);
                accountKanbanView.setGroupField((String) accountObject[3]);
                products.add(accountKanbanView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<AccountKanbanResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        AccountKanbanResponse accountKanbanResponse = new AccountKanbanResponse();
        accountKanbanResponse.setGroupItems(groupItems);
        accountKanbanResponse.setAccounts(products);
        return new Response<AccountKanbanResponse>(StatusCode.OK, "Get accounts kanban success!!!", accountKanbanResponse);
    }

    @Override
    public Response<AccountSplitViewResponse> findSplitViewData(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        
        JSONArray searchFields = new JSONArray();
        searchFields.put("id");
        searchFields.put("name");
        searchFields.put("type");

        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "accounts");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("name");
        viewFields.put("type");
        jsonFilter.put("view_fields", viewFields);
       
        System.out.println("-----JsonFilter-----" + jsonFilter.toString());
        
        List<AccountSplitView> accounts = new ArrayList<>();
        try {
            List<Object[]> accountObjects = accountRepository.findSplitViewData(jsonFilter.toString());
            for (Object[] accountObject : accountObjects) {
                AccountSplitView accountSplitView = new AccountSplitView();
                accountSplitView.setId((Long) accountObject[0]);
                accountSplitView.setName((String) accountObject[2]);
                accountSplitView.setType((String) accountObject[3]);
                accounts.add(accountSplitView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<AccountSplitViewResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        // Get Total rows
        List<Long> countByFilter = accountRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        Page<AccountSplitView> accountsPage = new PageImpl<>(accounts, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        AccountSplitViewResponse accountSplitViewResponse = new AccountSplitViewResponse();
        accountSplitViewResponse.setAccounts(accountsPage);
        return new Response<AccountSplitViewResponse>(StatusCode.OK, "Get accounts split view success!!!", accountSplitViewResponse);
    }

    @Override
    public Response<Boolean> updateKanbanAccount(Long accountId, String fieldName, String fieldValue, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "accounts");
        jsonFilter.put("id", accountId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);
        
        accountRepository.updateKanbanData(jsonFilter.toString());

        return new Response<Boolean>(StatusCode.OK, "Update account kanban success!!!", true);
    }

    @Override
    public Account findById(Long accountId) {
        Optional<Account> optAccount = accountRepository.findById(accountId);
        return optAccount.orElse(null);
    }

    @Override
    public AccountView getAccountView(Long accountId) {
        Optional<Account> optAccount = accountRepository.findById(accountId);
        if(optAccount.isPresent()){
            Account account = optAccount.get();
            return accountMapper.toAccountView(account);
        }
        return null;
    }

    @Override
    public Response<ContactOpportunityListByAccountResponse> getContactOpportunityListByAccount(Long accountId) {
        // Check if account exists
        if (accountRepository.findById(accountId).isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account not found", null);
        }

        // Get the contact list for the account
        List<ContactView> contactList = contactService.getContactsByAccountId(accountId);

        // Get the opportunity list for the account
        List<OpportunityView> opportunityList = opportunityService.getOpportunitiesByAccountId(accountId);

        ContactOpportunityListByAccountResponse response = new ContactOpportunityListByAccountResponse(accountId, contactList, opportunityList);
        return new Response<>(StatusCode.OK, "Get contact and opportunity list by account successfully", response);
        
    }

    @Override
    public Response<AccountDetails> getAccountDetail(Claims claims, Locale locale, Long id) {
        Optional<Account> optAccount = accountRepository.findById(id);
        if(optAccount.isPresent()){
            Account account = optAccount.get();
            AccountDetails accountDetails = accountMapper.toAccountDetails(account);
            
            // Count related contacts for an account
            Long countRelatedContacts = contactRepository.countByAccountId(id);
            accountDetails.setCountRelatedContacts(countRelatedContacts);

            // Get related contacts for an account
            Pageable pageable = PageRequest.of(0, 2, Direction.DESC, "createdAt");
            Page<Contact> relatedContacts = contactRepository.findByAccountId(id, pageable);
            accountDetails.setAttachedContacts(contactMapper.toContactAttachedView(relatedContacts.getContent()));

            // Count related opportunities for an account
            Long countRelatedOpportunities = opportunityRepository.countByAccountId(id);
            accountDetails.setCountRelatedOpportunities(countRelatedOpportunities);

            // Get related opportunities for an account
            Page<Opportunity> relatedOpportunities = opportunityRepository.findByAccountId(id, pageable);
            accountDetails.setAttachedOpportunities(opportunityMapper.toOpportunityAttachedView(relatedOpportunities.getContent()));

            // Count related campaigns for an account
            List<Long> countRelatedCampaigns = accountRepository.countCampaignsByAccountId(id);
            if (countRelatedCampaigns != null && countRelatedCampaigns.size() > 0) {
                accountDetails.setCountRelatedCampaigns(countRelatedCampaigns.get(0));
            }

            // get attached activities for an account
            List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(id, "ACCOUNT", null, id, locale.toString());
            accountDetails.setAttachedActivities(attachedActivities);
            return new Response<AccountDetails>(StatusCode.OK,  "Get account detail successfully", accountDetails);
        }else{
            return new Response<AccountDetails>(StatusCode.BAD_REQUEST,  "Account not found");
        }
    }

    @Override
    public Response<GenericTableResponse<ContactAttachedView>> getAttachedContactForAccount(Long accountId, int page, int limit, Long loginAccountId, String language) {
        // check if contact exists
        if (accountId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account ID is null!!!");
        }
        Optional<Account> optAccount = accountRepository.findById(accountId);
        if (optAccount.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        Page<Contact> attachedContacts = contactRepository.findByAccountId(accountId, pageable);
        Page<ContactAttachedView> contactPageViews = attachedContacts.map(contact -> contactMapper.toContactAttachedView(contact));
        return new Response<>(StatusCode.OK, "Get attached contacts for account success!!!", new GenericTableResponse<>(contactPageViews, null));
    }
    
    // get attached opportunities for an account
    @Override
    public Response<GenericTableResponse<OpportunityAttachedView>> getAttachedOpportunityForAccount(Long accountId, int page, int limit, Long loginAccountId, String language) {
        // check if opportunity exists
        if (accountId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account ID is null!!!");
        }
        Optional<Account> optAccount = accountRepository.findById(accountId);
        if (optAccount.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        Page<Opportunity> attachedOpportunities = opportunityRepository.findByAccountId(accountId, pageable);
        Page<OpportunityAttachedView> opportunityPageViews = attachedOpportunities.map(opportunity -> opportunityMapper.toOpportunityAttachedView(opportunity));
        return new Response<>(StatusCode.OK, "Get attached opportunities for account success!!!", new GenericTableResponse<>(opportunityPageViews, null));
    }


    /**
     * Update account relationships based on the provided IDs in the request
     * Handles ownerId and other relationship fields
     */
    private void updateAccountRelationships(UpdateAccountRequest request, Account account) {
        // Update Owner relationship
        if (request.getOwnerId() != null) {
            Optional<User> optOwner = userRepository.findById(request.getOwnerId());
            if (optOwner.isPresent()) {
                account.setOwner(optOwner.get());
            }
        }
    }

}
