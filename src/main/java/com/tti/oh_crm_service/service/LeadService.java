package com.tti.oh_crm_service.service;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;

import reactor.core.publisher.Mono;

public interface LeadService {
        public Response<LeadTableResponse> getLeadsTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
        public Response<LeadView> createLead(CreateLeadRequest request, Long accountId, String language);
        public Response<LeadView> updateLead(UpdateLeadRequest request, Long accountId, String language);
        public Response<Integer> deleteLeads(List<Long> ids);
        public Response<LeadDetails> getLeadDetails(Long leadId, Long accountId, String language);
        public Response<LeadKanbanResponse> getLeadsKanban(String filters, String groupBy, String search, Long accountId, String language);
        public Response<LeadSplitViewResponse> getLeadsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
        public Response<Boolean> updateKanbanLead(Long leadId, String fieldName, String fieldValue, Long accountId, String language);
        public Mono<ResponseEntity<Response<ImportLeadResponse>>> importLeads(FilePart dataFile, ImportLeadRequest request, Long accountId, String language);
        public Response<LeadView> updateLeadTags(Long leadId, String tags);
        public Response<LeadConversionResponse> convertLead(LeadConversionRequest request, Long accountId, String language); 
}
