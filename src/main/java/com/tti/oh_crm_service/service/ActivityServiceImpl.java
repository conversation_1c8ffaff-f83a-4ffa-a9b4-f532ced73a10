package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.mapper.ActivityMapper;
import com.tti.oh_crm_service.model.Activity;
import com.tti.oh_crm_service.model.ActivityBelongTo;
import com.tti.oh_crm_service.model.ActivityUser;
import com.tti.oh_crm_service.model.LayoutSettings;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.ActivityBelongToRepository;
import com.tti.oh_crm_service.repository.ActivityRepository;
import com.tti.oh_crm_service.repository.ActivityUserRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.ServiceUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;

import jakarta.transaction.Transactional;


@Slf4j
@Service
public class ActivityServiceImpl implements ActivityService{

    @Autowired
    private ServiceUtils serviceUtils;

    @Autowired
    private ActivityRepository activityRepository;

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ActivityBelongToRepository activityBelongToRepository;
    @Autowired
    private ActivityUserRepository activityUserRepository;

    @Override
    @Transactional
    public Response<ActivityView> getActivityDetail(Long id, Long accountId, String language) {
        Activity activity = activityRepository.findById(id).orElse(null);
        if(activity == null) {
            return new Response<ActivityView>(StatusCode.BAD_REQUEST, "Activity not found", null);
        }

        ActivityView activityView = activityMapper.toActivityView(activity);

        // Get assigned user info
        List<ActivityUser> activityUsers = activityUserRepository.findByActivityId(id);
        List<LookUpMultipleView> assignToUsers = new ArrayList<>();
        for (ActivityUser activityUser : activityUsers) {
            LookUpMultipleView assignToUser = new LookUpMultipleView();
            assignToUser.setId(activityUser.getId());

            Optional<User> optUser = userRepository.findById(activityUser.getUserId());
            if (optUser.isPresent()) {
                LookUpView lookUpView = new LookUpView();
                lookUpView.setId(optUser.get().getId());
                lookUpView.setName(optUser.get().getFirstName() + " " + optUser.get().getLastName());
                assignToUser.setData(lookUpView);
            }
            assignToUsers.add(assignToUser);
        }
        activityView.setAssignToUsers(assignToUsers);

        // Get belong to info
        List<ActivityBelongTo> activityBelongToList = activityBelongToRepository.findByActivityId(id);
        List<LookUpMultipleView> activityBelongToViews = new ArrayList<>();
        for (ActivityBelongTo activityBelongTo : activityBelongToList) {
            if (activityBelongTo.getBelongToModule() != null) {
                activityView.setBelongToModule(activityBelongTo.getBelongToModule());

                LookUpMultipleView activityBelongToView = new LookUpMultipleView();
                activityBelongToView.setId(activityBelongTo.getId());
                activityBelongToView.setData(serviceUtils.getLookUpViewById(activityBelongTo.getBelongToModule(), activityBelongTo.getBelongToId()));
                activityBelongToViews.add(activityBelongToView);
                activityView.setBelongTos(activityBelongToViews);
            }
        }

        // Get related to info
        activityView.setRelatedTo(serviceUtils.getLookUpViewById(activity.getRelatedToModule(), activity.getRelatedToId()));
        
        return new Response<ActivityView>(StatusCode.OK, "Get activity detail successfully", activityView);
    }

    @Override
    @Transactional
    public Response<ActivityTableResponse> getActivities(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, String language) {
        try{
            ActivityTableResponse activityTableResponse = new ActivityTableResponse();

            JSONObject jsonFilter = new JSONObject();
            jsonFilter.put("table_name", "activity");
            jsonFilter.put("sort_by", sortBy);
            jsonFilter.put("sort_direction", sortDirection);
            jsonFilter.put("page", page);
            jsonFilter.put("limit", limit);
            
            JSONArray searchFields = new JSONArray();
            searchFields.put("title");
            searchFields.put("activity_type");
            searchFields.put("deadline");
            searchFields.put("priority");
            searchFields.put("status");

            if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
                searchFields.put(groupBy);
            }else{
                searchFields.put("status");
            }

            jsonFilter.put("table_name", "activity");
            if (search != null && !search.trim().equalsIgnoreCase("")) {
                jsonFilter.put("search_text", search);
                jsonFilter.put("search_fields", searchFields);
            }


            if (filters != null && !filters.trim().equalsIgnoreCase("")) {
                JSONArray jsonArray = new JSONArray(filters);
                jsonFilter.put("filters", jsonArray);
            }

            // Get group by field
            List<GroupItem> groupItems = new ArrayList<>();
            Long totalRows = -1L;
            Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("ACTIVITY");
            if (optLayoutSettings.isPresent()) {
                
                String groupByField = optLayoutSettings.get().getGroupByField();
                if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                    if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
                        jsonFilter.put("field_name", groupBy);
                        groupByField = groupBy;
                    }else{
                        jsonFilter.put("field_name", groupByField);
                    }
                    List<Object[]> groupValues = activityRepository.getGroupValues(jsonFilter.toString());
                    totalRows = 0L;
                    for (Object[] grpValue : groupValues) {
                        totalRows += (long) grpValue[1];
                        GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                        groupItems.add(groupItem);
                    }

                    if (groupValue != null && !groupValue.trim().equalsIgnoreCase("")) {
                        if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                            jsonFilter.put("group_field", groupBy);
                        } else {
                            jsonFilter.put("group_field", groupByField);
                        }
                        
                        jsonFilter.put("group_value", groupValue);
                    }
                }
            }

            List<Long> countByFilter = activityRepository.countByFilter(jsonFilter.toString());
            Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;
    
            if (totalRows < 0) {
                groupItems.add(0, new GroupItem("total", totalRowsByGroup));
            } else {
                groupItems.add(0, new GroupItem("total", totalRows));
            }

            System.out.println("-----totalRowsByGroup-----" + totalRowsByGroup);

            System.out.println("-----JsonFilter-----" + jsonFilter.toString());
            
            List<Activity> listActivity = activityRepository.findByFilter(jsonFilter.toString());

            List<ActivityView> listActivityView = activityMapper.toActivityView(listActivity);

            System.out.println("ListActivityView" + listActivityView);

            activityTableResponse.setGroupItems(groupItems);
            Page<ActivityView> activityPage = new PageImpl<>(listActivityView, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
            activityTableResponse.setActivities(activityPage);

            return new Response<ActivityTableResponse>(StatusCode.OK,  "Get activities successfully", activityTableResponse);

        }catch(Exception e){
            return new Response<ActivityTableResponse>(StatusCode.INTERNAL_SERVER_ERROR,  "Internal server error");
        }
    }

    @Override
    @Transactional
    public Response<ActivityView> createActivity(CreateActivityRequest createActivityRequest, Long accountId, String language) {
        try{
            Optional<User> optCreateUser = userRepository.findById(accountId);
            if(optCreateUser.isEmpty()){
                return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
            }

            // Set user context for EntityListener
            UserContext.setCurrentUser(optCreateUser.get());

            // Register a transaction synchronization to clear context after commit
            TransactionUtils.registerContextCleanupAfterTransaction("Activity CREATE");

            Activity activity = activityMapper.create(createActivityRequest);
            activity.setCreatedBy(optCreateUser.get());
            activity.setLastModifiedBy(optCreateUser.get());

            // Save activity - HistoryEntityListener will automatically handle CREATE history
            activity = activityRepository.save(activity);

            // Save activity assignees
            if (createActivityRequest.getAssignToUserIds() != null) {
                List<ActivityUser> activityUsers = new ArrayList<>();
                for (Long assigneeId : createActivityRequest.getAssignToUserIds()) {
                    ActivityUser activityAssignee = new ActivityUser();
                    activityAssignee.setActivityId(activity.getId());
                    activityAssignee.setUserId(assigneeId);
                    activityUsers.add(activityAssignee);
                }
                activityUserRepository.saveAll(activityUsers);
            }

            // Save activity belong to
            if (createActivityRequest.getBelongToIds() != null) {
                List<ActivityBelongTo> activityBelongTos = new ArrayList<>();
                for (Long belongToId : createActivityRequest.getBelongToIds()) {
                    ActivityBelongTo activityBelongTo = new ActivityBelongTo();
                    activityBelongTo.setActivityId(activity.getId());
                    activityBelongTo.setBelongToModule(createActivityRequest.getBelongToModule());
                    activityBelongTo.setBelongToId(belongToId);
                    activityBelongTos.add(activityBelongTo);
                }
                activityBelongToRepository.saveAll(activityBelongTos);
            }
            
            // ActivityView activityView = activityMapper.toActivityView(activity);
            return new Response<ActivityView>(StatusCode.OK, "Create activity successfully");
        }catch(Exception e){
            return new Response<ActivityView>(StatusCode.INTERNAL_SERVER_ERROR, "Internal server error", null);
        }
    }

    @Override
    @Transactional
    public Response<ActivityView> updateActivity(UpdateActivityRequest updateActivityRequest, Long accountId, String language) {

        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Activity activity = activityRepository.findById(updateActivityRequest.getId()).orElse(null);
        if(activity == null) {
            return new Response<ActivityView>(StatusCode.BAD_REQUEST, "Activity not found", null);
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Activity originalActivity = EntityCloneUtils.cloneEntityForHistory(activity);
        EntityContext.setOriginalEntity(originalActivity);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Activity UPDATE");

        // Step 2: Apply updates to the entity
        activityMapper.update(updateActivityRequest, activity);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(activity);

        activity.setLastModifiedBy(optUser.get());

        // Save activity - HistoryEntityListener will automatically handle UPDATE history with field changes
        activityRepository.save(activity);

        // Update activity assignees
        if (updateActivityRequest.getDeleteAssignToUserIds() != null) {
            for (Long assignToUserId : updateActivityRequest.getDeleteAssignToUserIds()) {
                activityUserRepository.deleteById(assignToUserId);
            }
        }

        if (updateActivityRequest.getAddAssignToUserIds() != null) {
            List<ActivityUser> activityUsers = new ArrayList<>();
            for (Long assignToUserId : updateActivityRequest.getAddAssignToUserIds()) {
                ActivityUser activityAssignee = new ActivityUser();
                activityAssignee.setActivityId(updateActivityRequest.getId());
                activityAssignee.setUserId(assignToUserId);
                activityUsers.add(activityAssignee);
            }
            activityUserRepository.saveAll(activityUsers);
        }

        // Update activity belong to
        if (updateActivityRequest.getDeleteBelongToIds() != null) {
            for (Long belongToId : updateActivityRequest.getDeleteBelongToIds()) {
                activityBelongToRepository.deleteById(belongToId);
            }
        }

        if (updateActivityRequest.getAddBelongToIds() != null) {
            List<ActivityBelongTo> activityBelongTos = new ArrayList<>();
            for (Long belongToId : updateActivityRequest.getAddBelongToIds()) {
                ActivityBelongTo activityBelongTo = new ActivityBelongTo();
                activityBelongTo.setActivityId(updateActivityRequest.getId());
                activityBelongTo.setBelongToModule(updateActivityRequest.getBelongToModule());
                activityBelongTo.setBelongToId(belongToId);
                activityBelongTos.add(activityBelongTo);
            }
            activityBelongToRepository.saveAll(activityBelongTos);
        }

        ActivityView activityView = activityMapper.toActivityView(activity);
        return new Response<ActivityView>(StatusCode.OK, "Update activity successfully", activityView);

    }

    @Override
    @Transactional
    public Response<List<Long>> deleteActivities(List<Long> activityIds) {
        try{
            List<Long> listNotFound = new ArrayList<>();
            for(Long activityId : activityIds){
                Optional<Activity> optActivity = activityRepository.findById(activityId);
                if(optActivity.isEmpty()){
                    listNotFound.add(activityId);
                }
            }

            if(!listNotFound.isEmpty()){
                return new Response<List<Long>>(StatusCode.BAD_REQUEST,  "Activity not found", listNotFound);
            }

            for(Long activityId : activityIds){
                activityRepository.deleteById(activityId);
            }
                

            return new Response<List<Long>>(StatusCode.OK,  "Delete activity successfully", activityIds);
        }catch(Exception e){
            return new Response<List<Long>>(StatusCode.INTERNAL_SERVER_ERROR, "Internal server error", null);
        }
    }

    @Override
    @Transactional
    public Response<ActivityKanbanResponse> findKanban(String filters, String groupBy, String search, Long accountId, String language) {

        
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("title");
        searchFields.put("activity_type");
        searchFields.put("priority");
        searchFields.put("deadline");
        searchFields.put("status");
        searchFields.put("call_time");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            searchFields.put(groupBy);
        }else{
            searchFields.put("status");
        }

        // Get activities in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "activity");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("title");
        viewFields.put("activity_type");
        viewFields.put("priority");
        viewFields.put("deadline");
        viewFields.put("status");
        viewFields.put("call_time");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            viewFields.put(groupBy);
        }else{
            viewFields.put("status");
        }   

        jsonFilter.put("view_fields", viewFields);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        Long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("ACTIVITY");
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }
                List<Object[]> groupValues = activityRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        System.out.println("-----JsonFilter-----" + jsonFilter.toString());

        List<Long> countByFilter = activityRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
       
        System.out.println(jsonFilter.toString());
        
        
        List<ActivityKanbanView> products = new ArrayList<>();
        try {
            List<Object[]> activityObjects = activityRepository.findKanban(jsonFilter.toString());
            for (Object[] activityObject : activityObjects) {
                ActivityKanbanView activityKanbanView = new ActivityKanbanView();
                activityKanbanView.setId((Long) activityObject[0]);
                activityKanbanView.setTitle((String) activityObject[1]);
                activityKanbanView.setType((String) activityObject[2]);
                activityKanbanView.setStatus((String) activityObject[3]);
                activityKanbanView.setDeadline((Date) activityObject[4]);
                activityKanbanView.setPriority((String) activityObject[5]);
                activityKanbanView.setCallTime((Integer) activityObject[6]);
                activityKanbanView.setGroupField((String) activityObject[7]);
                products.add(activityKanbanView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<ActivityKanbanResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        ActivityKanbanResponse activityKanbanResponse = new ActivityKanbanResponse();
        activityKanbanResponse.setGroupItems(groupItems);
        activityKanbanResponse.setActivities(products);
        return new Response<ActivityKanbanResponse>(StatusCode.OK, "Get activities kanban success!!!", activityKanbanResponse);
    }

    @Override
    public Response<ActivitySplitViewResponse> findSplitViewData(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        
        JSONArray searchFields = new JSONArray();
        searchFields.put("title");
        searchFields.put("activity_type");
        searchFields.put("priority");
        searchFields.put("deadline");
        searchFields.put("status");
        searchFields.put("call_time");

        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "activity");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("title");
        viewFields.put("activity_type");
        viewFields.put("priority");
        viewFields.put("deadline");
        viewFields.put("status");
        viewFields.put("call_time");
        jsonFilter.put("view_fields", viewFields);
       
        System.out.println("-----JsonFilter-----" + jsonFilter.toString());
        
        List<ActivitySplitView> activities = new ArrayList<>();
        try {
            List<Object[]> activityObjects = activityRepository.findSplitViewData(jsonFilter.toString());
            for (Object[] activityObject : activityObjects) {
                ActivitySplitView activitySplitView = new ActivitySplitView();
                activitySplitView.setId((Long) activityObject[0]);
                activitySplitView.setTitle((String) activityObject[1]);
                activitySplitView.setType((String) activityObject[2]);
                activitySplitView.setStatus((String) activityObject[3]);
                activitySplitView.setDeadline((Date) activityObject[4]);
                activitySplitView.setPriority((String) activityObject[5]);
                activitySplitView.setCallTime((Integer) activityObject[6]);
                activities.add(activitySplitView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<ActivitySplitViewResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        // Get Total rows
        List<Long> countByFilter = activityRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        Page<ActivitySplitView> activitiesPage = new PageImpl<>(activities, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        ActivitySplitViewResponse activitySplitViewResponse = new ActivitySplitViewResponse();
        activitySplitViewResponse.setActivities(activitiesPage);
        return new Response<ActivitySplitViewResponse>(StatusCode.OK, "Get activities split view success!!!", activitySplitViewResponse);
    }

    @Override
    public Response<Boolean> updateKanbanActivity(Long activityId, String fieldName, String fieldValue, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "activity");
        jsonFilter.put("id", activityId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);
        
        activityRepository.updateKanbanData(jsonFilter.toString());

        return new Response<Boolean>(StatusCode.OK, "Update activities split view success!!!", true);
    }

    @Override
    public Response<AttachedActivitiesViewResponse> getAttachedActivities(Long moduleId, String moduleCode, String type, int page, int limit, Long accountId, String language) {
        Page<Activity> attachedActivities;

        if ("LEAD".equalsIgnoreCase(moduleCode) || "CONTACT".equalsIgnoreCase(moduleCode)) {
            attachedActivities = new PageImpl<>(
                new ArrayList<>(),
                PageRequest.of(page, limit),
                0
            );
            // if (type != null && !type.trim().equalsIgnoreCase("")) {
            //     attachedActivities = activityRepository.findByBelongToIdAndBelongToModuleAndType(
            //             moduleId,
            //             moduleCode,
            //             type,
            //             PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
            //     );
            // } else {
            //     attachedActivities = activityRepository.findByBelongToIdAndBelongToModule(
            //             moduleId,
            //             moduleCode,
            //             PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
            //     );
            // }
        } else {
            if (type != null && !type.trim().equalsIgnoreCase("")) {
                attachedActivities = activityRepository.findByRelatedToIdAndRelatedToModuleAndType(
                        moduleId,
                        moduleCode,
                        type,
                        PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
                );
            } else {
                attachedActivities = activityRepository.findByRelatedToIdAndRelatedToModule(
                        moduleId,
                        moduleCode,
                        PageRequest.of(page, limit, Sort.by(Sort.Direction.DESC, "createdAt"))
                );
            }
        }
        long totalRows = attachedActivities.getTotalElements();

        List<AttachedActivityShortView> attachedActivityShortViews = activityMapper.toAttachedActivityShortViews(attachedActivities.getContent());
        Page<AttachedActivityShortView> activityViewPage = new PageImpl<>(
                attachedActivityShortViews,
                PageRequest.of(page, limit),
                totalRows
        );

        AttachedActivitiesViewResponse response = new AttachedActivitiesViewResponse();
        response.setAttachedActivities(activityViewPage);

        return new Response<>(StatusCode.OK, "Get attached activities successfully", response);
    }

    @Override
    public List<AttachedActivityShortView> getAttachedActivitiesByModuleAndType(Long moduleId, String moduleCode, String type, Long accountId, String language) {
        List<Activity> attachedActivities;

        if ("LEAD".equalsIgnoreCase(moduleCode) || "CONTACT".equalsIgnoreCase(moduleCode)) {
            attachedActivities = new ArrayList<>();
            if (type != null && !type.trim().equalsIgnoreCase("")) {
                attachedActivities = activityRepository.findByBelongToIdAndBelongToModuleAndType(
                        moduleId,
                        moduleCode,
                        type
                );
            } else {
                attachedActivities = activityRepository.findByBelongToIdAndBelongToModule(
                        moduleId,
                        moduleCode
                );
            }
        } else {
            if (type != null && !type.trim().equalsIgnoreCase("")) {
                attachedActivities = activityRepository.findByRelatedToIdAndRelatedToModuleAndType(
                        moduleId,
                        moduleCode,
                        type
                );
            } else {
                attachedActivities = activityRepository.findByRelatedToIdAndRelatedToModule(
                        moduleId,
                        moduleCode
                );
            }
        }

        return activityMapper.toAttachedActivityShortViews(attachedActivities);
    }

}
