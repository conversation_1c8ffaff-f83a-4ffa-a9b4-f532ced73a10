package com.tti.oh_crm_service.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.entity.OrganizationLevelLabelView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.model.OrganizationLevelLabel;
import com.tti.oh_crm_service.model.OrganizationLevelLabelId;
import com.tti.oh_crm_service.repository.OrganizationLevelLabelRepository;

@Service
public class OrganizationLevelLabelServiceImpl implements OrganizationLevelLabelService {
    
    @Autowired
    private OrganizationLevelLabelRepository organizationLevelLabelRepository;
    
    @Override
    public List<OrganizationLevelLabelView> getOrganizationLevelLabels(String language) {
        List<OrganizationLevelLabel> labels = organizationLevelLabelRepository.findByIdLanguageOrderByIdLevel(language);
        List<OrganizationLevelLabelView> labelViews = labels.stream()
            .map(this::toView)
            .collect(Collectors.toList());
        return labelViews;
    }

    @Override
    public OrganizationLevelLabelView getOrganizationLevelLabel(Integer level, String language) {
        OrganizationLevelLabel label = organizationLevelLabelRepository.findByIdLevelAndIdLanguage(level, language)
            .orElseThrow(() -> new IllegalArgumentException("Organization level label not found for level: " + level + " and language: " + language));
        return toView(label);
        
    }
    
    @Override
    public OrganizationLevelLabelView createOrganizationLevelLabel(Integer level, String language, String label) {
        OrganizationLevelLabel newLabel = new OrganizationLevelLabel(level, language, label);
        newLabel = organizationLevelLabelRepository.save(newLabel);
        return toView(newLabel);
    }
    
    @Override
    public OrganizationLevelLabelView updateOrganizationLevelLabel(Integer level, String language, String label) {
        OrganizationLevelLabelId id = new OrganizationLevelLabelId(level, language);
        Optional<OrganizationLevelLabel> optLabel = organizationLevelLabelRepository.findById(id);
        if (optLabel.isPresent()) {
            OrganizationLevelLabel existingLabel = optLabel.get();
            existingLabel.setLabel(label);
            existingLabel = organizationLevelLabelRepository.save(existingLabel);
            return toView(existingLabel);
        } else {
            throw new IllegalArgumentException("Organization level label not found for level: " + level + " and language: " + language);
        }
    }
    
    @Override
    public String deleteOrganizationLevelLabel(Integer level, String language) {
        OrganizationLevelLabelId id = new OrganizationLevelLabelId(level, language);
        if (organizationLevelLabelRepository.existsById(id)) {
            organizationLevelLabelRepository.deleteById(id);
            return "Organization level label deleted successfully.";
        } else {
            throw new IllegalArgumentException("Organization level label not found for level: " + level + " and language: " + language);
        }
    }
    
    private OrganizationLevelLabelView toView(OrganizationLevelLabel label) {
        OrganizationLevelLabelView view = new OrganizationLevelLabelView();
        view.setLevel(label.getLevel());
        view.setLanguage(label.getLanguage());
        view.setLabel(label.getLabel());
        return view;
    }
}
