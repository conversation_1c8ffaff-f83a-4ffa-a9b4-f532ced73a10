package com.tti.oh_crm_service.service;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import com.tti.oh_crm_service.mapper.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.client.CommonFeignClient;
import com.tti.oh_crm_service.config.TenantContextHolder;
import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.utils.CodeGeneratorUtils;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.ServiceUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;

import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@Service("leadService")
public class LeadServiceImpl implements LeadService {

    // @Autowired
    // private FileService fileService;
    @Autowired
    private ActivityService activityService;
    @Autowired
    private ServiceUtils serviceUtils;

    @Autowired
    private LeadMapper leadMapper;
    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private ContactMapper contactMapper;
    @Autowired
    private OpportunityMapper opportunityMapper;
    @Autowired
    private CampaignMapper campaignMapper;

    @Autowired
    private CommonFeignClient commonFeignClient;

    @Autowired
    private LeadRepository leadRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;
    @Autowired
    private TblListValueRepository tblListValueRepository;
    @Autowired
    private CampaignLeadRepository campaignLeadRepository;
    @Autowired
    private CampaignRepository campaignRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private OpportunityRepository opportunityRepository;
    @Autowired
    private ActivityRepository activityRepository;
    @Autowired
    private ActivityBelongToRepository activityBelongToRepository;
    @Autowired
    private HistoryRepository historyRepository;
    @Autowired
    private CoreQueryService<Lead> coreQueryService;

    @Override
    public Response<LeadTableResponse> getLeadsTable(
        String filters, 
        String groupBy, 
        String groupValue, 
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit,
        Long accountId, 
        String language
    ) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "salutation",
                "first_name",
                "last_name",
                "email",
                "phone",
                "mobile",
                "company",
                "title",
                "website",
                "description",
                "street"
        );

        GenericTableResponse<Lead> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "leads", searchFields, "LEAD",
                leadRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }
        
        Page<Lead> leadsPage = tableResponse.getData();
        List<LeadView> leadViews = leadMapper.toLeadView(leadsPage.getContent());
        Page<LeadView> leadViewPage = new PageImpl<>(leadViews, leadsPage.getPageable(), leadsPage.getTotalElements());
        
        LeadTableResponse leadTableResponse = new LeadTableResponse();
        leadTableResponse.setGroupItems(tableResponse.getGroupItems());
        leadTableResponse.setLeads(leadViewPage);
        return new Response<LeadTableResponse>(StatusCode.OK, "Get leads table success!!!", leadTableResponse);
    }

    @Override
    @Transactional
    public Response<LeadView> createLead(CreateLeadRequest request, Long accountId, String language) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Lead CREATE");

        Lead newLead = new Lead();
        leadMapper.create(request, newLead);

        // Get "owner"
        Optional<User> optOwner = userRepository.findById(request.getOwnerId());
        if (optOwner.isPresent()) {
            newLead.setOwner(optOwner.get());
        }

        newLead.setCreatedBy(optUser.get());
        newLead.setLastModifiedBy(optUser.get());

        // Save lead - HistoryEntityListener will automatically handle CREATE history
        newLead = leadRepository.save(newLead);

        LeadView leadView = leadMapper.toLeadView(newLead);
        return new Response<>(StatusCode.OK, "Create lead success!!!", leadView);
    }

    @Override
    @Transactional
    public Response<LeadView> updateLead(UpdateLeadRequest request, Long accountId, String language) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Optional<Lead> optLead = leadRepository.findById(request.getId());
        if (optLead.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Lead not found!!!");
        }

        Lead lead = optLead.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());
        log.info("Set user context for update: {}", optUser.get().getId());

        // Step 1: Clone the original entity for history tracking before any modifications
        Lead originalLead = EntityCloneUtils.cloneEntityForHistory(lead);
        EntityContext.setOriginalEntity(originalLead);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Lead UPDATE");

        // Step 2: Apply updates to the entity
        leadMapper.update(request, lead);

        // Step 2.5: Handle relationship updates (if any)
        updateLeadRelationships(request, lead);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(lead);

        lead.setLastModifiedBy(optUser.get());

        // Save lead - HistoryEntityListener will automatically handle UPDATE history with field changes
        lead = leadRepository.save(lead);

        LeadView leadView = leadMapper.toLeadView(lead);
        return new Response<>(StatusCode.OK, "Update lead success!!!", leadView);
    }

    @Override
    @Transactional
    public Response<Integer> deleteLeads(List<Long> ids) {
        // Check lead exist
        for (Long id : ids) {
            Optional<Lead> optLead = leadRepository.findById(id);
            if (!optLead.isPresent()) {
                return new Response<Integer>(StatusCode.BAD_REQUEST, "Lead not found!!!");
            }
        }
        Integer result = leadRepository.deleteByIdIn(ids);
        return new Response<Integer>(StatusCode.OK, "Delete leads success!!!", result);
    }

    @Override
    public Response<LeadDetails> getLeadDetails(Long leadId, Long accountId, String language) {
        Optional<Lead> optLead = leadRepository.findById(leadId);
        if (optLead.isPresent()) {
            Lead lead = optLead.get();
            LeadDetails leadDetails = leadMapper.toLeadDetails(lead);
            
            // get attached activities for a lead
            List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(leadId, "LEAD", null, accountId, language);
            leadDetails.setAttachedActivities(attachedActivities);
            List<LeadCampaignView> leadCampaignViews = new ArrayList<>();
            List<CampaignLead> campaignLeads = campaignLeadRepository.findByLeadId(leadId);
            for (CampaignLead campaignLead : campaignLeads) {
                Optional<Campaign> optCampaign = campaignRepository.findById(campaignLead.getCampaignId());
                if (!optCampaign.isPresent()) {
                    continue;
                }
                LeadCampaignView leadCampaignView = campaignMapper.toLeadCampaignView(optCampaign.get());
                leadCampaignView.setMemberStatus(campaignLead.getMemberStatus());
                leadCampaignViews.add(leadCampaignView);
            }
            leadDetails.setCampaigns(leadCampaignViews);
            return new Response<LeadDetails>(StatusCode.OK, "Get lead details success!!!", leadDetails);
        } else {
            return new Response<LeadDetails>(StatusCode.BAD_REQUEST, "Lead not found!!!");
        }
    }

    @Override
    public Response<LeadKanbanResponse> getLeadsKanban(
        String filters,
        String groupBy,
        String search, 
        Long accountId, 
        String language
    ) {
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("salutation");
        searchFields.put("first_name");
        searchFields.put("last_name");
        searchFields.put("email");
        searchFields.put("phone");
        searchFields.put("mobile");
        searchFields.put("company");
        searchFields.put("title");
        searchFields.put("website");
        searchFields.put("description");
        searchFields.put("street");
        searchFields.put("zip_postal_code");

        // Get leads in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "leads");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("salutation");
        viewFields.put("first_name");
        viewFields.put("last_name");
        viewFields.put("phone");
        viewFields.put("mobile");
        viewFields.put("company");
        viewFields.put("title");
        jsonFilter.put("view_fields", viewFields);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        Long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("LEAD");
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }
                List<Object[]> groupValues = leadRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        List<Long> countByFilter = leadRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
       
        System.out.println(jsonFilter.toString());
        
        List<LeadKanbanView> leads = new ArrayList<>();
        try {
            List<Object[]> leadObjects = leadRepository.findKanban(jsonFilter.toString());
            for (Object[] leadObject : leadObjects) {
                LeadKanbanView leadKanbanView = new LeadKanbanView();
                leadKanbanView.setId((Long) leadObject[0]);
                leadKanbanView.setSalutation((String) leadObject[1]);
                leadKanbanView.setFirstName((String) leadObject[2]);
                leadKanbanView.setLastName((String) leadObject[3]);
                leadKanbanView.setPhone((String) leadObject[4]);
                leadKanbanView.setMobile((String) leadObject[5]);
                leadKanbanView.setCompany((String) leadObject[6]);
                leadKanbanView.setTitle((String) leadObject[7]);
                leadKanbanView.setGroupField((String) leadObject[8]);
                leads.add(leadKanbanView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<LeadKanbanResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        LeadKanbanResponse leadKanbanResponse = new LeadKanbanResponse();
        leadKanbanResponse.setGroupItems(groupItems);
        leadKanbanResponse.setLeads(leads);
        return new Response<LeadKanbanResponse>(StatusCode.OK, "Get leads kanban success!!!", leadKanbanResponse);
    }

    @Override
    public Response<LeadSplitViewResponse> getLeadsSplitView(String filters, String search, String sortBy, String sortDirection, int page,
            int limit, Long accountId, String language) {
        
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("salutation");
        searchFields.put("first_name");
        searchFields.put("last_name");
        searchFields.put("email");
        searchFields.put("phone");
        searchFields.put("mobile");
        searchFields.put("company");
        searchFields.put("title");
        searchFields.put("website");
        searchFields.put("description");
        searchFields.put("street");
        searchFields.put("zip_postal_code");

        // Get leads in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "leads");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("salutation");
        viewFields.put("first_name");
        viewFields.put("last_name");
        viewFields.put("phone");
        viewFields.put("mobile");
        viewFields.put("company");
        viewFields.put("title");
        jsonFilter.put("view_fields", viewFields);
       
        System.out.println(jsonFilter.toString());
        
        List<LeadSplitView> leads = new ArrayList<>();
        try {
            List<Object[]> leadObjects = leadRepository.findSplitViewData(jsonFilter.toString());
            for (Object[] leadObject : leadObjects) {
                LeadSplitView leadSplitView = new LeadSplitView();
                leadSplitView.setId((Long) leadObject[0]);
                leadSplitView.setSalutation((String) leadObject[1]);
                leadSplitView.setFirstName((String) leadObject[2]);
                leadSplitView.setLastName((String) leadObject[3]);
                leadSplitView.setPhone((String) leadObject[4]);
                leadSplitView.setMobile((String) leadObject[5]);
                leadSplitView.setCompany((String) leadObject[6]);
                leadSplitView.setTitle((String) leadObject[7]);
                leads.add(leadSplitView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<LeadSplitViewResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        // Get Total rows
        List<Long> countByFilter = leadRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        Page<LeadSplitView> leadsPage = new PageImpl<>(leads, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        LeadSplitViewResponse leadSplitViewResponse = new LeadSplitViewResponse();
        leadSplitViewResponse.setLeads(leadsPage);
        return new Response<LeadSplitViewResponse>(StatusCode.OK, "Get leads split view success!!!", leadSplitViewResponse);
    }

    @Override
    @Transactional
    public Response<Boolean> updateKanbanLead(Long leadId, String fieldName, String fieldValue, Long accountId,
            String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "leads");
        jsonFilter.put("id", leadId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);
        
        leadRepository.updateKanbanData(jsonFilter.toString());

        return new Response<Boolean>(StatusCode.OK, "Update lead kanban success!!!", true);
    }

    @Override
    public Mono<ResponseEntity<Response<ImportLeadResponse>>> importLeads(FilePart dataFile, ImportLeadRequest request, Long accountId,
                                                                          String language) {
        if (dataFile == null) {
            return Mono.empty();
        }
        
        return DataBufferUtils.join(dataFile.content())
                .publishOn(Schedulers.boundedElastic())
                .flatMap(data -> {
                    try {
                        Workbook workbook = WorkbookFactory.create(data.asInputStream());
                        Sheet sheet = workbook.getSheetAt(0);
                        int noOfRowsData = sheet.getLastRowNum();
                        System.out.println("No of rows (not including header): " + noOfRowsData);

                        List<List<String>> errors = new ArrayList<>();

                        // Ensure required columns are present
                        if (!verifyRequiredColumns(request)) {
                            return Mono.just(ResponseEntity.ok(new Response<ImportLeadResponse>(
                                    StatusCode.BAD_REQUEST, "Missing required column mappings.", null)));
                        }

                        // Build leads list reactively
                        List<Mono<Lead>> leadMonos = new ArrayList<>();
                        for (int i = 1; i <= noOfRowsData; i++) {
                            Row row = sheet.getRow(i);
                            if (!isRowValid(row, request)) {
                                System.out.println("Row " + i + " is invalid");
                                errors.add(extractRowErrors(row));
                                continue;
                            }

                            Mono<Lead> leadMono = buildLeadFromRow(row, request, language);
                            if (leadMono != null) {
                                leadMonos.add(leadMono);
                            } else {
                                System.out.println("Lead " + i + " is null");
                                errors.add(extractRowErrors(row));
                            }
                        }

                        // Combine all lead monos and save them
                        if (leadMonos.isEmpty()) {
                            return Mono.just(ResponseEntity.ok(new Response<>(
                                    StatusCode.OK, "No valid leads to import.", new ImportLeadResponse(0, noOfRowsData, errors))));
                        }

                        return Mono.zip(leadMonos, objects -> {
                            List<Lead> leads = new ArrayList<>();
                            for (Object obj : objects) {
                                if (obj instanceof Lead) {
                                    leads.add((Lead) obj);
                                }
                            }
                            return leads;
                        }).flatMap(savedLeads -> {
                            // Wrap the save operation with tenant context to ensure proper database routing
                            return TenantContextHolder.withTenantContext(
                                Mono.fromCallable(() -> {
                                    String saveTenant = TenantContextHolder.getCurrentTenant();
                                    log.info("Current tenant for saving {} leads: {}", savedLeads.size(), saveTenant);
                                    
                                    for (Lead lead : savedLeads) {
                                        log.info("Saving lead: {}", lead);
                                    }
                                    
                                    leadRepository.saveAll(savedLeads);
                                    log.info("Successfully saved {} leads to tenant: {}", savedLeads.size(), saveTenant);
                                    
                                    return ResponseEntity.ok(new Response<>(
                                            StatusCode.OK, "Leads imported successfully.", 
                                            new ImportLeadResponse(savedLeads.size(), noOfRowsData, errors)));
                                })
                                .onErrorResume(e -> {
                                    log.error("Error saving leads to database", e);
                                    return Mono.just(ResponseEntity.ok(new Response<>(
                                            StatusCode.BAD_REQUEST, "Error saving leads: " + e.getMessage(), null)));
                                })
                            );
                        });

                    } catch (Exception e) {
                        log.error("Error reading data file", e);
                        return Mono.just(ResponseEntity.ok(new Response<>(
                                StatusCode.BAD_REQUEST, "Error reading data file.", null)));
                    }
                });
    }

    @Override
    public Response<LeadView> updateLeadTags(Long leadId, String tags) {
        // Check lead and update tags
        Optional<Lead> optLead = leadRepository.findById(leadId);
        if (!optLead.isPresent()) {
            return new Response<LeadView>(StatusCode.BAD_REQUEST, "Lead is not existed!!!");
        }

        Lead lead = optLead.get();
        lead.setTags(tags);
        leadRepository.save(lead);
        return new Response<LeadView>(StatusCode.OK, "Update lead tags success!!", leadMapper.toLeadView(lead));
    }

    @Override
    @Transactional
    public Response<LeadConversionResponse> convertLead(LeadConversionRequest request, Long accountId, String language) {
        // Find User by Account ID
        User user = userRepository.findByAccountId(accountId)
                .orElseThrow(() -> new RuntimeException("Requesting User not found!!!"));

        // Find Lead
        Lead lead = leadRepository.findById(request.getLeadId())
                .orElseThrow(() -> new RuntimeException("Lead not found!!!"));

        // Find Owner
        User owner = userRepository.findById(request.getOwnerId())
                .orElseThrow(() -> new RuntimeException("Owner not found!!!"));

        // Validate Conversion Requests
        if (request.getAccountConversion() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account conversion request not found!!!");
        }
        if (request.getContactConversion() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact conversion request not found!!!");
        }
        if (request.getOpportunityConversion() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity conversion request not found!!!");
        }

        // Validate Existing Account, Contact, or Opportunity Links
        validateExistingEntities(request);

        // Convert Lead to Account
        Account account = handleAccountConversion(request, owner);

        // Convert Lead to Contact
        Contact contact = handleContactConversion(request, user, owner, account);

        // Find all activities associated with the lead
        List<Activity> activities = activityRepository.findByBelongToIdAndBelongToModule(lead.getId(), "LEAD");
        
        for (Activity activity : activities) {
            // update belong to module and id from lead to contact if this activity only belongs to a unique lead (current lead)
            List<ActivityBelongTo> activityBelongToList = activityBelongToRepository.findByActivityIdAndBelongToModule(activity.getId(), "LEAD");
            if (activityBelongToList.size() == 1) {
                ActivityBelongTo activityBelongTo = activityBelongToList.get(0);
                activityBelongTo.setBelongToModule("CONTACT");
                activityBelongTo.setBelongToId(contact.getId());
                activityBelongToRepository.save(activityBelongTo);
            }
            else if (activityBelongToList.size() > 1) {
                // if the activity belongs to multiple leads, we need to remove the current lead from the activity
                for (ActivityBelongTo activityBelongTo : activityBelongToList) {
                    if (activityBelongTo.getBelongToId().equals(lead.getId())) {
                        activityBelongToRepository.delete(activityBelongTo);
                    }
                }
            }

            // because 1 activity only related to 1 account/opportunity
            // so if the activity is not related to any account/opportunity
            // we can link it to the converted account by default
            //Link to Converted Account
            if (activity.getRelatedToId() == null) {
                activity.setRelatedToId(account.getId());
                activity.setRelatedToModule("ACCOUNT");
            }

            activityRepository.save(activity);
        }

        // Convert Lead to Opportunity
        Opportunity opportunity = handleOpportunityConversion(request, user, owner, account, lead.getSource());

        // Delete the Lead after all conversions are successful
        leadRepository.delete(lead);
        
        LeadConversionResponse response = new LeadConversionResponse(
                accountMapper.toAccountShortView(account),
                contactMapper.toContactShortView(contact),
                opportunityMapper.toOpportunityShortView(opportunity)
        );

        return new Response<>(StatusCode.OK, "Lead conversion successful!!!", response);
    }

    private boolean verifyRequiredColumns(ImportLeadRequest request) {
        List<String> requiredColumns = List.of("first_name", "last_name");
        return new HashSet<>(request.getColumnMappings()).containsAll(requiredColumns);
    }

    private boolean isRowValid(Row row, ImportLeadRequest request) {
        
        if (row == null) {
            return false;
        }
        for (int j = 0; j < request.getColumnMappings().size(); j++) {
            String column = request.getColumnMappings().get(j);
            if ((column.equals("first_name") || column.equals("last_name")) &&
                    (row.getCell(j) == null || row.getCell(j).toString().trim().isEmpty())) {
                return false;
            }
        }
        return true;
    }

    private List<String> extractRowErrors(Row row) {
        List<String> errors = new ArrayList<>();
        if (row == null) {
            return errors;
        }
        for (int j = 0; j < row.getLastCellNum(); j++) {
            Cell cell = row.getCell(j);
            errors.add(cell == null ? "" : cell.toString());
        }
        return errors;
    }

    private Mono<Lead> buildLeadFromRow(Row row, ImportLeadRequest request, String language) {
        return TenantContextHolder.withTenantContext(
            Mono.fromCallable(() -> {
                Lead lead = new Lead();
                String countryNameInput = null;
                String provinceNameInput = null;
                String districtNameInput = null;
                String wardNameInput = null;
                
                for (int j = 0; j < request.getColumnMappings().size(); j++) {
                    String column = request.getColumnMappings().get(j);
                    String cellValue = getCellValueAsString(row.getCell(j));
                    switch (column) {
                        // case country or country_code 
                        case "country" -> countryNameInput = cellValue;
                        case "country_code" -> countryNameInput = cellValue;
                        case "province" -> provinceNameInput = cellValue;
                        case "province_code" -> provinceNameInput = cellValue;
                        case "district" -> districtNameInput = cellValue;
                        case "district_code" -> districtNameInput = cellValue;
                        case "ward" -> wardNameInput = cellValue;
                        case "ward_code" -> wardNameInput = cellValue;
                        default -> mapColumnToLeadField(lead, column, cellValue, language);
                    }
                }
                return new Object[] { lead, countryNameInput, provinceNameInput, districtNameInput, wardNameInput };
            })
            .flatMap(data -> {
                Object[] leadData = (Object[]) data;
                Lead lead = (Lead) leadData[0];
                String countryNameInput = (String) leadData[1];
                String provinceNameInput = (String) leadData[2];
                String districtNameInput = (String) leadData[3];
                String wardNameInput = (String) leadData[4];
                
                // First set the owner reactively
                return mapOwnerReactive(lead, request.getOwner())
                    .flatMap(leadWithOwner -> {
                        // Then make the address API call
                        Map<String, String> header = new HashMap<>();
                        header.put("Accept-Language", language);
                        log.info("Header: {}", header);
                        log.info("Country: {}", countryNameInput);
                        log.info("Province: {}", provinceNameInput);
                        log.info("District: {}", districtNameInput);
                        log.info("Ward: {}", wardNameInput);
                        
                        return commonFeignClient.getCommonAddressByNames(header, countryNameInput, provinceNameInput, districtNameInput, wardNameInput)
                            .map(response -> {
                                if (response.getCode() == 200) {
                                    CountryView countryView = response.getData().getCountry();
                                    ProvinceView provinceView = response.getData().getProvince();
                                    DistrictView districtView = response.getData().getDistrict();
                                    WardView wardView = response.getData().getWard();
                                    if (countryView != null) {
                                        leadWithOwner.setCountryCode(countryView.getIso());
                                    }
                                    if (provinceView != null) {
                                        leadWithOwner.setProvinceCode(provinceView.getCode());
                                    }
                                    if (districtView != null) {
                                        leadWithOwner.setDistrictCode(districtView.getCode());
                                    }
                                    if (wardView != null) {
                                        leadWithOwner.setWardCode(wardView.getCode());
                                    }
                                }
                                System.out.println("Country code: " + leadWithOwner.getCountryCode());
                                System.out.println("Province code: " + leadWithOwner.getProvinceCode());
                                System.out.println("District code: " + leadWithOwner.getDistrictCode());
                                System.out.println("Ward code: " + leadWithOwner.getWardCode());
                                return leadWithOwner;
                            })
                            .doOnError(error -> {
                                System.err.println("Address Error occurred: " + error.getMessage());
                                log.error("Stack trace: ", error);
                            })
                            .onErrorReturn(leadWithOwner); // Return lead even if address lookup fails
                    });
            })
            .onErrorResume(e -> {
                log.error("Error building lead from row: ", e);
                return Mono.empty(); // Return empty if there's an error
            })
        );
    }

    private String getCellValueAsString(Cell cell) throws UnsupportedEncodingException {
        if (cell == null) return "";
        return switch (cell.getCellType()) {
            case STRING -> URLDecoder.decode(cell.getStringCellValue().trim(), StandardCharsets.UTF_8);
            case NUMERIC -> String.valueOf((int) cell.getNumericCellValue());
            default -> "";
        };
    }

    private Mono<Lead> mapOwnerReactive(Lead lead, Long ownerId) {
        return TenantContextHolder.withTenantContext(
            Mono.fromCallable(() -> {
                userRepository.findById(ownerId).ifPresent(lead::setOwner);
                return lead;
            })
        );
    }

    private void mapColumnToLeadField(Lead lead, String column, String value, String language) {
        switch (column) {
            case "salutation" -> lead.setSalutation(value);
            case "first_name" -> lead.setFirstName(value);
            case "last_name" -> lead.setLastName(value);
            case "description" -> lead.setDescription(value);
            case "email" -> lead.setEmail(value);
            case "phone" -> lead.setPhone(value);
            case "mobile" -> lead.setMobile(value);
            case "company" -> lead.setCompany(value);
            case "website" -> lead.setWebsite(value);
            case "title" -> lead.setTitle(value);
            case "no_of_employees" -> lead.setNoOfEmployees(Integer.parseInt(value.isEmpty() ? "0" : value));
            case "street" -> lead.setStreet(value);
            case "zip_postal_code" -> lead.setZipPostalCode(value);
            case "full_address" -> lead.setFullAddress(value);
            // for "status", "rating", "industry", "source"
            default -> setListValueField(lead, column, value, language);
        }
    }
    

    private void setListValueField(Lead lead, String column, String value, String language) {
        String listCode = CodeGeneratorUtils.generateListCode("LEAD", column);
        tblListValueRepository.findByListCodeAndLabelAndLanguage(listCode, value, language)
                .ifPresent(tblListValue -> {
                    switch (column) {
                        case "status" -> lead.setStatus(tblListValue.getValue());
                        case "rating" -> lead.setRating(tblListValue.getValue());
                        case "industry" -> lead.setIndustry(tblListValue.getValue());
                        case "source" -> lead.setSource(tblListValue.getValue());
                    }
                });
    }

    private void validateExistingEntities(LeadConversionRequest request) {
        if (request.getAccountConversion().getExistingAccountId() != null) {
            if (request.getContactConversion().getExistingContactId() != null) {
                Contact contact = contactRepository.findById(request.getContactConversion().getExistingContactId())
                        .orElseThrow(() -> new RuntimeException("The existing contact does not found!!!"));
                if (!contact.getAccount().getId().equals(request.getAccountConversion().getExistingAccountId())) {
                    throw new RuntimeException("The existing contact does not belong to the existing account in the request!!!");
                }
            }
            if (request.getOpportunityConversion().getExistingOpportunityId() != null) {
                Opportunity opportunity = opportunityRepository.findById(request.getOpportunityConversion().getExistingOpportunityId())
                        .orElseThrow(() -> new RuntimeException("The existing opportunity does not found!!!"));
                if (!opportunity.getAccount().getId().equals(request.getAccountConversion().getExistingAccountId())) {
                    throw new RuntimeException("The existing opportunity does not belong to the existing account in the request!!!");
                }
            }
        }
    }

    private Account handleAccountConversion(LeadConversionRequest request, User owner) {
        if (request.getAccountConversion().getExistingAccountId() != null) {
            return accountRepository.findById(request.getAccountConversion().getExistingAccountId())
                    .orElseThrow(() -> new RuntimeException("Account not found!!!"));
        } else {
            Account account = accountMapper.create(request.getAccountConversion().getNewAccount());
            account.setOwner(owner);
            return accountRepository.save(account);
        }
    }

    private Contact handleContactConversion(LeadConversionRequest request, User user, User owner, Account account) {
        if (request.getContactConversion().getExistingContactId() != null) {
            return contactRepository.findById(request.getContactConversion().getExistingContactId())
                    .orElseThrow(() -> new RuntimeException("Contact not found!!!"));
        } else {
            Contact contact = new Contact();
            contactMapper.create(request.getContactConversion().getNewContact(), contact, new UserMapStructContext(user, owner));
            // set account that converted from lead to contact
            contact.setAccount(account);
            return contactRepository.save(contact);
        }
    }

    private Opportunity handleOpportunityConversion(LeadConversionRequest request, User user, User owner, Account account, String leadSource) {
        Opportunity opportunity = new Opportunity();
        if (request.getOpportunityConversion().getExistingOpportunityId() != null) {
            opportunity = opportunityRepository.findById(request.getOpportunityConversion().getExistingOpportunityId())
                    .orElseThrow(() -> new RuntimeException("Opportunity not found!!!"));

            // convert source from lead to opportunity leadSource
            if (leadSource != null && !leadSource.trim().equalsIgnoreCase("")) {
                opportunity.setLeadSource(leadSource);
            }
        } else {
            opportunityMapper.create(request.getOpportunityConversion().getNewOpportunity(), opportunity, new UserMapStructContext(user, owner));
            opportunity.setAccount(account);
            // convert source from lead to opportunity leadSource if leadSource from new opportunity is null
            if (request.getOpportunityConversion().getNewOpportunity() != null 
                && request.getOpportunityConversion().getNewOpportunity().getLeadSource() == null
                && leadSource != null && !leadSource.trim().equalsIgnoreCase("")
            ) {
                opportunity.setLeadSource(leadSource);
            }
        }
        
        opportunity = opportunityRepository.save(opportunity);
        return opportunity;
    }

    /**
     * Update lead relationships based on the provided IDs in the request

     */
    private void updateLeadRelationships(UpdateLeadRequest request, Lead lead) {

    }

}
