package com.tti.oh_crm_service.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.http.codec.multipart.FilePart;
import reactor.core.publisher.Mono;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.enumeration.EListType;

public interface SettingService {
    public Response<List<RoleView>> getRoles(Long accountId, String language);
    public Response<RoleView> getRoleDetails(Long roleId, Long accountId, String language);
    public Response<RoleView> createRole(CreateRoleRequest request, Long accountId, String language);
    public Response<RoleView> updateRole(UpdateRoleRequest request, Long accountId, String language);
    public Response<RoleView> deleteRoles(TransferAndDeleteRoleRequest request, Long accountId, String language);
    public Response<List<ProfileView>> getProfiles(Long accountId, String language);
    public Response<ProfileView> createProfile(CreateProfileRequest request, Long accountId, String language);
    public Response<Boolean> updateProfilePermissions(UpdateProfilePermissionsRequest request);
    public Response<List<ProfilePermissionView>> getProfilePermissions(Long profileId);
    public Response<ProfileView> updateProfile(UpdateProfileRequest request, Long accountId, String language);
    public Response<Boolean> deleteProfiles(List<Long> profileIds, Long accountId, String language);
    public Response<ModuleView> createModule(CreateModuleRequest request);
    public Response<List<ModuleView>> getModules(Long accountId, String language);
    public Response<ModuleView> updateModuleDisplayName(Long accountId, UpdateDisplayNameRequest request);
    public Response<List<ModuleView>> updateModules (Long accountId, List<ModuleView> modules);
    public Response<DepartmentView> createDepartment(CreateDepartmentRequest request, Long accountId, String language);
    public Response<DepartmentView> updateDepartment(UpdateDepartmentRequest request, Long accountId, String language);
    public Response<List<DepartmentView>> getDepartments();
    public Response<List<DepartmentViewTreeNode>> getDepartmentsTree(Long accountId, String language);
    public Response<DepartmentDetails> getDepartmentDetails(Long departmentId, Long accountId, String language);
    public Response<Boolean> deleteDepartment(TransferAndDeleteDepartmentRequest request, Long accountId, String language);
    public Mono<Response<UserView>> createUser(CreateUserRequest request, String authToken, Long organizationId, Long accountId, String language);
    public Response<UserView> updateUser(UpdateUserRequest request, Long accountId, String language);
    public Response<Boolean> deleteUser(Long userId, Long accountId, String language);
    public Response<UserDetails> getUserDetails(Long userId, Long accountId, String language);
    public Response<List<UserView>> getUsers();
    public Response<Page<UserView>> getUsersByRole(Long roleId, int page, int limit, String language);
    public Response<ModuleSettings> getModuleSettings(String moduleCode, String language);
    public Response<LayoutSettingsView> createLayoutSettings(CreateLayoutSettingsRequest request, Long accountId, String language);
    public Response<List<ListValueRelatedDataItem>> updateLayoutSettings(UpdateLayoutSettingsRequest request, Long accountId, String language);
    public Response<LayoutGroupSettingsView> createGroupSettings(CreateGroupSettingsRequest request, Long accountId, String language);
    public Response<LayoutGroupSettingsView> updateGroupSettings(UpdateGroupSettingsRequest request, Long accountId, String language);
    public Response<List<FieldSettingsView>> createFieldSettings(List<CreateFieldSettingsRequest> fieldSettingsRequests, Long accountId, String language);
    public Response<List<FieldSettingsView>> updateFieldSettings(List<UpdateFieldSettingsRequest> fieldSettingsRequests, Long accountId, String language);
    public Response<List<TblListView>> getListByType(EListType type, String language);
    public Response<List<TblListValueView>> getListOfValues(String listCode, String language);
    public Response<List<TblListValueView>> createOrUpdateListOfValuesForField(UpdateFieldListRequest request, Long accountId, String language);
    public Response<Integer> deleteFieldSettings(List<DeleteFieldSettingsRequest> fields, String language);
    public Response<List<LeadStatusSettingsViewFull>> getLeadStatusSettings(String language);
    public Response<Boolean> updateLeadStatusSettings(List<UpdateLeadStatusSettingsRequest> updateLeadStatusSettingsRequests, Long accountId);
    public Response<List<OpportunityStageSettingsViewFull>> getOpportunityStageSettings(String language);
    public Response<Boolean> updateOpportunityStageSettings(List<UpdateOpportunityStageSettingsRequest> updateOpportunityStageSettingsRequests, Long accountId);
    public Response<List<DataPermissionView>> getDataPermissions(Long accountId, String language);
    public Response<List<DataPermissionView>> createOrUpdateDataPermissions(List<CreateDataPermissionRequest> requests, Long accountId, String language);
    public Response<DataPermissionView> getDataPermissionByModuleCode(String moduleCode, Long accountId, String language);
    public Response<List<DataPermissionShareRuleView>> getDataPermissionShareRules(String moduleCode, Long accountId, String language);
    public Response<DataPermissionShareRuleView> createDataPermissionShareRule(CreateDataPermissionShareRuleRequest request, Long accountId, String language);
    public Response<DataPermissionShareRuleView> updateDataPermissionShareRule(UpdateDataPermissionShareRuleRequest request, Long accountId, String language);
    public Response<Boolean> deleteDataPermissionShareRule(List<Long> ruleIds);
    public Response<ImageUploadResponse> uploadImages(List<FilePart> imageFiles, Long accountId, String language);
    public Response<com.tti.oh_crm_service.entity.FileUploadResponse> uploadFiles(List<FilePart> files, String module, Long accountId, String language);
}
