package com.tti.oh_crm_service.service;

import java.util.List;
import java.util.Locale;

import com.tti.oh_crm_service.entity.*;

import com.tti.oh_crm_service.model.Account;
import io.jsonwebtoken.Claims;


public interface AccountService {
    public Response<AccountDetails> getAccountDetail(<PERSON>laims claims, Locale locale, Long id);
    public Response<AccountTableResponse> getAccounts(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<AccountView> createAccount(<PERSON>laims claims, CreateAccountRequest createAccountRequest, Locale locale);
    public Response<AccountView> updateAccount(<PERSON>laims claims, UpdateAccountRequest updateAccountRequest, Locale locale);
    public Response<List<Long>> deleteAccount(List<Long>  accountIds);
    public Response<AccountKanbanResponse> findKanban(String filters, String groupBy, String search, Long accountId, String language);
    public Response<AccountSplitViewResponse> findSplitViewData(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<Boolean> updateKanbanAccount(Long accountId, String fieldName, String fieldValue, String language);
    public Account findById(Long accountId);
    public AccountView getAccountView(Long accountId);
    public Response<ContactOpportunityListByAccountResponse> getContactOpportunityListByAccount(Long accountId);
    public Response<GenericTableResponse<ContactAttachedView>> getAttachedContactForAccount(Long accountId, int page, int limit, Long loginAccountId, String language);
    public Response<GenericTableResponse<OpportunityAttachedView>> getAttachedOpportunityForAccount(Long accountId, int page, int limit, Long loginAccountId, String language);
}
