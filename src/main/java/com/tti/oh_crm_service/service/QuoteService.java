package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Quote;

import java.util.List;

public interface QuoteService {
    Response<QuoteTableResponse> getQuotesTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    Response<QuoteView> createQuote(CreateQuoteRequest request, Long accountId, String language);
    Response<QuoteView> updateQuote(UpdateQuoteRequest request, Long accountId, String language);
    Response<Integer> deleteQuotes(List<Long> ids);
    Response<QuoteDetails> getQuoteDetails(Long quoteId, Long accountId, String language);
    Response<QuoteKanbanResponse> getQuotesKanban(String filters, String groupBy, String search, Long accountId, String language);
    Response<QuoteSplitViewResponse> getQuotesSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    Response<Boolean> updateKanbanQuote(Long quoteId, String fieldName, String fieldValue, Long accountId, String language);
    List<QuoteView> getQuotesByAccountId(Long accountId);
    Quote findById(Long id);
}