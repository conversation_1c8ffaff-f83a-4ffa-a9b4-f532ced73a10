package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.entity.QuoteAttachedView;
import com.tti.oh_crm_service.mapper.OpportunityMapper;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.OpportunityRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.repository.ContactOpportunityRoleRepository;
import com.tti.oh_crm_service.repository.QuoteRepository;
import com.tti.oh_crm_service.mapper.QuoteMapper;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service("opportunityService")
@Slf4j
public class OpportunityServiceImpl implements OpportunityService {
    @Autowired
    private OpportunityMapper opportunityMapper;

    @Autowired
    private OpportunityRepository opportunityRepository;

    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private CoreQueryService<Opportunity> coreQueryService;
    
    @Autowired
    private CampaignService campaignService;
    
    @Autowired
    private AccountService accountService;
    
    @Autowired
    private ActivityService activityService;

    @Autowired
    private ContactOpportunityRoleRepository contactOpportunityRoleRepository;

    @Autowired
    private QuoteRepository quoteRepository;

    @Autowired
    private QuoteMapper quoteMapper;
    @Autowired
    private ContactServiceImpl contactService;

    @Override
    public Response<OpportunityTableResponse> getOpportunitiesTable(
            String filters, 
            String groupBy, 
            String groupValue, 
            String search, 
            String sortBy, 
            String sortDirection, 
            int page, 
            int limit, 
            Long accountId, 
            String language
    ) {
        List<String> searchFields = Arrays.asList(
                "name",
                "description"
        );

        GenericTableResponse<Opportunity> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "opportunities", searchFields, "OPPORTUNITY",
                opportunityRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        Page<Opportunity> opportunityPage = tableResponse.getData();
        List<OpportunityView> opportunityViews = opportunityMapper.toOpportunityView(opportunityPage.getContent());
        OpportunityTableResponse opportunityTableResponse = new OpportunityTableResponse();
        opportunityTableResponse.setGroupItems(tableResponse.getGroupItems());
        opportunityTableResponse.setOpportunities(new PageImpl<>(opportunityViews, opportunityPage.getPageable(), opportunityPage.getTotalElements()));
        return new Response<>(StatusCode.OK, "Get contacts table success!!!", opportunityTableResponse);
    }

    @Override
    @Transactional
    public Response<OpportunityView> createOpportunity(CreateOpportunityRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<User> optOwner = userRepository.findById(request.getOwnerId());
        if (optOwner.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Owner not found!!!");
        }
        Campaign campaign = campaignService.findById(request.getCampaignId());
        if (campaign == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Campaign not found!!!");
        }

        Account account = accountService.findById(request.getAccountId());
        if (account == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Account not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Opportunity CREATE");

        Opportunity opportunity = new Opportunity();
        opportunityMapper.create(request, opportunity, new UserMapStructContext(optUser.get(), optOwner.get()));
        opportunity.setCampaign(campaign);
        opportunity.setAccount(account);

        // Save opportunity - HistoryEntityListener will automatically handle CREATE history
        opportunityRepository.save(opportunity);

        // create an association between the opportunity and the contact if field attachedContactId is not null
        if (request.getAttachedContactId() != null) {
            Contact contact = contactService.findById(request.getAttachedContactId());
            if (contact == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Attached Contact not found!!!");
            }
            ContactOpportunityRole contactOpportunityRole = new ContactOpportunityRole();
            contactOpportunityRole.setContact(contact);
            contactOpportunityRole.setOpportunity(opportunity);
            contactOpportunityRole.setOpportunityId(opportunity.getId());
            contactOpportunityRole.setContactId(contact.getId());
            contactOpportunityRole.setCreatedBy(optUser.get());
            contactOpportunityRole.setLastModifiedBy(optUser.get());
            contactOpportunityRoleRepository.save(contactOpportunityRole);
        }

        OpportunityView opportunityView = opportunityMapper.toOpportunityView(opportunity);
        return new Response<>(StatusCode.OK, "Create opportunity success!!!", opportunityView);
    }

    @Override
    @Transactional
    public Response<OpportunityView> updateOpportunity(UpdateOpportunityRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(request.getId());
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }

        Opportunity opportunity = optOpportunity.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Opportunity originalOpportunity = EntityCloneUtils.cloneEntityForHistory(opportunity);
        EntityContext.setOriginalEntity(originalOpportunity);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Opportunity UPDATE");

        // Step 2: Apply updates to the entity
        opportunityMapper.update(request, opportunity, optUser.get());

        // Handle relationship updates (if any)
        updateOpportunityRelationships(request, opportunity);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(opportunity);

        // Save opportunity - HistoryEntityListener will automatically handle UPDATE history with field changes
        opportunityRepository.save(opportunity);

        OpportunityView opportunityView = opportunityMapper.toOpportunityView(opportunity);
        return new Response<>(StatusCode.OK, "Update opportunity success!!!", opportunityView);
    }

    @Override
    public Response<Integer> deleteOpportunities(List<Long> ids) {
        // Check opportunities exist
        for (Long id : ids) {
            Optional<Opportunity> optOpportunity = opportunityRepository.findById(id);
            if (optOpportunity.isEmpty()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
            }
        }
        Integer result = opportunityRepository.deleteByIdIn(ids);
        return new Response<>(StatusCode.OK, "Delete opportunities success!!!", result);
    }

    @Override
    public Response<OpportunityDetails> getOpportunityDetails(Long opportunityId, Long accountId, String language) {
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }
        OpportunityDetails opportunityDetails = opportunityMapper.toOpportunityDetails(optOpportunity.get());

        // Get attached activities
        List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(
                opportunityId, "OPPORTUNITY", null, accountId, language
        );
        opportunityDetails.setAttachedActivities(attachedActivities);

        // Get related contacts count and top 2
        opportunityDetails.setCountRelatedContacts(contactOpportunityRoleRepository.countByOpportunityId(opportunityId));
        Pageable topTwoContacts = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "lastModifiedAt"));
        List<ContactOpportunityRole> roles = contactOpportunityRoleRepository.findByOpportunityIdWithContact(opportunityId, topTwoContacts);
        opportunityDetails.setAttachedContactRoles(opportunityMapper.toOpportunityContactRoleView(roles, false));

        // Get related quotes count and top 2
        opportunityDetails.setCountRelatedQuotes(quoteRepository.countByOpportunityId(opportunityId));
        Pageable topTwoQuotesPageable = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "lastModifiedAt"));
        Page<Quote> topTwoQuotesPage = quoteRepository.findByOpportunityIdOrderByLastModifiedAtDesc(opportunityId, topTwoQuotesPageable);
        opportunityDetails.setAttachedQuotes(quoteMapper.toQuoteAttachedView(topTwoQuotesPage.getContent()));

        return new Response<>(StatusCode.OK, "Get opportunity details success!!!", opportunityDetails);
    }

    @Override
    public Response<OpportunityKanbanResponse> getOpportunitiesKanban(String filters, String groupBy, String search, Long accountId, String language) {
        List<String> searchFields = Arrays.asList(
                "name", 
                "description"
        );

        List<String> viewFields = Arrays.asList(
                "id",
                "name",
                "stage",
                "success_probability",
                "forecast_category",
                "next_step",
                "type"
        );

        GenericKanbanResponse kanbanResponse = coreQueryService.getKanban(
                filters, groupBy, search, accountId, language, "opportunities", searchFields, viewFields, "OPPORTUNITY", opportunityRepository
        );

        if (kanbanResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<OpportunityKanbanView> opportunityKanbanViews = new ArrayList<>();
        for (Object[] itemObject : kanbanResponse.getItemObjects()) {
            OpportunityKanbanView opportunityKanbanView = new OpportunityKanbanView();
            opportunityKanbanView.setId((Long) itemObject[0]);
            opportunityKanbanView.setName((String) itemObject[1]);
            opportunityKanbanView.setStage(itemObject[2] != null ? (String) itemObject[2]: null);
            opportunityKanbanView.setSuccessProbability((Float) itemObject[3]);
            opportunityKanbanView.setForecastCategory(itemObject[4] != null ? (String) itemObject[4] : null);
            opportunityKanbanView.setNextStep((String) itemObject[5]);
            opportunityKanbanView.setType(itemObject[6] != null ? (String) itemObject[6] : null);
            opportunityKanbanView.setGroupField((String) itemObject[7]);
            opportunityKanbanViews.add(opportunityKanbanView);
        }
        OpportunityKanbanResponse opportunityKanbanResponse = new OpportunityKanbanResponse();
        opportunityKanbanResponse.setGroupItems(kanbanResponse.getGroupItems());
        opportunityKanbanResponse.setOpportunities(opportunityKanbanViews);
        return new Response<>(StatusCode.OK, "Get opportunities kanban success!!!", opportunityKanbanResponse);
    }

    @Override
    public Response<OpportunitySplitViewResponse> getOpportunitiesSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = List.of(
                "name", "expected_revenue", "end_date", "stage",
                "success_probability", "forecast_category", "next_step", "type",
                "description"
        );
        
        // Define view fields
        List<String> viewFields = List.of(
                "id",
                "name",
                "stage",
                "success_probability",
                "forecast_category",
                "next_step",
                "type",
                "lead_source",
                "campaign_id",
                "account_id"
        );

        GenericSplitViewResponse<Object[]> genericResponse = coreQueryService.getSplitView(
                filters, search, sortBy, sortDirection, page, limit,
                "opportunities", searchFields, viewFields, opportunityRepository
        );

        if (genericResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<OpportunitySplitView> opportunities = new ArrayList<>();
        for (Object[] opportunityObject : genericResponse.getData().getContent()) {
            OpportunitySplitView opportunitySplitView = new OpportunitySplitView();
            // Set opportunity fields based on the returned data
            opportunitySplitView.setId((Long) opportunityObject[0]);
            opportunitySplitView.setName((String) opportunityObject[1]);
            opportunitySplitView.setStage(opportunityObject[2] != null ? (String) opportunityObject[2] : null);
            opportunitySplitView.setSuccessProbability((Float) opportunityObject[3]);
            opportunitySplitView.setForecastCategory(opportunityObject[4] != null ? (String) opportunityObject[4] : null);
            opportunitySplitView.setNextStep((String) opportunityObject[5]);
            opportunitySplitView.setType(opportunityObject[6] != null ? (String) opportunityObject[6] : null);
            opportunitySplitView.setLeadSource(opportunityObject[7] != null ? (String) opportunityObject[7] : null);
            Long campaignId = (Long) opportunityObject[8];
            if (campaignId != null) {
                System.out.println("Campaign ID: " + campaignId);
                CampaignShortView campaignShortView = campaignService.getCampaignShortView(campaignId);
                opportunitySplitView.setCampaign(campaignShortView);
            }
            Long customerId = (Long) opportunityObject[9];
            if (customerId != null) {
                AccountView accountView = accountService.getAccountView(customerId);
                opportunitySplitView.setAccount(accountView);
            }
            opportunities.add(opportunitySplitView);
        }

        Page<OpportunitySplitView> opportunitiesPage = new PageImpl<>(
                opportunities,
                genericResponse.getData().getPageable(),
                genericResponse.getData().getTotalElements()
        );

        OpportunitySplitViewResponse response = new OpportunitySplitViewResponse();
        response.setOpportunities(opportunitiesPage);
        return new Response<>(StatusCode.OK, "Get opportunities split view success!!!", response);
    }

    @Override
    public Response<Boolean> updateKanbanOpportunity(Long opportunityId, String fieldName, String fieldValue, Long accountId, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "opportunities");
        jsonFilter.put("id", opportunityId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);

        opportunityRepository.updateKanbanData(jsonFilter.toString());

        return new Response<>(StatusCode.OK, "Update opportunity kanban success!!!", true);
    }

    @Override
    public List<OpportunityView> getOpportunitiesByAccountId(Long accountId) {
        List<Opportunity> opportunities = opportunityRepository.findByAccountId(accountId);
        return opportunityMapper.toOpportunityView(opportunities);
    }

    @Override
    public Opportunity findById(Long id) {
        Opportunity opportunity = opportunityRepository.findById(id).orElse(null);
        return opportunity;
    }

    @Override
    public Response<List<OpportunityContactRoleView>> bulkCreateContactRolesForOpportunity(List<CreateOpportunityContactRoleAttachedRequest> contactRoleList, Long opportunityId, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        // check if opportunity exists
        if (opportunityId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity ID is null!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }
        List<ContactOpportunityRole> contactRoleListToCreate = new ArrayList<>();
        for (CreateOpportunityContactRoleAttachedRequest contactRole : contactRoleList) {
            // check if contact exists
            if (contactRole.getContactId() == null) {
                continue;
            }
            Contact contact = contactService.findById(contactRole.getContactId());
            if (contact == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Provided Contact ID not found!!!");
            }
            // check if (contact role + contact_id + opportunity_id) value already exists
            if (contactOpportunityRoleRepository.existsByContactIdAndOpportunityIdAndRole(contactRole.getContactId(), opportunityId, contactRole.getRole())) {
                return new Response<>(StatusCode.BAD_REQUEST, "Contact role already exists for this opportunity!!!");
            }
            ContactOpportunityRole contactOpportunityRole = new ContactOpportunityRole();
            contactOpportunityRole.setContactId(contactRole.getContactId());
            contactOpportunityRole.setContact(contact);
            contactOpportunityRole.setOpportunity(optOpportunity.get());
            contactOpportunityRole.setOpportunityId(opportunityId);
            contactOpportunityRole.setRole(contactRole.getRole());
            contactOpportunityRole.setCreatedBy(optUser.get());
            contactOpportunityRole.setLastModifiedBy(optUser.get());
            contactRoleListToCreate.add(contactOpportunityRole);
        }
        // save contact roles
        contactOpportunityRoleRepository.saveAll(contactRoleListToCreate);

        List<OpportunityContactRoleView> contactRoleViews = opportunityMapper.toOpportunityContactRoleView(contactRoleListToCreate, false);
        return new Response<>(StatusCode.OK, "Create contact roles for opportunity success!!!", contactRoleViews);
    }

    @Override
    public Response<List<OpportunityContactRoleView>> bulkUpdateContactRolesForOpportunity(List<UpdateOpportunityContactRoleAttachedRequest> contactRoleList, Long opportunityId, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        // check if opportunity exists
        if (opportunityId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity ID is null!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }
        List<ContactOpportunityRole> contactRoleListToUpdate = new ArrayList<>();
        for (UpdateOpportunityContactRoleAttachedRequest contactRole : contactRoleList) {
            // check if contact exists
            if (contactRole.getContactId() == null) {
                continue;
            }
            Contact contact = contactService.findById(contactRole.getContactId());
            if (contact == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Provided Contact ID not found!!!");
            }
            Optional<ContactOpportunityRole> contactOpportunityRole = contactOpportunityRoleRepository.findById(contactRole.getId());
            if (contactOpportunityRole.isEmpty()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Provided Contact Opportunity Role ID not found!!!");
            }
            ContactOpportunityRole contactOpportunityRoleToUpdate = contactOpportunityRole.get();
            contactOpportunityRoleToUpdate.setContactId(contactRole.getContactId());
            contactOpportunityRoleToUpdate.setContact(contact);
            contactOpportunityRoleToUpdate.setRole(contactRole.getRole());
            contactOpportunityRoleToUpdate.setLastModifiedBy(optUser.get());
            contactRoleListToUpdate.add(contactOpportunityRoleToUpdate);
        }
        // save contact roles
        contactOpportunityRoleRepository.saveAll(contactRoleListToUpdate);
        List<OpportunityContactRoleView> contactRoleViews = opportunityMapper.toOpportunityContactRoleView(contactRoleListToUpdate, false);
        return new Response<>(StatusCode.OK, "Update contact roles for opportunity success!!!", contactRoleViews);
    }

    @Override
    public Response<String> bulkDeleteContactRoleForOpportunity(List<Long> contactRoleIds, Long opportunityId, Long accountId, String language) {
        // check if opportunity exists
        if (opportunityId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity ID is null!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }
        contactOpportunityRoleRepository.deleteAllById(contactRoleIds);
        return new Response<>(StatusCode.OK, "Delete contact roles for opportunity success!!!", null);
    }

    @Override
    public Response<GenericTableResponse<OpportunityContactRoleView>> getContactRolesForOpportunity(Long opportunityId, int page, int limit, Long accountId, String language) {
        // check if opportunity exists
        if (opportunityId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity ID is null!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        List<ContactOpportunityRole> contactRoles = contactOpportunityRoleRepository.findByOpportunityIdWithContact(opportunityId, pageable);
        long totalCount = contactOpportunityRoleRepository.countByOpportunityId(opportunityId);

        List<OpportunityContactRoleView> contactRoleViews = opportunityMapper.toOpportunityContactRoleView(contactRoles, false);
        Page<OpportunityContactRoleView> contactRolePage = new PageImpl<>(contactRoleViews, pageable, totalCount);

        GenericTableResponse<OpportunityContactRoleView> responseData = new GenericTableResponse<>(contactRolePage, null);

        return new Response<>(StatusCode.OK, "Get contact roles for opportunity success!!!", responseData);
    }

    @Override
    public Response<GenericTableResponse<QuoteAttachedView>> getAttachedQuoteForOpportunity(Long opportunityId, int page, int limit, Long accountId, String language) {
        // check if opportunity exists
        if (opportunityId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity ID is null!!!");
        }
        Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
        if (optOpportunity.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        Page<Quote> attachedQuotes = quoteRepository.findByOpportunityIdOrderByLastModifiedAtDesc(opportunityId, pageable);
        Page<QuoteAttachedView> quotePageViews = attachedQuotes.map(quote -> quoteMapper.toQuoteAttachedView(quote));
        return new Response<>(StatusCode.OK, "Get attached quotes for opportunity success!!!", new GenericTableResponse<>(quotePageViews, null));
    }

    /**
     * Force initialization of lazy-loaded relationships to ensure they are accessible during cloning
     * This prevents Hibernate proxy issues when extracting IDs for history tracking
     */
    private void initializeOpportunityRelationships(Opportunity opportunity) {
        log.debug("Initializing lazy-loaded relationships for Opportunity ID: {}", opportunity.getId());

        try {
            // Force initialization of each @ManyToOne relationship by accessing their IDs
            if (opportunity.getOwner() != null) {
                Long ownerId = opportunity.getOwner().getId();
                log.debug("Initialized owner relationship, ID: {}", ownerId);
            }

            if (opportunity.getAccount() != null) {
                Long accountId = opportunity.getAccount().getId();
                log.debug("Initialized account relationship, ID: {}", accountId);
            }

            if (opportunity.getCampaign() != null) {
                Long campaignId = opportunity.getCampaign().getId();
                log.debug("Initialized campaign relationship, ID: {}", campaignId);
            }

            // Note: Opportunity doesn't have a direct contact relationship
            // It has contactRoles (OneToMany) which we don't need to initialize for history tracking

            if (opportunity.getCreatedBy() != null) {
                Long createdById = opportunity.getCreatedBy().getId();
                log.debug("Initialized createdBy relationship, ID: {}", createdById);
            }

            if (opportunity.getLastModifiedBy() != null) {
                Long lastModifiedById = opportunity.getLastModifiedBy().getId();
                log.debug("Initialized lastModifiedBy relationship, ID: {}", lastModifiedById);
            }

            log.debug("Successfully initialized all relationships for Opportunity ID: {}", opportunity.getId());

        } catch (Exception e) {
            log.warn("Error initializing relationships for Opportunity ID: {} - {}", opportunity.getId(), e.getMessage());
            // Continue anyway - the enhanced extractEntityId method should handle uninitialized proxies
        }
    }

    /**
     * Update opportunity relationships based on the provided IDs in the request
     */
    private void updateOpportunityRelationships(UpdateOpportunityRequest request, Opportunity opportunity) {
        // accountID
        if (request.getAccountId() != null) {
            Account account = accountService.findById(request.getAccountId());
            if (account == null) {
                return;
            }
            opportunity.setAccount(account);
        }
        // campaignID
        if (request.getCampaignId() != null) {
            Campaign campaign = campaignService.findById(request.getCampaignId());
            if (campaign == null) {
                return;
            }
            opportunity.setCampaign(campaign);
        }
    }
}
