package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapper.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.*;

import lombok.extern.slf4j.Slf4j;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import java.util.*;

@Service("orderService")
@Slf4j
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private OpportunityRepository opportunityRepository;
    @Autowired
    private OrderProductRepository orderProductRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private ActivityService activityService;

    @Autowired
    private OrderProductMapper orderProductMapper;

    @Autowired
    private CoreQueryService<Order> coreQueryService;
    
    @Autowired
    private AccountService accountService;
    @Autowired
    private ContactService contactService;
    @Autowired
    private OpportunityService opportunityService;
    @Autowired
    private ContractService contractService;

    @Override
    public Response<OrderTableResponse> getOrdersTable(
            String filters, 
            String groupBy, 
            String groupValue, 
            String search, 
            String sortBy, 
            String sortDirection, 
            int page, 
            int limit, 
            Long accountId, 
            String language
    ) {
        List<String> searchFields = Arrays.asList(
                "order_code",
                "status"
        );

        GenericTableResponse<Order> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "orders", searchFields, "ORDER",
                orderRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        Page<Order> orderPage = tableResponse.getData();
        List<OrderView> orderViews = orderMapper.toOrderView(orderPage.getContent());
        OrderTableResponse orderTableResponse = new OrderTableResponse();
        orderTableResponse.setGroupItems(tableResponse.getGroupItems());
        orderTableResponse.setOrders(new PageImpl<>(orderViews, orderPage.getPageable(), orderPage.getTotalElements()));
        return new Response<>(StatusCode.OK, "Get contacts table success!!!", orderTableResponse);
    }

    @Override
    @Transactional
    public Response<OrderView> createOrder(CreateOrderRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        
        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());
        
        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Order CREATE");
        
        User owner = null;
        if (request.getOwnerId() != null) {
            owner = userRepository.findById(request.getOwnerId()).orElse(null);
        }
        
        Contact contact = null;
        if (request.getContactId() != null) {
            contact = contactService.findById(request.getContactId());
        }

        Opportunity opportunity = null;
        if (request.getOpportunityId() != null) {
            opportunity = opportunityService.findById(request.getOpportunityId());
        }
        
        Contract contract = null;
        if (request.getContractId() != null) {
            contract = contractService.findById(request.getContractId());
        }
        
        Account account = null;
        if (request.getAccountId() != null) {
            account = accountService.findById(request.getAccountId());
        }

        Order order = new Order();
        orderMapper.create(request, order, new UserMapStructContext(optUser.get(), owner));
        order.setContact(contact);
        order.setOpportunity(opportunity);
        order.setContract(contract);
        order.setAccount(account);
        
        // Save order - HistoryEntityListener will automatically handle CREATE history
        order = orderRepository.save(order);

        OrderView orderView = orderMapper.toOrderView(order);
        return new Response<>(StatusCode.OK, "Create order success!!!", orderView);
    }

    @Override
    @Transactional
    public Response<OrderView> updateOrder(UpdateOrderRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<Order> optOrder = orderRepository.findById(request.getId());
        if (optOrder.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found!!!");
        }
        Order order = optOrder.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: clone the original entity for history tracking
        // This avoids all Hibernate entity reference issues
        Order originalOrder = EntityCloneUtils.cloneEntityForHistory(order);
        EntityContext.setOriginalEntity(originalOrder);
        
        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Order UPDATE");
        
        // Step 2: Apply updates to the entity
        orderMapper.update(request, order, optUser.get());

        // Handle relationship updates
        updateOrderRelationships(request, order);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(order);

        // Save order - HistoryEntityListener will automatically handle UPDATE history with field changes
        orderRepository.save(order);
        
        OrderView orderView = orderMapper.toOrderView(order);
        return new Response<>(StatusCode.OK, "Update order success!!!", orderView);
    }

    @Override
    public Response<Integer> deleteOrders(List<Long> ids) {
        // Check orders exist
        for (Long id : ids) {
            Optional<Order> optOrder = orderRepository.findById(id);
            if (optOrder.isEmpty()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Order not found!!!");
            }
        }
        Integer result = orderRepository.deleteByIdIn(ids);
        return new Response<>(StatusCode.OK, "Delete orders success!!!", result);
    }

    @Override
    public Response<OrderDetails> getOrderDetails(Long orderId, Long accountId, String language) {
        Optional<Order> optOrder = orderRepository.findById(orderId);
        if (optOrder.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found!!!");
        }
        OrderDetails orderDetails = orderMapper.toOrderDetails(optOrder.get());
        
        // Get attached activities
        List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(
                orderId, "ORDER", null, accountId, language
        );
        orderDetails.setAttachedActivities(attachedActivities);
        
        return new Response<>(StatusCode.OK, "Get order details success!!!", orderDetails);
    }

    @Override
    public Response<OrderKanbanResponse> getOrdersKanban(String filters, String groupBy, String search, Long accountId, String language) {
        List<String> searchFields = Arrays.asList(
                "order_code",
                "status"
        );
        
        List<String> viewFields = Arrays.asList(
                "id",
                "order_code",
                "status",
                "order_type",
                "order_date",
                "order_cost",
                "shipping_status",
                "order_payment_status",
                "paid_cost",
                "remaining_cost",
                "contract_id",
                "owner_id",
                "account_id"
        );

        GenericKanbanResponse kanbanResponse = coreQueryService.getKanban(
                filters, groupBy, search, accountId, language, "orders", searchFields, viewFields, "ORDER", orderRepository
        );

        if (kanbanResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<OrderKanbanView> orderKanbanViews = new ArrayList<>();
        for (Object[] itemObject : kanbanResponse.getItemObjects()) {
            OrderKanbanView orderKanbanView = new OrderKanbanView();
            orderKanbanView.setId((Long) itemObject[0]);
            orderKanbanView.setCode((String) itemObject[1]);
            orderKanbanView.setStatus((String) itemObject[2]);
            orderKanbanView.setType((String) itemObject[3]);
            orderKanbanView.setOrderDate((Date) itemObject[4]);
            orderKanbanView.setCost((Integer) itemObject[5]);
            orderKanbanView.setShippingStatus((String) itemObject[6]);
            orderKanbanView.setPaymentStatus((String) itemObject[7]);
            orderKanbanView.setPaidCost((Integer) itemObject[8]);
            orderKanbanView.setRemainingCost((Integer) itemObject[9]);
            Long contractId = (Long) itemObject[10];
            if (contractId != null) {
                Optional<Contract> optContract = contractRepository.findById(contractId);
                if (optContract.isPresent()) {
                    LookUpView contractLookUpView = new LookUpView();
                    contractLookUpView.setId(optContract.get().getId());
                    contractLookUpView.setName(optContract.get().getContractNumber());
                    orderKanbanView.setContract(contractLookUpView);
                }
            }
            Long ownerId = (Long) itemObject[11];
            if (ownerId != null) {
                Optional<User> optOwner = userRepository.findById(ownerId);
                if (optOwner.isPresent()) {
                    LookUpView ownerLookUpView = new LookUpView();
                    ownerLookUpView.setId(optOwner.get().getId());
                    ownerLookUpView.setName(optOwner.get().getFirstName() + " " + optOwner.get().getLastName());
                    orderKanbanView.setOwner(ownerLookUpView);
                }

            }
            Long linkedAccountId = (Long) itemObject[12];
            if (linkedAccountId != null) {
                Optional<Account> optLinkedAccount = accountRepository.findById(linkedAccountId);
                if (optLinkedAccount.isPresent()) {
                    LookUpView accountLookUpView = new LookUpView();
                    accountLookUpView.setId(optLinkedAccount.get().getId());
                    accountLookUpView.setName(optLinkedAccount.get().getName());
                    orderKanbanView.setAccount(accountLookUpView);
                }
            }
            orderKanbanView.setGroupField((String) itemObject[13]);
            
            orderKanbanViews.add(orderKanbanView);
        }
        OrderKanbanResponse orderKanbanResponse = new OrderKanbanResponse();
        orderKanbanResponse.setGroupItems(kanbanResponse.getGroupItems());
        orderKanbanResponse.setOrders(orderKanbanViews);
        return new Response<>(StatusCode.OK, "Get orders kanban success!!!", orderKanbanResponse);
    }

    @Override
    public Response<OrderSplitViewResponse> getOrdersSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = List.of(
                "order_code",
                "status"
        );
        
        // Define view fields
        List<String> viewFields = List.of(
                "id",
                "code",
                "status",
                "type",
                "order_date",
                "cost",
                "billing_number",
                "billing_date",
                "billing_value",
                "shipping_status",
                "payment_status",
                "paid_cost",
                "remaining_cost",
                "shipping_date",
                "payment_date",
                "contact_id",
                "opportunity_id",
                "contract_id",
                "owner_id",
                "account_id"
        );

        GenericSplitViewResponse<Object[]> genericResponse = coreQueryService.getSplitView(
                filters, search, sortBy, sortDirection, page, limit,
                "orders", searchFields, viewFields, orderRepository
        );

        if (genericResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<OrderSplitView> orders = new ArrayList<>();
        for (Object[] orderObject : genericResponse.getData().getContent()) {
            OrderSplitView orderSplitView = new OrderSplitView();
            // Set order fields based on the returned data
            orderSplitView.setId((Long) orderObject[0]);
            orderSplitView.setCode((String) orderObject[1]);
            orderSplitView.setStatus((String) orderObject[2]);
            orderSplitView.setType((String) orderObject[3]);
            orderSplitView.setOrderDate((Date) orderObject[4]);
            orderSplitView.setCost((Integer) orderObject[5]);
            orderSplitView.setBillingNumber((String) orderObject[6]);
            orderSplitView.setBillingDate((Date) orderObject[7]);
            orderSplitView.setBillingValue((Integer) orderObject[8]);
            orderSplitView.setShippingStatus((String) orderObject[9]);
            orderSplitView.setPaymentStatus((String) orderObject[10]);
            orderSplitView.setPaidCost((Integer) orderObject[11]);
            orderSplitView.setRemainingCost((Integer) orderObject[12]);
            orderSplitView.setShippingDate((Date) orderObject[13]);
            orderSplitView.setPaymentDate((Date) orderObject[14]);
            Long contactId = (Long) orderObject[15];
            if (contactId != null) {
                Optional<Contact> optContact = contactRepository.findById(contactId);
                if (optContact.isPresent()) {
                    LookUpView contact = new LookUpView();
                    contact.setId(optContact.get().getId());
                    contact.setName(optContact.get().getFirstName() + " " + optContact.get().getLastName());
                    orderSplitView.setContact(contact);
                }
            }
            Long opportunityId = (Long) orderObject[16];
            if (opportunityId != null) {
                Optional<Opportunity> optOpportunity = opportunityRepository.findById(opportunityId);
                if (optOpportunity.isPresent()) {
                    LookUpView opportunity = new LookUpView();
                    opportunity.setId(optOpportunity.get().getId());
                    opportunity.setName(optOpportunity.get().getName());
                    orderSplitView.setOpportunity(opportunity);
                }
            }
            Long contractId = (Long) orderObject[17];
            if (contractId != null) {
                Optional<Contract> optContract = contractRepository.findById(contractId);
                if (optContract.isPresent()) {
                    LookUpView contract = new LookUpView();
                    contract.setId(optContract.get().getId());
                    contract.setName(optContract.get().getContractNumber());
                    orderSplitView.setContract(contract);
                }
            }
            Long ownerId = (Long) orderObject[18];
            if (ownerId != null) {
                Optional<User> optOwner = userRepository.findById(ownerId);
                if (optOwner.isPresent()) {
                    LookUpView owner = new LookUpView();
                    owner.setId(optOwner.get().getId());
                    owner.setName(optOwner.get().getFirstName() + " " + optOwner.get().getLastName());
                    orderSplitView.setOwner(owner);
                }
            }
            Long linkedAccountId = (Long) orderObject[19];
            if (linkedAccountId != null) {
                Optional<Account> optLinkedAccount = accountRepository.findById(linkedAccountId);
                if (optLinkedAccount.isPresent()) {
                    LookUpView linkedAccount = new LookUpView();
                    linkedAccount.setId(optLinkedAccount.get().getId());
                    linkedAccount.setName(optLinkedAccount.get().getName());
                    orderSplitView.setAccount(linkedAccount);
                }
            }
            orders.add(orderSplitView);
        }

        Page<OrderSplitView> ordersPage = new PageImpl<>(
                orders,
                genericResponse.getData().getPageable(),
                genericResponse.getData().getTotalElements()
        );

        OrderSplitViewResponse response = new OrderSplitViewResponse();
        response.setOrders(ordersPage);
        return new Response<>(StatusCode.OK, "Get orders split view success!!!", response);
    }

    @Override
    public Response<Boolean> updateKanbanOrder(Long orderId, String fieldName, String fieldValue, Long accountId, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "orders");
        jsonFilter.put("id", orderId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);

        orderRepository.updateKanbanData(jsonFilter.toString());

        return new Response<>(StatusCode.OK, "Update order kanban success!!!", true);
    }

    @Override
    public List<OrderView> getOrdersByAccountId(Long accountId) {
        List<Order> orders = orderRepository.findByAccountId(accountId);
        return orderMapper.toOrderView(orders);
    }

    @Override
    public Response<GenericTableResponse<OrderProductView>> getProductsForOrder(Long orderId, int page, int limit, Long accountId, String language) {
        // check if orderId exists
        if (orderId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "order ID is null!!!");
        }
        Optional<Order> optOrder = orderRepository.findById(orderId);
        if (optOrder.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        List<OrderProduct> orderProducts = orderProductRepository.findByOrderIdWithProduct(orderId, pageable);
        long totalCount = orderProductRepository.countByOrderId(orderId);

        List<OrderProductView> orderProductViews = orderProductMapper.toOrderProductView(orderProducts);
        Page<OrderProductView> orderProductPage = new PageImpl<>(orderProductViews, pageable, totalCount);

        GenericTableResponse<OrderProductView> responseData = new GenericTableResponse<>(orderProductPage, null);

        return new Response<>(StatusCode.OK, "Get products for order success!!!", responseData);
    }

    @Override
    @Transactional
    public Response<List<OrderProductView>> addProductsToOrder(Long orderId, List<AddProductToOrderRequest> request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        // Validate that the order exists and belongs to the account
        Optional<Order> orderOptional = orderRepository.findById(orderId);
        if (!orderOptional.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found");
        }

        Order order = orderOptional.get();

        List<OrderProduct> orderProductsToCreate = new ArrayList<>();

        for (AddProductToOrderRequest productRequest : request) {
            // Check if product exists
            Optional<Product> productOptional = productRepository.findById(productRequest.getProductId());
            if (!productOptional.isPresent()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Product with ID " + productRequest.getProductId() + " not found", null);
            }

            // Check if the product is already added to this order
            if (orderProductRepository.existsByOrderIdAndProductId(orderId, productRequest.getProductId())) {
                return new Response<>(StatusCode.BAD_REQUEST, "Product with ID " + productRequest.getProductId() + " is already added to this order", null);
            }

            Product product = productOptional.get();

            // Create new OrderProduct entity
            OrderProduct orderProduct = new OrderProduct();
            orderProduct.setOrder(order);
            orderProduct.setOrderId(orderId);
            orderProduct.setProduct(product);
            orderProduct.setProductId(product.getId());
            orderProduct.setDescription(productRequest.getDescription());
            orderProduct.setCreatedBy(optUser.get());
            orderProduct.setLastModifiedBy(optUser.get());

            orderProductsToCreate.add(orderProduct);
        }
        // Save all OrderProduct entities
        orderProductsToCreate = orderProductRepository.saveAll(orderProductsToCreate);

        // Convert to response DTOs
        List<OrderProductView> responseList = orderProductMapper.toOrderProductView(orderProductsToCreate);

        return new Response<>(StatusCode.OK, "Products added to order successfully", responseList);
    }

    @Override
    @Transactional
    public Response<OrderProductView> updateOrderProduct(Long orderId, UpdateOrderProductRequest request, Long accountId, String language) {
        // check if the order exists
        Optional<Order> orderOptional = orderRepository.findById(orderId);
        if (!orderOptional.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found", null);
        }
        // Find the OrderProduct
        Optional<OrderProduct> orderProductOptional = orderProductRepository.findById(request.getId());
        if (!orderProductOptional.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order product not found", null);
        }

        OrderProduct orderProduct = orderProductOptional.get();

        // Update the description
        orderProduct.setDescription(request.getDescription());
        
        // Save the updated OrderProduct
        OrderProduct updatedOrderProduct = orderProductRepository.save(orderProduct);

        // Convert to response DTO
        OrderProductView responseView = orderProductMapper.toOrderProductView(updatedOrderProduct);

        return new Response<>(StatusCode.OK, "Order product updated successfully", responseView);
    }

    @Override
    @Transactional
    public Response<String> deleteOrderProducts(Long orderId, List<Long> orderProductIds, Long accountId, String language) {
        Optional<Order> orderOptional = orderRepository.findById(orderId);
        if (!orderOptional.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Order not found", null);
        }

        // use deleteAllById
        orderProductRepository.deleteAllById(orderProductIds);
        return new Response<>(StatusCode.OK, "Delete order products successfully!!!");
    }

    /**
     * Update order relationships based on the provided IDs in the request
     * Handles contactId, opportunityId, contractId, ownerId, and accountId
     */
    private void updateOrderRelationships(UpdateOrderRequest request, Order order) {
        if (request.getContactId() != null) {
            Optional<Contact> optContact = contactRepository.findById(request.getContactId());
            if (optContact.isPresent()) {
                order.setContact(optContact.get());
            }
        }

        if (request.getOpportunityId() != null) {
            Optional<Opportunity> optOpportunity = opportunityRepository.findById(request.getOpportunityId());
            if (optOpportunity.isPresent()) {
                order.setOpportunity(optOpportunity.get());
            }
        }

        if (request.getContractId() != null) {
            Optional<Contract> optContract = contractRepository.findById(request.getContractId());
            if (optContract.isPresent()) {
                order.setContract(optContract.get());
                log.info("Updated order contract to ID: {}", request.getContractId());
            }
        }

        if (request.getOwnerId() != null) {
            Optional<User> optOwner = userRepository.findById(request.getOwnerId());
            if (optOwner.isPresent()) {
                order.setOwner(optOwner.get());
            }
        }

        if (request.getAccountId() != null) {
            Optional<Account> optAccount = accountRepository.findById(request.getAccountId());
            if (optAccount.isPresent()) {
                order.setAccount(optAccount.get());
            }
        }
    }

}
