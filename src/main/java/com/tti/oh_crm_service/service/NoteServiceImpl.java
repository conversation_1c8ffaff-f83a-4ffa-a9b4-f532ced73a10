package com.tti.oh_crm_service.service;

import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.entity.CreateNoteRequest;
import com.tti.oh_crm_service.entity.LookUpView;
import com.tti.oh_crm_service.entity.NoteView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateNoteRequest;
import com.tti.oh_crm_service.mapper.NoteMapper;
import com.tti.oh_crm_service.model.Note;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.NoteRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.utils.ServiceUtils;

@Service("noteService")
public class NoteServiceImpl implements NoteService {

    @Autowired
    private ServiceUtils serviceUtils;

    @Autowired
    private NoteMapper noteMapper;

    @Autowired
    private UserRepository userRepository;
    @Autowired
    private NoteRepository noteRepository;
    
    @Override
    public Response<NoteView> getNoteDetail(Long id) {
        Optional<Note> optNote = noteRepository.findById(id);
        if (!optNote.isPresent()) {
            return new Response<NoteView>(StatusCode.BAD_REQUEST, "Note not found!!!");
        }
        NoteView noteView = noteMapper.toNoteView(optNote.get());
        noteView.setRelatedTo(serviceUtils.getLookUpViewById(optNote.get().getRelatedToModule(), optNote.get().getRelatedToId()));

        if (optNote.get().getCreatedBy() != null) {
            LookUpView createdBy = new LookUpView();
            createdBy.setId(optNote.get().getCreatedBy().getId());
            createdBy.setName(optNote.get().getCreatedBy().getFirstName() + " " + optNote.get().getCreatedBy().getLastName());
            noteView.setCreatedBy(createdBy);
        }

        if (optNote.get().getLastModifiedBy() != null) {
            LookUpView lastModifiedBy = new LookUpView();
            lastModifiedBy.setId(optNote.get().getLastModifiedBy().getId());
            lastModifiedBy.setName(optNote.get().getLastModifiedBy().getFirstName() + " " + optNote.get().getLastModifiedBy().getLastName());
            noteView.setLastModifiedBy(lastModifiedBy);
        }

        return new Response<NoteView>(StatusCode.OK, "Get note detail success!!!", noteView);
    }

    @Override
    public Response<Page<NoteView>> getNotesByRelated(String relatedModule, Long relatedId, String sortBy,
            String sortDirection, int page, int limit, String language) {
        Pageable pageable = PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy));
        Page<Note> notes = noteRepository.findByRelatedToIdAndRelatedToModule(relatedId, relatedModule, pageable);
        List<NoteView> noteViews = noteMapper.toNoteView(notes.getContent());
        // Get look up info
        for (int i = 0; i < noteViews.size(); i++) {
            NoteView noteView = noteViews.get(i);
            Note note = notes.getContent().get(i);
            noteView.setRelatedTo(serviceUtils.getLookUpViewById(note.getRelatedToModule(), note.getRelatedToId()));

            if (note.getCreatedBy() != null) {
                LookUpView createdBy = new LookUpView();
                createdBy.setId(note.getCreatedBy().getId());
                createdBy.setName(note.getCreatedBy().getFirstName() + " " + note.getCreatedBy().getLastName());
                noteView.setCreatedBy(createdBy);
            }
    
            if (note.getLastModifiedBy() != null) {
                LookUpView lastModifiedBy = new LookUpView();
                lastModifiedBy.setId(note.getLastModifiedBy().getId());
                lastModifiedBy.setName(note.getLastModifiedBy().getFirstName() + " " + note.getLastModifiedBy().getLastName());
                noteView.setLastModifiedBy(lastModifiedBy);
            }
        }
        Page<NoteView> noteViewPage = new PageImpl<>(noteViews, pageable, notes.getTotalElements());
        return new Response<Page<NoteView>>(StatusCode.OK, "Get notes by related success!!!", noteViewPage);
    }

    @Override
    public Response<NoteView> createNote(CreateNoteRequest createNoteRequest, Long accountId) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        Note newNote = new Note();
        newNote = noteMapper.create(createNoteRequest);
        if (optUser.isPresent()) {
            newNote.setCreatedBy(optUser.get());
            newNote.setLastModifiedBy(optUser.get());
        }
        newNote = noteRepository.save(newNote);
        return new Response<NoteView>(StatusCode.OK, "Create note success!!!");
    }

    @Override
    public Response<NoteView> updateNote(UpdateNoteRequest updateNoteRequest, Long accountId) {
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        Optional<Note> optNote = noteRepository.findById(updateNoteRequest.getId());
        if (optNote.isPresent()) {
            Note note = optNote.get();
            noteMapper.update(updateNoteRequest, note);
            if (optUser.isPresent()) {
                note.setLastModifiedBy(optUser.get());
            }
            note = noteRepository.save(note);
            return new Response<NoteView>(StatusCode.OK, "Update note success!!!");
        } else {
            return new Response<NoteView>(StatusCode.BAD_REQUEST, "Note not found!!!");
        }
    }

    @Override
    public Response<List<Long>> deleteNotes(List<Long> noteIds) {
        for (Long id : noteIds) {
            Optional<Note> optNote = noteRepository.findById(id);
            if (!optNote.isPresent()) {
                return new Response<List<Long>>(StatusCode.BAD_REQUEST, "Note not found!!!");
            }
        }
        noteRepository.deleteByIdIn(noteIds);
        return new Response<List<Long>>(StatusCode.OK, "Delete notes success!!!");
    }
    
}
