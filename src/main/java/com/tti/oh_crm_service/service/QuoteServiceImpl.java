package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapper.QuoteMapper;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.QuoteRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service("quoteService")
@Slf4j
public class QuoteServiceImpl implements QuoteService {

    @Autowired
    private QuoteMapper quoteMapper;

    @Autowired
    private QuoteRepository quoteRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CoreQueryService<Quote> coreQueryService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private OpportunityService opportunityService;

    @Autowired
    private ContactService contactService;

    @Override
    public Response<QuoteTableResponse> getQuotesTable(
            String filters,
            String groupBy,
            String groupValue,
            String search,
            String sortBy,
            String sortDirection,
            int page,
            int limit,
            Long accountId,
            String language
    ) {
        List<String> searchFields = Arrays.asList(
                "name",
                "code"
        );

        GenericTableResponse<Quote> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "quotes", searchFields, "QUOTE",
                quoteRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        Page<Quote> quotePage = tableResponse.getData();
        List<QuoteView> quoteViews = quoteMapper.toQuoteView(quotePage.getContent());
        QuoteTableResponse quoteTableResponse = new QuoteTableResponse();
        quoteTableResponse.setGroupItems(tableResponse.getGroupItems());
        quoteTableResponse.setQuotes(new PageImpl<>(quoteViews, quotePage.getPageable(), quotePage.getTotalElements()));
        return new Response<>(StatusCode.OK, "Get quotes table success!!!", quoteTableResponse);
    }

    @Override
    @Transactional
    public Response<QuoteView> createQuote(CreateQuoteRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Quote CREATE");

        Account account = null;
        if (request.getAccountId() != null) {
            account = accountService.findById(request.getAccountId());
            if (account == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Account not found!!!");
            }
        }

        Opportunity opportunity = null;
        if (request.getOpportunityId() != null) {
            opportunity = opportunityService.findById(request.getOpportunityId());
            if (opportunity == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Opportunity not found!!!");
            }
        }

        Contact contact = null;
        if (request.getContactId() != null) {
            contact = contactService.findById(request.getContactId());
            if (contact == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Contact not found!!!");
            }
        }

        Quote quote = new Quote();
        quoteMapper.create(request, quote, new UserMapStructContext(optUser.get(), null));
        quote.setAccount(account);
        quote.setOpportunity(opportunity);
        quote.setContact(contact);

        // Save quote - HistoryEntityListener will automatically handle CREATE history
        quoteRepository.save(quote);

        QuoteView quoteView = quoteMapper.toQuoteView(quote);
        return new Response<>(StatusCode.OK, "Create quote success!!!", quoteView);
    }

    @Override
    @Transactional
    public Response<QuoteView> updateQuote(UpdateQuoteRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        Optional<Quote> optQuote = quoteRepository.findById(request.getId());
        if (optQuote.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Quote not found!!!");
        }

        Quote quote = optQuote.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Quote originalQuote = EntityCloneUtils.cloneEntityForHistory(quote);
        EntityContext.setOriginalEntity(originalQuote);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Quote UPDATE");

        // Step 2: Apply updates to the entity
        quoteMapper.update(request, quote, optUser.get());

        // Handle relationship updates (if any)
        updateQuoteRelationships(request, quote);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(quote);

        // Save quote - HistoryEntityListener will automatically handle UPDATE history with field changes
        quoteRepository.save(quote);

        QuoteView quoteView = quoteMapper.toQuoteView(quote);
        return new Response<>(StatusCode.OK, "Update quote success!!!", quoteView);
    }

    @Override
    public Response<Integer> deleteQuotes(List<Long> ids) {
        // Check quotes exist
        for (Long id : ids) {
            Optional<Quote> optQuote = quoteRepository.findById(id);
            if (optQuote.isEmpty()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Quote not found!!!");
            }
        }

        Integer result = quoteRepository.deleteByIdIn(ids);
        return new Response<>(StatusCode.OK, "Delete quotes success!!!", result);
    }

    @Override
    public Response<QuoteDetails> getQuoteDetails(Long quoteId, Long accountId, String language) {
        Optional<Quote> optQuote = quoteRepository.findById(quoteId);
        if (optQuote.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Quote not found!!!");
        }

        QuoteDetails quoteDetails = quoteMapper.toQuoteDetails(optQuote.get());

        // Get attached activities for the quote
//        List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(
//                quoteId, "QUOTE", null, accountId, language
//        );
//        quoteDetails.setAttachedActivities(attachedActivities);

        return new Response<>(StatusCode.OK, "Get quote details success!!!", quoteDetails);
    }

    @Override
    public Response<QuoteKanbanResponse> getQuotesKanban(String filters, String groupBy, String search, Long accountId, String language) {
        List<String> searchFields = Arrays.asList(
                "name",
                "code"
        );

        List<String> viewFields = Arrays.asList(
                "id",
                "name",
                "code",
                "status",
                "due_date",
                "total_cost"
        );

        GenericKanbanResponse kanbanResponse = coreQueryService.getKanban(
                filters, groupBy, search, accountId, language, "quotes", searchFields, viewFields, "QUOTE", quoteRepository
        );

        if (kanbanResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<QuoteKanbanView> quoteKanbanViews = new ArrayList<>();
        for (Object[] itemObject : kanbanResponse.getItemObjects()) {
            QuoteKanbanView quoteKanbanView = new QuoteKanbanView();
            quoteKanbanView.setId((Long) itemObject[0]);
            quoteKanbanView.setName((String) itemObject[1]);
            quoteKanbanView.setCode((String) itemObject[2]);
            quoteKanbanView.setStatus((String) itemObject[3]);
            quoteKanbanView.setDueDate((Date) itemObject[4]);
            quoteKanbanView.setTotalCost((Integer) itemObject[5]);
            quoteKanbanView.setGroupField((String) itemObject[6]);

            quoteKanbanViews.add(quoteKanbanView);
        }

        QuoteKanbanResponse quoteKanbanResponse = new QuoteKanbanResponse();
        quoteKanbanResponse.setGroupItems(kanbanResponse.getGroupItems());
        quoteKanbanResponse.setQuotes(quoteKanbanViews);
        return new Response<>(StatusCode.OK, "Get quotes kanban success!!!", quoteKanbanResponse);
    }

    @Override
    public Response<QuoteSplitViewResponse> getQuotesSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = List.of(
                "name",
                "code"
        );

        // Define view fields
        List<String> viewFields = List.of(
                "id",
                "name",
                "code",
                "status",
                "due_date",
                "discount",
                "tax_cost",
                "shipping_cost",
                "total_cost",
                "contact_id",
                "opportunity_id",
                "account_id"
        );

        GenericSplitViewResponse<Object[]> genericResponse = coreQueryService.getSplitView(
                filters, search, sortBy, sortDirection, page, limit,
                "quotes", searchFields, viewFields, quoteRepository
        );

        if (genericResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<QuoteSplitView> quotes = new ArrayList<>();
        for (Object[] quoteObject : genericResponse.getData().getContent()) {
            QuoteSplitView quoteSplitView = new QuoteSplitView();
            // Set quote fields based on the returned data
            quoteSplitView.setId((Long) quoteObject[0]);
            quoteSplitView.setName((String) quoteObject[1]);
            quoteSplitView.setCode((String) quoteObject[2]);
            quoteSplitView.setStatus((String) quoteObject[3]);
            quoteSplitView.setDueDate((Date) quoteObject[4]);
            quoteSplitView.setDiscount((Float) quoteObject[5]);
            quoteSplitView.setTaxCost((Integer) quoteObject[6]);
            quoteSplitView.setShippingCost((Integer) quoteObject[7]);
            quoteSplitView.setTotalCost((Integer) quoteObject[8]);
            Long contactId = (Long) quoteObject[9];
            if (contactId != null) {
                Contact contact = contactService.findById(contactId);
                if (contact != null) {
                    LookUpView contactLookUpView = new LookUpView();
                    contactLookUpView.setId(contact.getId());
                    contactLookUpView.setName(contact.getFirstName() + " " + contact.getLastName());
                    quoteSplitView.setContact(contactLookUpView);
                }
            }
            

            Long opportunityId = (Long) quoteObject[10];
            if (opportunityId != null) {
                Opportunity opportunity = opportunityService.findById(opportunityId);
                if (opportunity != null) {
                    LookUpView opportunityLookUpView = new LookUpView();
                    opportunityLookUpView.setId(opportunity.getId());
                    opportunityLookUpView.setName(opportunity.getName());
                    quoteSplitView.setOpportunity(opportunityLookUpView);
                }
            }

            Long linkedAccountId = (Long) quoteObject[11];
            if (linkedAccountId != null) {
                Account account = accountService.findById(linkedAccountId);
                if (account != null) {
                    LookUpView accountLookUpView = new LookUpView();
                    accountLookUpView.setId(account.getId());
                    accountLookUpView.setName(account.getName());
                    quoteSplitView.setAccount(accountLookUpView);
                }
            }

            quotes.add(quoteSplitView);
        }

        Page<QuoteSplitView> quotesPage = new PageImpl<>(
                quotes,
                genericResponse.getData().getPageable(),
                genericResponse.getData().getTotalElements()
        );

        QuoteSplitViewResponse response = new QuoteSplitViewResponse();
        response.setQuotes(quotesPage);
        return new Response<>(StatusCode.OK, "Get quotes split view success!!!", response);
    }

    @Override
    public Response<Boolean> updateKanbanQuote(Long quoteId, String fieldName, String fieldValue, Long accountId, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "opportunities");
        jsonFilter.put("id", quoteId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);

        quoteRepository.updateKanbanData(jsonFilter.toString());
        return new Response<>(StatusCode.OK, "Update quote kanban success!!!", true);
    }

    @Override
    public List<QuoteView> getQuotesByAccountId(Long accountId) {
        List<Quote> quotes = quoteRepository.findByAccountId(accountId);
        return quoteMapper.toQuoteView(quotes);
    }

    @Override
    public Quote findById(Long id) {
        return quoteRepository.findById(id).orElse(null);
    }


    /**
     * Update quote relationships based on the provided IDs in the request
     */
    private void updateQuoteRelationships(UpdateQuoteRequest request, Quote quote) {
        if (request.getAccountId() != null) {
            Account account = accountService.findById(request.getAccountId());
            if (account == null) {
                return;
            }
            quote.setAccount(account);
        }

        if (request.getContactId() != null) {
            Contact contact = contactService.findById(request.getContactId());
            if (contact == null) {
                return;
            }
            quote.setContact(contact);
        }

        if (request.getOpportunityId() != null) {
            Opportunity opportunity = opportunityService.findById(request.getOpportunityId());
            if (opportunity == null) {
                return;
            }
            quote.setOpportunity(opportunity);
        }
    }
}