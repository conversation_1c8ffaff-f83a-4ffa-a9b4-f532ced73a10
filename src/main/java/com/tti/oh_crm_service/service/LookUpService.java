package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import org.springframework.data.domain.Page;

public interface LookUpService {
    public Response<Page<LookUpUserView>> lookUpUsers(String keywordSearch, int page, int limit);
    public Response<Page<LookUpAccountView>> lookUpAccounts(String keywordSearch, int page, int limit);
    public Response<Page<LookUpContactView>> lookUpContacts(String keywordSearch, int page, int limit);
    public Response<Page<LookUpContractView>> lookUpContracts(String keywordSearch, int page, int limit);
    public Response<Page<LookUpOrderView>> lookUpOrders(String keywordSearch, int page, int limit);
    public Response<Page<LookupModuleShortView>> lookUpCampaigns(String keywordSearch, int page, int limit);
    public Response<Page<LookUpOpportunityView>> lookUpOpportunities(String keywordSearch, int page, int limit);
    public Response<Page<LookUpLeadView>> lookUpLeads(String keywordSearch, int page, int limit);
    public Response<Page<RoleView>> lookUpRoles(String keywordSearch, int page, int limit);
}
