package com.tti.oh_crm_service.service;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.tti.oh_crm_service.client.IdAccountFeignClient;
import com.tti.oh_crm_service.entity.*;

import com.tti.oh_crm_service.utils.FileUtils;
import com.tti.oh_crm_service.utils.ImageUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EListType;
import com.tti.oh_crm_service.mapper.DataPermissionMapper;
import com.tti.oh_crm_service.mapper.DataPermissionShareRuleMapper;
import com.tti.oh_crm_service.mapper.DepartmentMapper;
import com.tti.oh_crm_service.mapper.FieldSettingsMapper;
import com.tti.oh_crm_service.mapper.GroupSettingsMapper;
import com.tti.oh_crm_service.mapper.LayoutSettingsMapper;
import com.tti.oh_crm_service.mapper.LeadStatusSettingsMapper;
import com.tti.oh_crm_service.mapper.ModuleMapper;
import com.tti.oh_crm_service.mapper.OpportunityStageSettingsMapper;
import com.tti.oh_crm_service.mapper.ProfileMapper;
import com.tti.oh_crm_service.mapper.ProfilePermissionMapper;
import com.tti.oh_crm_service.mapper.RoleMapper;
import com.tti.oh_crm_service.mapper.TblListMapper;
import com.tti.oh_crm_service.mapper.TblListValueMapper;
import com.tti.oh_crm_service.mapper.UserMapper;
import com.tti.oh_crm_service.model.DataPermission;
import com.tti.oh_crm_service.model.DataPermissionShareFor;
import com.tti.oh_crm_service.model.DataPermissionShareRule;
import com.tti.oh_crm_service.model.Department;
import com.tti.oh_crm_service.model.FieldSettings;
import com.tti.oh_crm_service.model.GroupSettings;
import com.tti.oh_crm_service.model.LayoutSettings;
import com.tti.oh_crm_service.model.LeadStatusSettings;
import com.tti.oh_crm_service.model.TblListValue;
import com.tti.oh_crm_service.model.Module;
import com.tti.oh_crm_service.model.OpportunityStageSettings;
import com.tti.oh_crm_service.model.ModuleStagePlaybook;
import com.tti.oh_crm_service.model.GroupedModulePlaybookProcess;
import com.tti.oh_crm_service.model.Profile;
import com.tti.oh_crm_service.model.ProfilePermissionModuleLink;
import com.tti.oh_crm_service.model.Role;
import com.tti.oh_crm_service.model.TblList;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.DataPermissionRepository;
import com.tti.oh_crm_service.repository.DataPermissionShareForRepository;
import com.tti.oh_crm_service.repository.DataPermissionShareRuleRepository;
import com.tti.oh_crm_service.repository.DepartmentRepository;
import com.tti.oh_crm_service.repository.FieldSettingsRepository;
import com.tti.oh_crm_service.repository.GroupSettingsRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import com.tti.oh_crm_service.repository.LeadStatusSettingsRepository;
import com.tti.oh_crm_service.repository.TblListRepository;
import com.tti.oh_crm_service.repository.TblListValueRepository;
import com.tti.oh_crm_service.repository.ModuleRepository;
import com.tti.oh_crm_service.repository.OpportunityStageSettingsRepository;
import com.tti.oh_crm_service.repository.ProfilePermissionModuleRepository;
import com.tti.oh_crm_service.repository.ProfileRepository;
import com.tti.oh_crm_service.repository.RoleRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.repository.ModuleStagePlaybookRepository;
import com.tti.oh_crm_service.repository.GroupedModulePlaybookProcessRepository;
import com.tti.oh_crm_service.entity.CreateIdAccountRemoteRequest;
import com.tti.oh_crm_service.utils.CodeGeneratorUtils;


import jakarta.transaction.Transactional;

@Service
@Slf4j
public class SettingServiceImpl implements SettingService {
    @Autowired
    private RoleMapper roleMapper;
    @Autowired
    private ProfileMapper profileMapper;
    @Autowired
    private ModuleMapper moduleMapper;
    @Autowired
    private DepartmentMapper departmentMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private LayoutSettingsMapper layoutSettingsMapper;
    @Autowired
    private GroupSettingsMapper groupSettingsMapper;
    @Autowired
    private FieldSettingsMapper fieldSettingsMapper;
    @Autowired
    private TblListMapper tblListMapper;
    @Autowired
    private TblListValueMapper tblListValueMapper;
    @Autowired
    private LeadStatusSettingsMapper leadStatusSettingsMapper;
    @Autowired
    private OpportunityStageSettingsMapper opportunityStageSettingsMapper;
    @Autowired
    private ProfilePermissionMapper profilePermissionMapper;
    @Autowired
    private DataPermissionMapper dataPermissionMapper;
    @Autowired
    private DataPermissionShareRuleMapper dataPermissionShareRuleMapper;

    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private ProfileRepository profileRepository;
    @Autowired
    private ModuleRepository moduleRepository;
    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private ProfilePermissionModuleRepository profilePermissionModuleRepository;
    @Autowired
    private UserRepository userRepository;
    // @Autowired
    // private ProfilePermissionModuleRepository profilePermissionModuleRepository;
    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;
    @Autowired
    private GroupSettingsRepository groupSettingsRepository;
    @Autowired
    private FieldSettingsRepository fieldSettingsRepository;
    @Autowired
    private TblListRepository tblListRepository;
    @Autowired
    private TblListValueRepository tblListValueRepository;
    @Autowired
    private LeadStatusSettingsRepository leadStatusSettingsRepository;
    @Autowired
    private OpportunityStageSettingsRepository opportunityStageSettingsRepository;
    @Autowired
    private DataPermissionRepository dataPermissionRepository;
    @Autowired
    private DataPermissionShareRuleRepository dataPermissionShareRuleRepository;
    @Autowired
    private DataPermissionShareForRepository dataPermissionShareForRepository;
    @Autowired
    private ModuleStagePlaybookRepository moduleStagePlaybookRepository;
    @Autowired
    private GroupedModulePlaybookProcessRepository groupedModulePlaybookProcessRepository;
    @Autowired
    private IdAccountFeignClient idAccountFeignClient;
    @Autowired
    private OrganizationLevelLabelService organizationLevelLabelService;

    @Override
    @Transactional
    public Response<RoleView> createRole(CreateRoleRequest request, Long accountId, String language) {
        if (request.getName() == null || request.getName().trim().equalsIgnoreCase("") || roleRepository.existsByName(request.getName())) {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role name is null or duplicated!!!");
        }

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Role newRole = new Role();
        roleMapper.create(request, newRole);
        if (request.getReportToId() != null) {
            Optional<Role> optReportTo = roleRepository.findById(request.getReportToId());
            if (optReportTo.isPresent()) {
                newRole.setReportTo(optReportTo.get());
            } else {
                newRole.setReportTo(null);
            }
        } else {
            newRole.setReportTo(null);
        }
        if (optUser.isPresent()) {
            newRole.setCreatedBy(optUser.get());
            newRole.setLastModifiedBy(optUser.get());
        }

        newRole = roleRepository.save(newRole);
        return new Response<RoleView>(StatusCode.OK, "Create role success!!!", roleMapper.toRoleView(newRole));
    }

    @Override
    public Response<List<RoleView>> getRoles(Long accountId, String language) {
        List<Role> roles = roleRepository.findAll();
        return new Response<List<RoleView>>(StatusCode.OK, "List of roles!!!", roleMapper.toRoleView(roles));
    }

    @Override
    public Response<RoleView> updateRole(UpdateRoleRequest request, Long accountId, String language) {
        if (request.getId() == null || !roleRepository.existsById(request.getId())) {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role ID is null or not found!!!");
        }

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<Role> optRole = roleRepository.findById(request.getId());
        if (optRole.isPresent()) {
            Role role = optRole.get();
            roleMapper.update(request, role);
            if (request.getReportToId() != null) {
                Optional<Role> optReportTo = roleRepository.findById(request.getReportToId());
                if (optReportTo.isPresent()) {
                    role.setReportTo(optReportTo.get());
                }
            }
            if (optUser.isPresent()) {
                role.setLastModifiedBy(optUser.get());
            }
            role = roleRepository.save(role);
            return new Response<RoleView>(StatusCode.OK, "Update role success!!!", roleMapper.toRoleView(role));
        } else {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role not found!!!");
        }
    }

    @Override
    @Transactional
    public Response<RoleView> deleteRoles(TransferAndDeleteRoleRequest request, Long accountId, String language) {
        if (request.getId() == null || request.getTransferToId() == null || !roleRepository.existsById(request.getId()) || !roleRepository.existsById(request.getTransferToId())) {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role ID are null or not exists!!!");
        }

        Optional<Role> optRole = roleRepository.findById(request.getId());
        Optional<Role> optTransferToRole = roleRepository.findById(request.getTransferToId());

        // Find Users belong to Role to transfer
        List<User> users = userRepository.findByRole(optRole.get());
        for (User user : users) {
            user.setRole(optTransferToRole.get());
            userRepository.save(user);
        }

        // Find roles to transfer
        List<Role> roles = roleRepository.findByReportTo(optRole.get());
        for (Role role : roles) {
            role.setReportTo(optTransferToRole.get());
            roleRepository.save(role);
        }

        // Delete role
        roleRepository.delete(optRole.get());

        return new Response<RoleView>(StatusCode.OK, "Delete roles success!!!");
    }

    @Override
    public Response<List<ProfileView>> getProfiles(Long accountId, String language) {
        List<Profile> profiles = profileRepository.findAll();
        return new Response<List<ProfileView>>(StatusCode.OK, "List of profiles!!!", profileMapper.toProfileView(profiles));
    }


    @Override
    public Response<ProfileView> createProfile(CreateProfileRequest request, Long accountId, String language) {
        if (request.getName() == null || request.getName().trim().equalsIgnoreCase("") || profileRepository.existsByName(request.getName())) {
            return new Response<ProfileView>(StatusCode.BAD_REQUEST, "Profile name is null or duplicated!!!");
        }

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Profile newProfile = new Profile();
        profileMapper.create(request, newProfile);

        if (optUser.isPresent()) {
            newProfile.setCreatedBy(optUser.get());
            newProfile.setLastModifiedBy(optUser.get());
        }

        newProfile = profileRepository.save(newProfile);

        // TODO: clone permissions from existing profile
        if (request.getCloneFrom() != null) {
            // Get permissions from existing profile
            List<ProfilePermissionModuleLink> permissions = profilePermissionModuleRepository.findByProfileId(request.getCloneFrom());
            // Clone to new profile
            if (permissions != null && !permissions.isEmpty()) {
                for (ProfilePermissionModuleLink permission : permissions) {
                    ProfilePermissionModuleLink newPermission = new ProfilePermissionModuleLink();
                    newPermission.setModuleCode(permission.getModuleCode());
                    newPermission.setPermission(permission.getPermission());
                    newPermission.setProfileId(newProfile.getId());
                    profilePermissionModuleRepository.save(newPermission);
                }
            }
        }
        
        return new Response<ProfileView>(StatusCode.OK, "Create profile success!!!", profileMapper.toProfileView(newProfile));
    }

    @Override
    public Response<Boolean> updateProfilePermissions(UpdateProfilePermissionsRequest request) {
        if (request.getProfileId() == null || !profileRepository.existsById(request.getProfileId())) {
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Profile is not found!!!", false);
        }

        // Remove permissions from profile
        if (request.getRemovePermissions() != null && !request.getRemovePermissions().isEmpty()) {
            for (ProfilePermissionView permission : request.getRemovePermissions()) {
                Optional<ProfilePermissionModuleLink> optPermission = profilePermissionModuleRepository
                        .findByProfileIdAndModuleCodeAndPermission(request.getProfileId(), permission.getModuleCode(), permission.getPermission());
                if (optPermission.isPresent()) {
                    profilePermissionModuleRepository.delete(optPermission.get());
                }
            }
        }

        // Add permissions to profile
        if (request.getAddPermissions() != null && !request.getAddPermissions().isEmpty()) {
            for (ProfilePermissionView permission : request.getAddPermissions()) {
                Optional<ProfilePermissionModuleLink> optPermission = profilePermissionModuleRepository
                        .findByProfileIdAndModuleCodeAndPermission(request.getProfileId(), permission.getModuleCode(), permission.getPermission());
                if (optPermission.isPresent()) {
                    continue;
                }

                ProfilePermissionModuleLink newPermission = new ProfilePermissionModuleLink();
                newPermission.setModuleCode(permission.getModuleCode());
                newPermission.setPermission(permission.getPermission());
                newPermission.setProfileId(request.getProfileId());
                profilePermissionModuleRepository.save(newPermission);
            }
        }

        return new Response<Boolean>(StatusCode.OK, "Update profile permissions success!!!", true);
    }

    @Override
    public Response<ProfileView> updateProfile(UpdateProfileRequest request, Long accountId, String language) {
        if (request.getId() == null || !profileRepository.existsById(request.getId())) {
            return new Response<ProfileView>(StatusCode.BAD_REQUEST, "Profile ID is null or not found!!!");
        }

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<Profile> optProfile = profileRepository.findById(request.getId());
        if (optProfile.isPresent()) {
            Profile profile = optProfile.get();
            profileMapper.update(request, profile);
            if (optUser.isPresent()) {
                profile.setLastModifiedBy(optUser.get());
            }
            profile = profileRepository.save(profile);
            return new Response<ProfileView>(StatusCode.OK, "Update role success!!!", profileMapper.toProfileView(profile));
        } else {
            return new Response<ProfileView>(StatusCode.BAD_REQUEST, "Role not found!!!");
        }
    }

    @Override
    public Response<Boolean> deleteProfiles(List<Long> profileIds, Long accountId, String language) {
        if (profileIds == null || profileIds.isEmpty()) {
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Profile IDs are null or empty!!!");
        }

        profileRepository.deleteAllById(profileIds);
        return new Response<Boolean>(StatusCode.OK, "Delete roles success!!!", true);
    }

    @Override
    public Response<List<ProfilePermissionView>> getProfilePermissions(Long profileId) {
        List<ProfilePermissionModuleLink> permissions = profilePermissionModuleRepository.findByProfileId(profileId);
        return new Response<List<ProfilePermissionView>>(StatusCode.OK, "Get profile permissions success!!!", profilePermissionMapper.toProfilePermissionView(permissions));
    }

    @Override
    public Response<ModuleView> createModule(CreateModuleRequest request) {
        Module newModule = new Module();
        moduleMapper.create(request, newModule);
        newModule = moduleRepository.save(newModule);
        return new Response<ModuleView>(StatusCode.OK, "Create module success!!!", moduleMapper.toModuleView(newModule));
    }

    @Override
    public Response<List<ModuleView>> getModules(Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if(!optUser.isPresent()) {
            return new Response<List<ModuleView>>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        
        List<Module> modules = moduleRepository.findByLanguage(language);

        return new Response<List<ModuleView>>(StatusCode.OK, "Get list modules success!!!", moduleMapper.toModuleView(modules));
    }

    @Override
    public Response<DepartmentView> createDepartment(CreateDepartmentRequest request, Long accountId, String language) {
        
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<DepartmentView>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        // Validate required fields
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department name is required!");
        }
        
        if (request.getCode() == null || request.getCode().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department code is required!");
        }
        
        if (request.getOrganizationLevel() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Organization level is required!");
        }
        
        Department newDepartment = new Department();
        departmentMapper.create(request, newDepartment, optUser.get());
        
        // Set parent department if specified
        if (request.getParentDepartmentId() != null) {
            Optional<Department> optParent = departmentRepository.findById(request.getParentDepartmentId());
            if (optParent.isPresent()) {
                newDepartment.setParentDepartment(optParent.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Parent department not found!");
            }
        }
        
        // Set department head if specified
        if (request.getDepartmentHeadId() != null) {
            Optional<User> optHead = userRepository.findById(request.getDepartmentHeadId());
            if (optHead.isPresent()) {
                newDepartment.setDepartmentHead(optHead.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Department head not found!");
            }
        }
        
        newDepartment = departmentRepository.save(newDepartment);
        return new Response<>(StatusCode.OK, "Create department success!!!", departmentMapper.toDepartmentView(newDepartment));
    }

    @Override
    public Response<DepartmentView> updateDepartment(UpdateDepartmentRequest request, Long accountId, String language) {
        // Validate required fields
        if (request.getId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department ID is required!");
        }

        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }
        
        Optional<Department> optDepartment = departmentRepository.findById(request.getId());
        if (!optDepartment.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department not found!");
        }
        
        Department department = optDepartment.get();
        
        // Validate name if provided
        if (request.getName() != null && request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department name cannot be empty!");
        }
        
        // Validate code if provided
        if (request.getCode() != null && request.getCode().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department code cannot be empty!");
        }
        
        // Update basic fields using mapper
        departmentMapper.update(request, department, optUser.get());
        
        // Set parent department if specified
        if (request.getParentDepartmentId() != null) {
            // Prevent setting self as parent
            if (request.getParentDepartmentId().equals(request.getId())) {
                return new Response<>(StatusCode.BAD_REQUEST, "Department cannot be its own parent!");
            }
            
            Optional<Department> optParent = departmentRepository.findById(request.getParentDepartmentId());
            if (optParent.isPresent()) {
                // Check for circular dependency
                Department parent = optParent.get();
                if (isCircularDependency(department, parent)) {
                    return new Response<>(StatusCode.BAD_REQUEST, "Circular dependency detected in department hierarchy!");
                }
                department.setParentDepartment(parent);
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Parent department not found!");
            }
        }
        
        // Set department head if specified
        if (request.getDepartmentHeadId() != null) {
            Optional<User> optHead = userRepository.findById(request.getDepartmentHeadId());
            if (optHead.isPresent()) {
                department.setDepartmentHead(optHead.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Department head not found!");
            }
        }
        
        department = departmentRepository.save(department);
        return new Response<>(StatusCode.OK, "Update department success!!!", departmentMapper.toDepartmentView(department));
    }
    
    /**
     * Check if setting the given parent would create a circular dependency
     * @param department the department being updated
     * @param proposedParent the proposed parent department
     * @return true if circular dependency would be created
     */
    private boolean isCircularDependency(Department department, Department proposedParent) {
        Department current = proposedParent;
        while (current != null) {
            if (current.getId().equals(department.getId())) {
                return true; // Circular dependency found
            }
            current = current.getParentDepartment();
        }
        return false;
    }

    @Override
    public Response<List<DepartmentView>> getDepartments() {
        List<Department> departments = departmentRepository.findAll();
        return new Response<List<DepartmentView>>(StatusCode.OK, "Get list departments success!!!", departmentMapper.toDepartmentView(departments));
    }

    @Override
    public Response<List<DepartmentViewTreeNode>> getDepartmentsTree(Long accountId, String language) {
        try {
            // Fetch all departments from the database
            List<Department> allDepartments = departmentRepository.findAll();
            
            // Convert departments to tree nodes using mapper
            List<DepartmentViewTreeNode> allNodes = new ArrayList<>();
            for (Department dept : allDepartments) {
                allNodes.add(departmentMapper.toDepartmentViewTreeNode(dept));
            }
            
            // Build the tree structure
            List<DepartmentViewTreeNode> rootNodes = buildDepartmentTree(allNodes);
            
            return new Response<>(StatusCode.OK, "Get departments tree success!!!", rootNodes);
        } catch (Exception e) {
            return new Response<>(StatusCode.INTERNAL_SERVER_ERROR, "Error getting departments tree: " + e.getMessage());
        }
    }
    
    /**
     * Build a department tree from a flat list of department tree nodes.
     * @param allNodes flat list of all department nodes
     * @return list of root nodes with children populated recursively
     */
    private List<DepartmentViewTreeNode> buildDepartmentTree(List<DepartmentViewTreeNode> allNodes) {
        List<DepartmentViewTreeNode> rootNodes = new ArrayList<>();
        
        // Create a map for quick lookup by ID
        Map<Long, DepartmentViewTreeNode> nodeMap = new HashMap<>();
        for (DepartmentViewTreeNode node : allNodes) {
            nodeMap.put(node.getId(), node);
            node.setChildren(new ArrayList<>()); // Initialize children list
        }
        
        // Build the tree by assigning children to their parents
        for (DepartmentViewTreeNode node : allNodes) {
            if (node.getParentDepartmentId() == null) {
                // This is a root node
                rootNodes.add(node);
            } else {
                // This is a child node, find its parent and add it as a child
                DepartmentViewTreeNode parent = nodeMap.get(node.getParentDepartmentId());
                if (parent != null) {
                    parent.getChildren().add(node);
                }
            }
        }
        
        return rootNodes;
    }
    
    @Override
    public Response<DepartmentDetails> getDepartmentDetails(Long departmentId, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }
        // Validate required parameters
        if (departmentId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department ID is required!");
        }
        
        // Find the department by ID
        Optional<Department> optDepartment = departmentRepository.findById(departmentId);
        if (!optDepartment.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Department not found!");
        }
        
        Department department = optDepartment.get();
        
        // Convert to DepartmentDetails using mapper
        DepartmentDetails departmentDetails = departmentMapper.toDepartmentDetails(department);
        
        // Set organization level label using the organization level label service
        if (department.getOrganizationLevel() != null) {
            OrganizationLevelLabelView organizationLevelLabelView = organizationLevelLabelService.getOrganizationLevelLabel(department.getOrganizationLevel(), language);
            if (organizationLevelLabelView != null) {
                departmentDetails.setOrganizationLevelLabel(organizationLevelLabelView.getLabel());
            }
        }
        
        return new Response<>(StatusCode.OK, "Get department details success!!!", departmentDetails);
    }
    
    @Override
    @Transactional
    public Response<Boolean> deleteDepartment(TransferAndDeleteDepartmentRequest request, Long accountId, String language) {
        if (request.getId() == null || !departmentRepository.existsById(request.getId())) {
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Department ID is null or not exists!!!");
        }

        Optional<Department> optDepartment = departmentRepository.findById(request.getId());
        Department department = optDepartment.get();

        // Check if there are child departments
        List<Department> childDepartments = departmentRepository.findByParentDepartment(department);
        
        // Check if there are users assigned to this department
        List<User> users = userRepository.findByDepartment(department);

        // If there are child departments or users, transferToId is required
        if ((!childDepartments.isEmpty() || !users.isEmpty()) && request.getTransferToId() == null) {
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Transfer department ID is required when there are child departments or users assigned to this department!!!");
        }

        // If transferToId is provided, validate it exists
        if (request.getTransferToId() != null && !departmentRepository.existsById(request.getTransferToId())) {
            return new Response<Boolean>(StatusCode.BAD_REQUEST, "Transfer department ID does not exist!!!");
        }

        // Transfer users if any exist
        if (!users.isEmpty()) {
            Optional<Department> optTransferToDepartment = departmentRepository.findById(request.getTransferToId());
            for (User user : users) {
                user.setDepartment(optTransferToDepartment.get());
                userRepository.save(user);
            }
        }

        // Transfer child departments if any exist
        if (!childDepartments.isEmpty()) {
            Optional<Department> optTransferToDepartment = departmentRepository.findById(request.getTransferToId());
            for (Department childDepartment : childDepartments) {
                childDepartment.setParentDepartment(optTransferToDepartment.get());
                departmentRepository.save(childDepartment);
            }
        }

        // Delete department
        departmentRepository.delete(department);

        return new Response<Boolean>(StatusCode.OK, "Delete department success!!!", true);
    }
    
    @Override
    @Transactional
    public Mono<Response<UserView>> createUser(CreateUserRequest request, String authToken, Long organizationId, Long accountId, String language) {
        // Validate user performing the creation
        Optional<User> optCurrentUser = userRepository.findByAccountId(accountId);
        if (!optCurrentUser.isPresent()) {
            return Mono.just(new Response<>(StatusCode.BAD_REQUEST, "User not found!"));
        }

        // Validate required fields
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            return Mono.just(new Response<UserView>(StatusCode.BAD_REQUEST, "Email is required!!!"));
        }

        // Prepare headers for Feign client calls
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authToken);

        // Step 1: Check if email exists in id-service (reactive chain)
        return idAccountFeignClient.checkAccountViaEmail(headers, request.getEmail())
            .flatMap(checkResponse -> {
                if (checkResponse != null && checkResponse.getCode() == 200 && checkResponse.getData() != null) {
                    log.info("Email exists in id-service, accountId: {}", checkResponse.getData());
                    // Email exists in id-service, use the returned accountId
                    Long userAccountId = checkResponse.getData();

                    // Check if this accountId is already used in our system
                    if (userRepository.existsByAccountId(userAccountId)) {
                        return Mono.just(new Response<UserView>(StatusCode.BAD_REQUEST, "Account is already registered in the system!!!"));
                    }

                    // Set accountId and continue with user creation
                    request.setAccountId(userAccountId);
                    return createUserWithAccountId(request, optCurrentUser.get());
                } else {
                    log.info("Email does not exist in id-service, creating new account...");
                    // Step 2: Email doesn't exist in id-service, create new account
                    CreateIdAccountRemoteRequest createAccountRequest = new CreateIdAccountRemoteRequest();
                    createAccountRequest.setEmail(request.getEmail());
                    createAccountRequest.setFullName((request.getFirstName() != null ? request.getFirstName() : "") + " " +
                                                   (request.getLastName() != null ? request.getLastName() : ""));
                    createAccountRequest.setDisplayName(createAccountRequest.getFullName());
                    createAccountRequest.setPhone(request.getPhone() != null ? request.getPhone() : request.getMobile());
                    createAccountRequest.setGender(request.getGender());
                    createAccountRequest.setOrganizationId(organizationId);

                    return idAccountFeignClient.createAccount(headers, createAccountRequest)
                        .flatMap(createResponse -> {
                            if (createResponse != null && createResponse.getCode() == 200 && createResponse.getData() != null) {
                                log.info("New account created in id-service, accountId: {}", createResponse.getData().getId());
                                Long userAccountId = createResponse.getData().getId().longValue();
                                request.setAccountId(userAccountId);
                                return createUserWithAccountId(request, optCurrentUser.get());
                            } else {
                                log.error("Failed to create account in id-service, response: {}", createResponse);
                                String failedReason = createResponse != null ? createResponse.getMessage() : "Unknown error";
                                return Mono.just(new Response<UserView>(StatusCode.BAD_REQUEST, "Failed to create account in id-service. " + failedReason));
                            }
                        });
                }
            })
            .onErrorResume(error -> {
                log.error("Error occurred during user creation process for email: {}", request.getEmail(), error);
                log.error("Error details - Type: {}, Message: {}", error.getClass().getSimpleName(), error.getMessage());

                // Log additional context information
                log.error("User creation context - Email: {}, FirstName: {}, LastName: {}, Phone: {}, Mobile: {}, OrganizationId: {}",
                    request.getEmail(),
                    request.getFirstName(),
                    request.getLastName(),
                    request.getPhone(),
                    request.getMobile(),
                    organizationId);

                return Mono.just(new Response<UserView>(StatusCode.INTERNAL_SERVER_ERROR, "Error communicating with id-service: " + error.getMessage()));
            });

    }

    // Helper method for user creation with accountId
    @Transactional
    protected Mono<Response<UserView>> createUserWithAccountId(CreateUserRequest request, User currentUser) {
        User newUser = new User();

        userMapper.create(request, newUser);

        // Set audit fields
        newUser.setCreatedBy(currentUser);
        newUser.setLastModifiedBy(currentUser);

        if (request.getRoleId() != null) {
            Optional<Role> optRole = roleRepository.findById(request.getRoleId());
            if (optRole.isPresent()) {
                newUser.setRole(optRole.get());
            } else {
                return Mono.just(new Response<>(StatusCode.BAD_REQUEST, "Role not found!"));
            }
        } else {
            newUser.setRole(null);
        }

        if (request.getProfileId() != null) {
            Optional<Profile> optProfile = profileRepository.findById(request.getProfileId());
            if (optProfile.isPresent()) {
                newUser.setProfile(optProfile.get());
            } else {
                return Mono.just(new Response<>(StatusCode.BAD_REQUEST, "Profile not found!"));
            }
        } else {
            newUser.setProfile(null);
        }

        if (request.getDepartmentId() != null) {
            Optional<Department> optDepartment = departmentRepository.findById(request.getDepartmentId());
            if (optDepartment.isPresent()) {
                newUser.setDepartment(optDepartment.get());
            } else {
                return Mono.just(new Response<>(StatusCode.BAD_REQUEST, "Department not found!"));
            }
        } else {
            newUser.setDepartment(null);
        }

        newUser = userRepository.save(newUser);
        return Mono.just(new Response<UserView>(StatusCode.OK, "Create user success!!!", userMapper.toUserView(newUser)));
    }

    @Override
    public Response<List<UserView>> getUsers() {
        List<User> users = userRepository.findAll();
        return new Response<List<UserView>>(StatusCode.OK, "Get list users success!!!", userMapper.toUserView(users));
    }

    @Override
    @Transactional
    public Response<UserView> updateUser(UpdateUserRequest request, Long accountId, String language) {
        // Validate user performing the update
        Optional<User> optCurrentUser = userRepository.findByAccountId(accountId);
        if (!optCurrentUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "User ID is required!");
        }

        // Find the user to update
        Optional<User> optUser = userRepository.findById(request.getId());
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        User user = optUser.get();
        User currentUser = optCurrentUser.get();

        // Update user fields using mapper
        userMapper.update(request, user);

        // Handle role assignment
        if (request.getRoleId() != null) {
            Optional<Role> optRole = roleRepository.findById(request.getRoleId());
            if (optRole.isPresent()) {
                user.setRole(optRole.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Role not found!");
            }
        }

        // Handle profile assignment
        if (request.getProfileId() != null) {
            Optional<Profile> optProfile = profileRepository.findById(request.getProfileId());
            if (optProfile.isPresent()) {
                user.setProfile(optProfile.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Profile not found!");
            }
        }

        // Handle department assignment
        if (request.getDepartmentId() != null) {
            Optional<Department> optDepartment = departmentRepository.findById(request.getDepartmentId());
            if (optDepartment.isPresent()) {
                user.setDepartment(optDepartment.get());
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Department not found!");
            }
        }

        // Set audit fields
        user.setLastModifiedBy(currentUser);

        // Save the updated user
        try {
            user = userRepository.save(user);
            return new Response<>(StatusCode.OK, "User updated successfully!", userMapper.toUserView(user));
        } catch (Exception e) {
            return new Response<>(StatusCode.INTERNAL_SERVER_ERROR, "Error updating user: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Response<Boolean> deleteUser(Long userId, Long accountId, String language) {
        // Validate user performing the deletion
        Optional<User> optCurrentUser = userRepository.findByAccountId(accountId);
        if (!optCurrentUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (userId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "User ID is required!");
        }

        // Find the user to delete
        Optional<User> optUser = userRepository.findById(userId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        User user = optUser.get();

        // Check if this user is a department head for any department
        List<Department> departmentsLedByUser = departmentRepository.findAll().stream()
            .filter(dept -> dept.getDepartmentHead() != null && 
                          dept.getDepartmentHead().getId().equals(userId))
            .collect(Collectors.toList());

        if (!departmentsLedByUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Cannot delete user. This user is a department head. Please reassign department leadership first.");
        }

        // Delete the user
        try {
            userRepository.delete(user);
            return new Response<>(StatusCode.OK, "User deleted successfully!", true);
        } catch (Exception e) {
            return new Response<>(StatusCode.INTERNAL_SERVER_ERROR, "Error deleting user: " + e.getMessage(), false);
        }
    }

    @Override
    public Response<UserDetails> getUserDetails(Long userId, Long accountId, String language) {
        // Validate user performing the request
        Optional<User> optCurrentUser = userRepository.findByAccountId(accountId);
        if (!optCurrentUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (userId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "User ID is required!");
        }

        // Find the user by ID
        Optional<User> optUser = userRepository.findById(userId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        User user = optUser.get();

        // Convert to UserDetails using mapper
        UserDetails userDetails = userMapper.toUserDetails(user);

        return new Response<>(StatusCode.OK, "Get user details success!!!", userDetails);
    }

    @Override
    public Response<Page<UserView>> getUsersByRole(Long roleId, int page, int limit, String language) {
        if (roleId == null) {
            return new Response<Page<UserView>>(StatusCode.BAD_REQUEST, "Role ID is required!!!");
        }

        // Check if role exists
        Optional<Role> optRole = roleRepository.findById(roleId);
        if (!optRole.isPresent()) {
            return new Response<Page<UserView>>(StatusCode.BAD_REQUEST, "Role not found!!!");
        }

        // Create pageable object
        Pageable pageable = PageRequest.of(page, limit);

        // Find users by role with pagination
        Page<User> usersPage = userRepository.findByRoleId(roleId, pageable);

        // Convert to UserView page
        Page<UserView> userViewPage = usersPage.map(user -> userMapper.toUserView(user));

        return new Response<Page<UserView>>(StatusCode.OK, "Get users by role success!!!", userViewPage);
    }

    @Override
    public Response<RoleView> getRoleDetails(Long roleId, Long accountId, String language) {
        if (roleId == null) {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role ID is required!!!");
        }

        // Find the role by ID
        Optional<Role> optRole = roleRepository.findById(roleId);
        if (!optRole.isPresent()) {
            return new Response<RoleView>(StatusCode.BAD_REQUEST, "Role not found!!!");
        }

        Role role = optRole.get();
        RoleView roleView = roleMapper.toRoleView(role);

        return new Response<RoleView>(StatusCode.OK, "Get role details success!!!", roleView);
    }

    @Override
    public Response<ModuleView> updateModuleDisplayName(Long accountId, UpdateDisplayNameRequest request) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        if(!optUser.isPresent()) {
            return new Response<ModuleView>(StatusCode.BAD_REQUEST, "User not found!!!");
        }


        Optional<Module> optModule = moduleRepository.findById(request.getModuleId());
        if (optModule.isPresent()) {
            Module module = optModule.get();
            module.setDisplayName(request.getDisplayName());
            module.setLastModifiedBy(optUser.get());
            module = moduleRepository.save(module);
            return new Response<ModuleView>(StatusCode.OK, "Update module display name success!!!", moduleMapper.toModuleView(module));
        } else {
            return new Response<ModuleView>(StatusCode.BAD_REQUEST, "Module not found!!!");
        }
    }

    @Override
    public Response<List<ModuleView>> updateModules(Long accountId,  List<ModuleView> modules) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        if(!optUser.isPresent()) {
            return new Response<List<ModuleView>>(StatusCode.BAD_REQUEST, "User not found!!!");
        }

        List<Module> moduleList = new ArrayList<Module>();
        for (ModuleView moduleView : modules) {
            Optional<Module> optModule = moduleRepository.findById(moduleView.getId());
            if (optModule.isPresent()) {
                Module module = optModule.get();
                moduleMapper.toModule(moduleView, module);
                module.setLastModifiedBy(optUser.get());
                moduleList.add(module);
            }else{
                return new Response<List<ModuleView>>(StatusCode.BAD_REQUEST, "Module not found!!!");
            }
        }

        moduleRepository.saveAll(moduleList);
        List<ModuleView> moduleViews = moduleMapper.toModuleView(moduleList);
        return new Response<List<ModuleView>>(StatusCode.OK, "Update modules success!!!", moduleViews);
    }

    @Override
    public Response<ModuleSettings> getModuleSettings(String moduleCode, String language) {
        ModuleSettings moduleSettings = new ModuleSettings();

        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode(moduleCode);
        if (optLayoutSettings.isPresent()) {
            moduleSettings.setLayoutSettings(layoutSettingsMapper.toLayoutSettingsView(optLayoutSettings.get()));
        }

        List<GroupSettings> groupSettings = groupSettingsRepository.findByModuleCodeAndLanguage(moduleCode, language);
        moduleSettings.setLayoutGroupSettings(groupSettingsMapper.toLayoutGroupSettingsView(groupSettings));
        
        List<FieldSettings> fieldSettings = fieldSettingsRepository.findByModuleCodeAndLanguage(moduleCode, language);
        List<FieldSettingsView> fieldSettingsViews = fieldSettingsMapper.toFieldSettingsView(fieldSettings);
        for (FieldSettingsView fieldSettingsView  : fieldSettingsViews) {
            if (fieldSettingsView.getListCode() != null) {
                List<TblListValue> tblListValues = null;
                if (fieldSettingsView.getSortByAlphaBet()) {
                    tblListValues = tblListValueRepository.findByListCodeAndLanguageOrderByLabelAsc(fieldSettingsView.getListCode(), language);
                } else {
                    tblListValues = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(fieldSettingsView.getListCode(), language);
                }

                // Get settings for some list values
                List<TblListValueView> arrListValueViewWithSettings = null;
                switch (fieldSettingsView.getListCode()) {
                    case "LST_LEAD_STATUS":
                        arrListValueViewWithSettings = new ArrayList<>();
                        for (TblListValue tblListValue : tblListValues) {
                            ListValueViewWithSettings<LeadStatusSettingsView> listValueViewWithSettings = new ListValueViewWithSettings<>();
                            listValueViewWithSettings.setId(tblListValue.getId());
                            listValueViewWithSettings.setLabel(tblListValue.getLabel());
                            listValueViewWithSettings.setValue(tblListValue.getValue());
                            listValueViewWithSettings.setPosition(tblListValue.getPosition());

                            // Get settings of lead status list value
                            Optional<LeadStatusSettings> optLeadStatusSettings = leadStatusSettingsRepository.findByLeadStatusValue(tblListValue.getValue());
                            if (optLeadStatusSettings.isPresent()) {
                                listValueViewWithSettings.setSettings(leadStatusSettingsMapper.toLeadStatusSettingsView(optLeadStatusSettings.get()));
                            }

                            arrListValueViewWithSettings.add(listValueViewWithSettings);
                        }
                        break;
                    case "LST_OPPORTUNITY_STAGE":
                        arrListValueViewWithSettings = new ArrayList<>();
                        for (TblListValue tblListValue : tblListValues) {
                            ListValueViewWithSettings<OpportunityStageSettingsView> listValueViewWithSettings = new ListValueViewWithSettings<>();
                            listValueViewWithSettings.setId(tblListValue.getId());
                            listValueViewWithSettings.setLabel(tblListValue.getLabel());
                            listValueViewWithSettings.setValue(tblListValue.getValue());
                            listValueViewWithSettings.setPosition(tblListValue.getPosition());

                            // Get settings of opportunity stage list value
                            Optional<OpportunityStageSettings> optOpportunityStageSettings = opportunityStageSettingsRepository.findByOpportunityStageValue(tblListValue.getValue());
                            if (optOpportunityStageSettings.isPresent()) {
                                listValueViewWithSettings.setSettings(opportunityStageSettingsMapper.toOpportunityStageSettingsView(optOpportunityStageSettings.get()));
                            }

                            arrListValueViewWithSettings.add(listValueViewWithSettings);
                        }
                        break;
                    default:
                        arrListValueViewWithSettings = new ArrayList<>();
                        for (TblListValue tblListValue : tblListValues) {
                            ListValueViewWithSettings<LeadStatusSettingsView> listValueViewWithSettings = new ListValueViewWithSettings<>();
                            listValueViewWithSettings.setId(tblListValue.getId());
                            listValueViewWithSettings.setLabel(tblListValue.getLabel());
                            listValueViewWithSettings.setValue(tblListValue.getValue());
                            listValueViewWithSettings.setPosition(tblListValue.getPosition());
                            arrListValueViewWithSettings.add(listValueViewWithSettings);
                        }
                        break;
                }
                fieldSettingsView.setListItems(arrListValueViewWithSettings);
            }
        }
        moduleSettings.setFieldSettings(fieldSettingsViews);
        
        return new Response<ModuleSettings>(StatusCode.OK, "Get module settings!!!", moduleSettings);
    }

    @Override
    public Response<LayoutSettingsView> createLayoutSettings(CreateLayoutSettingsRequest request, Long accountId, String language) {
        LayoutSettings layoutSettings;

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode(request.getModuleCode());
        if (optLayoutSettings.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Layout settings already exist!!!");
        }

        layoutSettings = new LayoutSettings();
        if (optUser.isPresent()) {
            layoutSettings.setCreatedBy(optUser.get());
            layoutSettings.setLastModifiedBy(optUser.get());
        }
        layoutSettingsMapper.create(request, layoutSettings);

        // Insert layout settings
        layoutSettings = layoutSettingsRepository.save(layoutSettings);
        
        // Update Group settings
        if (request.getGroupSettings() != null) {
            for (UpdateGroupSettingsRequest groupSettingsRequest : request.getGroupSettings()) {
                GroupSettings groupSettings;
                Optional<GroupSettings> optLayoutGroupSettings = groupSettingsRepository.findByModuleCodeAndCodeAndLanguage(groupSettingsRequest.getModuleCode(), groupSettingsRequest.getCode(), language);
                if (!optLayoutGroupSettings.isPresent()) {
                    continue;
                }

                groupSettings = optLayoutGroupSettings.get();
                groupSettingsMapper.update(groupSettingsRequest, groupSettings);
                if (optUser.isPresent()) {
                    groupSettings.setLastModifiedBy(optUser.get());
                }
                
                // update field settings
                groupSettings = groupSettingsRepository.save(groupSettings);
            }
        }

        // Update Field Settings
        if (request.getFieldSettings() != null) {
            for (UpdateFieldSettingsRequest fieldSettingsRequest : request.getFieldSettings()) {
                FieldSettings fieldSettings;
                Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName(), language);
                if (!optFieldSettings.isPresent()) {
                    continue;
                }

                fieldSettings = optFieldSettings.get();
                fieldSettingsMapper.update(fieldSettingsRequest, fieldSettings);
                if (optUser.isPresent()) {
                    fieldSettings.setLastModifiedBy(optUser.get());
                }
                
                // Field is LIST type - check and update list.
                if (fieldSettingsRequest.getFieldType() == EFieldType.LIST) {
                    String listCode = CodeGeneratorUtils.generateListCode(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName());
        
                    if (fieldSettingsRequest.getListCustom()) {
                        fieldSettings.setListCustom(true);
                        fieldSettings.setListCode(listCode);

                        // Update list values to list
                        Boolean existByListCode = tblListRepository.existsByTypeAndCodeAndLanguage(EListType.CUSTOM, listCode, language);
                        if (existByListCode) { // List is existed, just add or update value items
                            // Delete list items
                            if (fieldSettingsRequest.getListDeletedItems() != null) {
                                for (TblListValueView item : fieldSettingsRequest.getListDeletedItems()) {
                                    tblListValueRepository.deleteById(item.getId());
                                }
                            }

                            // Add or update list items
                            if (fieldSettingsRequest.getListAddedItems() != null) {
                                for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                    TblListValue tblListValue;
                                    Optional<TblListValue> optTblListValue = tblListValueRepository.findByListCodeAndValueAndLanguage(listCode, item.getValue(), language);
                                    if (optTblListValue.isPresent()) { // Item is existed, just update label
                                        tblListValue = optTblListValue.get();
                                    } else {
                                        tblListValue = new TblListValue();
                                        tblListValue.setListCode(listCode);
                                        tblListValue.setValue(item.getValue());
                                        tblListValue.setLanguage(language);
                                        if (optUser.isPresent()) {
                                            tblListValue.setCreatedBy(optUser.get());
                                        }
                                    }
                                    tblListValue.setLabel(item.getLabel());
                                    tblListValue.setPosition(item.getPosition());
                                    if (optUser.isPresent()) {
                                        tblListValue.setLastModifiedBy(optUser.get());
                                    }
                
                                    // Insert or update layout settings
                                    tblListValueRepository.save(tblListValue);
                                }
                            }
                        } else { // Create new list
                            TblList tblList = new TblList();
                            tblList.setType(EListType.CUSTOM);
                            tblList.setCode(listCode);
                            tblList.setName(listCode);
                            tblList.setLanguage(language);
                            if (optUser.isPresent()) {
                                tblList.setCreatedBy(optUser.get());
                                tblList.setLastModifiedBy(optUser.get());
                            }
                            tblListRepository.save(tblList);

                            // Create list items
                            for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                TblListValue tblListValue = new TblListValue();
                                tblListValue.setListCode(listCode);
                                tblListValue.setValue(item.getValue());
                                tblListValue.setLanguage(language);
                                tblListValue.setLabel(item.getLabel());
                                tblListValue.setPosition(item.getPosition());
                                if (optUser.isPresent()) {
                                    tblListValue.setCreatedBy(optUser.get());
                                    tblListValue.setLastModifiedBy(optUser.get());
                                }
            
                                // Insert or update layout settings
                                tblListValueRepository.save(tblListValue);
                            }
                        }
                    } else {
                        fieldSettings.setListCustom(false);
                        fieldSettings.setListCode(fieldSettingsRequest.getListCode());
                    }
                }

                // Insert or update field settings
                fieldSettings = fieldSettingsRepository.save(fieldSettings);
            }
        }

        return new Response<LayoutSettingsView>(StatusCode.OK, "Update layout settings success");
    }

    @Override
    @Transactional
    public Response<List<ListValueRelatedDataItem>> updateLayoutSettings(UpdateLayoutSettingsRequest request, Long accountId, String language) {
        LayoutSettings layoutSettings;

        System.out.println("++++++ Update module layout ++++++");

        /**************** Check related data of deleted and updated fields ***************/
        System.out.println("#1 - Check related data of deleted and updated fields");
        if (request.getFieldSettings() != null) {
            List<ListValueRelatedDataItem> relatedDataByListValues = new ArrayList<>();
            for (UpdateFieldSettingsRequest fieldSettingsRequest : request.getFieldSettings()) {
                Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName(), language);
                if (!optFieldSettings.isPresent()) {
                    continue;
                }

                FieldSettings fieldSettings = optFieldSettings.get();
                // Field is LIST type - check and update list.
                if (fieldSettings.getFieldType() == EFieldType.LIST) {
                    String listCode = fieldSettings.getListCode();
        
                    if (fieldSettings.getListCustom()) {
                        Boolean existByListCode = tblListRepository.existsByTypeAndCodeAndLanguage(EListType.CUSTOM, listCode, language);
                        if (existByListCode) {
                            // #### Check related data before deleting and updating list items
                            // Mapping module code to "table_name"
                            String tableName = CodeGeneratorUtils.mappingModuleCodeToTableName(fieldSettingsRequest.getModuleCode());

                            if (fieldSettingsRequest.getListDeletedItems() != null) { // Check data related to deleted items
                                for (TblListValueView item : fieldSettingsRequest.getListDeletedItems()) {
                                    if (item.getId() == null) {
                                        continue;
                                    }
                                    Optional<TblListValue> optTblListValue = tblListValueRepository.findById(item.getId());
                                    String columnName = fieldSettingsRequest.getName();
                                    String columnValue = optTblListValue.get().getValue();
                                    JSONObject jsonFilter = new JSONObject();
                                    jsonFilter.put("table_name", tableName);
                                    jsonFilter.put("column_name", columnName);
                                    jsonFilter.put("column_value", columnValue);
                                    List<Long> countData = fieldSettingsRepository.countDataRelatedListValue(jsonFilter.toString());
                                    Long totalRelatedData = countData.size() > 0 ? (Long) countData.get(0) : 0L;
                                    if (totalRelatedData > 0) {
                                        ListValueRelatedDataItem listValueRelatedDataItem = new ListValueRelatedDataItem();
                                        listValueRelatedDataItem.setFieldName(columnName);
                                        listValueRelatedDataItem.setListValue(columnValue);
                                        listValueRelatedDataItem.setRelatedDataCount(totalRelatedData);
                                        relatedDataByListValues.add(listValueRelatedDataItem);
                                    }
                                }
                            }

                            if (fieldSettingsRequest.getListAddedItems() != null) {
                                for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                    if (item.getId() != null) { // Check data related to update value items
                                        Optional<TblListValue> optTblListValue = tblListValueRepository.findById(item.getId());
                                        if (optTblListValue.isPresent() && !optTblListValue.get().getValue().equalsIgnoreCase(item.getValue())) {
                                            System.out.println("-----> Old value = " + optTblListValue.get().getValue() + " - New value = " + item.getValue());
                                            String columnName = fieldSettingsRequest.getName();
                                            String columnValue = optTblListValue.get().getValue();
                                            JSONObject jsonFilter = new JSONObject();
                                            jsonFilter.put("table_name", tableName);
                                            jsonFilter.put("column_name", columnName);
                                            jsonFilter.put("column_value", columnValue);
                                            List<Long> countData = fieldSettingsRepository.countDataRelatedListValue(jsonFilter.toString());
                                            Long totalRelatedData = countData.size() > 0 ? (Long) countData.get(0) : 0L;
                                            if (totalRelatedData > 0) {
                                                ListValueRelatedDataItem listValueRelatedDataItem = new ListValueRelatedDataItem();
                                                listValueRelatedDataItem.setFieldName(columnName);
                                                listValueRelatedDataItem.setListValue(columnValue);
                                                listValueRelatedDataItem.setRelatedDataCount(totalRelatedData);
                                                relatedDataByListValues.add(listValueRelatedDataItem);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if (relatedDataByListValues.size() > 0) { // Have some data related to UPDATE or DELETE list value - Cancel update
                return new Response<List<ListValueRelatedDataItem>>(StatusCode.DATA_EXISTED, "Dữ liệu liên quan đang tồn tại!!!", relatedDataByListValues);
            }
        }

        System.out.println("---- Finished check related data ----");
        
        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        System.out.println("++++++ Update layout settings +++++++");
        // #2 - Update LAYOUT settings
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode(request.getModuleCode());
        if (!optLayoutSettings.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Layout settings is not existed!!");
        }

        layoutSettings = optLayoutSettings.get();
        if (optUser.isPresent()) {
            layoutSettings.setLastModifiedBy(optUser.get());
        }
        layoutSettingsMapper.update(request, layoutSettings);

        
        layoutSettings = layoutSettingsRepository.save(layoutSettings);
        
        System.out.println("++++++ Update group settings +++++++");
        // #3 - Update GROUP settings
        if (request.getGroupSettings() != null) {
            for (UpdateGroupSettingsRequest groupSettingsRequest : request.getGroupSettings()) {
                GroupSettings layoutGroupSettings;
                Optional<GroupSettings> optLayoutGroupSettings = groupSettingsRepository.findByModuleCodeAndCodeAndLanguage(groupSettingsRequest.getModuleCode(), groupSettingsRequest.getCode(), language);
                if (!optLayoutGroupSettings.isPresent()) {
                    continue;
                }

                layoutGroupSettings = optLayoutGroupSettings.get();
                groupSettingsMapper.update(groupSettingsRequest, layoutGroupSettings);
                if (optUser.isPresent()) {
                    layoutGroupSettings.setLastModifiedBy(optUser.get());
                }
                
                layoutGroupSettings = groupSettingsRepository.save(layoutGroupSettings);
            }
        }

        System.out.println("++++++ Update field settings +++++++");
        // #4 -Update Field Settings
        if (request.getFieldSettings() != null) {
            System.out.println(request.getFieldSettings().size());
            for (UpdateFieldSettingsRequest fieldSettingsRequest : request.getFieldSettings()) {
                System.out.println(" -- " + fieldSettingsRequest.getModuleCode() + " - " + fieldSettingsRequest.getName() + " - " + language);
                Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName(), language);
                System.out.println("-- " + optFieldSettings.isPresent());
                if (!optFieldSettings.isPresent()) {
                    continue;
                }

                FieldSettings fieldSettings = optFieldSettings.get();
                fieldSettingsMapper.update(fieldSettingsRequest, fieldSettings);
                if (optUser.isPresent()) {
                    fieldSettings.setLastModifiedBy(optUser.get());
                }
                System.out.println("-- " + fieldSettings.getFieldType());
                // Field is LIST type - check and update list.
                if (fieldSettings.getFieldType() == EFieldType.LIST) {
                    System.out.println("### UPDATE LAYOUT SETTINGS - UPDATE LIST");
                    String listCode = fieldSettings.getListCode();
        
                    if (fieldSettings.getListCustom()) {
                        System.err.println(" --> List is custom.");
                        fieldSettings.setListCustom(true);
                        fieldSettings.setListCode(listCode);

                        // Update list values to list
                        Boolean existByListCode = tblListRepository.existsByTypeAndCodeAndLanguage(EListType.CUSTOM, listCode, language);
                        if (existByListCode) { // List is existed, just add or update value items
                            System.out.println(" --> List is existed.");

                            // Delete list items
                            if (fieldSettingsRequest.getListDeletedItems() != null) {
                                System.out.println(" --> Delete list items.");
                                for (TblListValueView item : fieldSettingsRequest.getListDeletedItems()) {
                                    tblListValueRepository.deleteById(item.getId());
                                }
                            }

                            // Add or update list items
                            if (fieldSettingsRequest.getListAddedItems() != null) {
                                System.out.println(" --> Add or update list items.");
                                for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                    TblListValue tblListValue;
                                    if (item.getId() != null) { // Update list item
                                        Optional<TblListValue> optTblListValue = tblListValueRepository.findById(item.getId());
                                        tblListValue = optTblListValue.get();
                                    } else { // Add list item
                                        tblListValue = new TblListValue();
                                        tblListValue.setListCode(listCode);
                                        tblListValue.setLanguage(language);
                                        if (optUser.isPresent()) {
                                            tblListValue.setCreatedBy(optUser.get());
                                        }
                                    }
                                    
                                    tblListValue.setValue(item.getValue());
                                    tblListValue.setLabel(item.getLabel());
                                    tblListValue.setPosition(item.getPosition());
                                    if (optUser.isPresent()) {
                                        tblListValue.setLastModifiedBy(optUser.get());
                                    }
                
                                    // Insert or update layout settings
                                    tblListValueRepository.save(tblListValue);
                                }
                            }
                        } else { // Create new list
                            System.out.println(" --> Create new list.");
                            TblList tblList = new TblList();
                            tblList.setType(EListType.CUSTOM);
                            tblList.setCode(listCode);
                            tblList.setName(listCode);
                            tblList.setLanguage(language);
                            if (optUser.isPresent()) {
                                tblList.setCreatedBy(optUser.get());
                                tblList.setLastModifiedBy(optUser.get());
                            }
                            tblListRepository.save(tblList);

                            // Create list items
                            for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                TblListValue tblListValue = new TblListValue();
                                tblListValue.setListCode(listCode);
                                tblListValue.setValue(item.getValue());
                                tblListValue.setLanguage(language);
                                tblListValue.setLabel(item.getLabel());
                                tblListValue.setPosition(item.getPosition());
                                if (optUser.isPresent()) {
                                    tblListValue.setCreatedBy(optUser.get());
                                    tblListValue.setLastModifiedBy(optUser.get());
                                }
            
                                // Insert or update layout settings
                                tblListValueRepository.save(tblListValue);
                            }
                        }
                    } else {
                        System.err.println(" --> List is not custom.");
                        fieldSettings.setListCustom(false);
                        fieldSettings.setListCode(fieldSettingsRequest.getListCode());
                    }
                }

                // Insert or update field settings
                fieldSettings = fieldSettingsRepository.save(fieldSettings);
            }
        }

        return new Response<>(StatusCode.OK, "Update layout settings success");
    }

    @Override
    public Response<LayoutGroupSettingsView> createGroupSettings(CreateGroupSettingsRequest request, Long accountId, String language) {
        GroupSettings groupSettings;

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<GroupSettings> optLayoutGroupSettings = groupSettingsRepository.findByModuleCodeAndCodeAndLanguage(request.getModuleCode(), request.getCode(), language);
        if (optLayoutGroupSettings.isPresent()) {
            return new Response<LayoutGroupSettingsView>(StatusCode.BAD_REQUEST, "Group settings is existed");
        }

        groupSettings = new GroupSettings();
        groupSettings.setLanguage(language);
        if (optUser.isPresent()) {
            groupSettings.setCreatedBy(optUser.get());
            groupSettings.setLastModifiedBy(optUser.get());
        }

        groupSettingsMapper.create(request, groupSettings);

        // Insert or update layout settings
        groupSettings = groupSettingsRepository.save(groupSettings);
        
        return new Response<LayoutGroupSettingsView>(StatusCode.OK, "Update layout group settings success");
    }

    @Override
    public Response<LayoutGroupSettingsView> updateGroupSettings(UpdateGroupSettingsRequest request, Long accountId, String language) {
        GroupSettings groupSettings;

        // Find creating or updating User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        Optional<GroupSettings> optLayoutGroupSettings = groupSettingsRepository.findByModuleCodeAndCodeAndLanguage(request.getModuleCode(), request.getCode(), language);
        if (!optLayoutGroupSettings.isPresent()) {
            return new Response<LayoutGroupSettingsView>(StatusCode.BAD_REQUEST, "Group settings is not existed");
        }

        groupSettings = optLayoutGroupSettings.get();
        if (optUser.isPresent()) {
            groupSettings.setLastModifiedBy(optUser.get());
        }

        groupSettingsMapper.update(request, groupSettings);

        // Insert or update layout settings
        groupSettings = groupSettingsRepository.save(groupSettings);
        
        return new Response<LayoutGroupSettingsView>(StatusCode.OK, "Update layout group settings success");
    }

    @Override
    public Response<List<FieldSettingsView>> createFieldSettings(List<CreateFieldSettingsRequest> fieldSettingsRequests, Long accountId, String language) {

        // Find created User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        for (CreateFieldSettingsRequest fieldSettingsRequest : fieldSettingsRequests) {
            FieldSettings fieldSettings;
            Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName(), language);
            if (optFieldSettings.isPresent()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Field is existed!!!");
            }

            fieldSettings = new FieldSettings();
            fieldSettingsMapper.create(fieldSettingsRequest, fieldSettings);
            fieldSettings.setLanguage(language);
            if (optUser.isPresent()) {
                fieldSettings.setCreatedBy(optUser.get());
                fieldSettings.setLastModifiedBy(optUser.get());
            }
            
            // Field is LIST type - check and update list.
            if (fieldSettingsRequest.getFieldType() == EFieldType.LIST) {
                String listCode = CodeGeneratorUtils.generateListCode(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName());
    
                if (fieldSettingsRequest.getListCustom()) {
                    fieldSettings.setListCustom(true);
                    fieldSettings.setListCode(listCode);

                    // Update list values to list
                    Boolean existByListCode = tblListRepository.existsByTypeAndCodeAndLanguage(EListType.CUSTOM, listCode, language);
                    if (existByListCode) { // List is existed, just add or update value items
                        // Delete all old list values by list code
                        tblListValueRepository.deleteByListCode(listCode);
                    } else { // Create new list
                        TblList tblList = new TblList();
                        tblList.setType(EListType.CUSTOM);
                        tblList.setCode(listCode);
                        tblList.setName(listCode);
                        tblList.setLanguage(language);
                        if (optUser.isPresent()) {
                            tblList.setCreatedBy(optUser.get());
                            tblList.setLastModifiedBy(optUser.get());
                        }
                        tblListRepository.save(tblList);
                    }

                    // Create new list items
                    if (fieldSettingsRequest.getListAddedItems() != null) {
                        for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                            TblListValue tblListValue = new TblListValue();
                            tblListValue.setListCode(listCode);
                            tblListValue.setValue(item.getValue());
                            tblListValue.setLanguage(language);
                            tblListValue.setLabel(item.getLabel());
                            tblListValue.setPosition(item.getPosition());
                            if (optUser.isPresent()) {
                                tblListValue.setCreatedBy(optUser.get());
                                tblListValue.setLastModifiedBy(optUser.get());
                            }
        
                            tblListValueRepository.save(tblListValue);
                        }
                    }
                } else {
                    fieldSettings.setListCustom(false);
                    fieldSettings.setListCode(fieldSettingsRequest.getListCode());
                }
            }

            // Insert or update field settings
            fieldSettings = fieldSettingsRepository.save(fieldSettings);
        }
        
        return new Response<List<FieldSettingsView>>(StatusCode.OK, "Create field settings success");
    }

    @Override
    public Response<List<FieldSettingsView>> updateFieldSettings(List<UpdateFieldSettingsRequest> fieldSettingsRequests, Long accountId, String language) {

        // Find created User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        for (UpdateFieldSettingsRequest fieldSettingsRequest : fieldSettingsRequests) {
            FieldSettings fieldSettings;
            Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName(), language);
            if (!optFieldSettings.isPresent()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Field is not existed!!!");
            }

            fieldSettings = optFieldSettings.get();
            fieldSettingsMapper.update(fieldSettingsRequest, fieldSettings);
            if (optUser.isPresent()) {
                fieldSettings.setLastModifiedBy(optUser.get());
            }
            
            // Field is LIST type - check and update list.
            if (fieldSettingsRequest.getFieldType() == EFieldType.LIST) {
                String listCode = CodeGeneratorUtils.generateListCode(fieldSettingsRequest.getModuleCode(), fieldSettingsRequest.getName());
    
                if (fieldSettingsRequest.getListCustom()) {
                    fieldSettings.setListCustom(true);
                    fieldSettings.setListCode(listCode);

                    // Update list values to list
                    Boolean existByListCode = tblListRepository.existsByTypeAndCodeAndLanguage(EListType.CUSTOM, listCode, language);
                    if (existByListCode) { // List is existed, just add or update value items
                        // Delete list items
                        if (fieldSettingsRequest.getListDeletedItems() != null) {
                            for (TblListValueView item : fieldSettingsRequest.getListDeletedItems()) {
                                tblListValueRepository.deleteById(item.getId());
                            }
                        }

                        // Add or update list items
                        if (fieldSettingsRequest.getListAddedItems() != null) {
                            for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                                TblListValue tblListValue;
                                Optional<TblListValue> optTblListValue = tblListValueRepository.findByListCodeAndValueAndLanguage(listCode, item.getValue(), language);
                                if (optTblListValue.isPresent()) { // Item is existed, just update label
                                    tblListValue = optTblListValue.get();
                                } else {
                                    tblListValue = new TblListValue();
                                    tblListValue.setListCode(listCode);
                                    tblListValue.setValue(item.getValue());
                                    tblListValue.setLanguage(language);
                                    if (optUser.isPresent()) {
                                        tblListValue.setCreatedBy(optUser.get());
                                    }
                                }
                                tblListValue.setLabel(item.getLabel());
                                tblListValue.setPosition(item.getPosition());
                                if (optUser.isPresent()) {
                                    tblListValue.setLastModifiedBy(optUser.get());
                                }
            
                                // Insert or update layout settings
                                tblListValueRepository.save(tblListValue);
                            }
                        }
                    } else { // Create new list
                        TblList tblList = new TblList();
                        tblList.setType(EListType.CUSTOM);
                        tblList.setCode(listCode);
                        tblList.setName(listCode);
                        tblList.setLanguage(language);
                        if (optUser.isPresent()) {
                            tblList.setCreatedBy(optUser.get());
                            tblList.setLastModifiedBy(optUser.get());
                        }
                        tblListRepository.save(tblList);

                        // Create list items
                        for (TblListValueView item : fieldSettingsRequest.getListAddedItems()) {
                            TblListValue tblListValue = new TblListValue();
                            tblListValue.setListCode(listCode);
                            tblListValue.setValue(item.getValue());
                            tblListValue.setLanguage(language);
                            tblListValue.setLabel(item.getLabel());
                            tblListValue.setPosition(item.getPosition());
                            if (optUser.isPresent()) {
                                tblListValue.setCreatedBy(optUser.get());
                                tblListValue.setLastModifiedBy(optUser.get());
                            }
        
                            // Insert or update layout settings
                            tblListValueRepository.save(tblListValue);
                        }
                    }
                } else {
                    fieldSettings.setListCustom(false);
                    fieldSettings.setListCode(fieldSettingsRequest.getListCode());
                }
            }

            // Insert or update field settings
            fieldSettings = fieldSettingsRepository.save(fieldSettings);

        }
        
        return new Response<List<FieldSettingsView>>(StatusCode.OK, "Update field settings success");
    }

    @Override
    public Response<List<TblListView>> getListByType(EListType type, String language) {
        List<TblList> listByType = tblListRepository.findByTypeAndLanguage(type, language);
        return new Response<List<TblListView>>(StatusCode.OK, "List by type", tblListMapper.toTblListViews(listByType));
    }

    @Override
    public Response<List<TblListValueView>> getListOfValues(String listCode, String language) {
        List<TblListValue> listOfValues = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(listCode, language);
        return new Response<List<TblListValueView>>(StatusCode.OK, "List of values by code!!!", tblListValueMapper.toTblListValueViews(listOfValues));
    }

    @Override
    @Transactional
    public Response<List<TblListValueView>> createOrUpdateListOfValuesForField(UpdateFieldListRequest request, Long accountId, String language) {
       
        String listCode = CodeGeneratorUtils.generateListCode(request.getModuleCode(), request.getFieldName());

        // Update list code to field
        Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(request.getModuleCode(), request.getFieldName(), language);
        if (!optFieldSettings.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Field is not existed");
        }

        if (request.getCustom()) {
            optFieldSettings.get().setListCustom(true);
            optFieldSettings.get().setListCode(listCode);
            fieldSettingsRepository.save(optFieldSettings.get());
        } else {
            optFieldSettings.get().setListCustom(false);
            optFieldSettings.get().setListCode(request.getListCode());
            fieldSettingsRepository.save(optFieldSettings.get());
            return new Response<List<TblListValueView>>(StatusCode.OK, "Update list successfully");
        }

        // Update list values to list
        Boolean existByListCode = tblListValueRepository.existsByListCodeAndLanguage(listCode, language);
        if (existByListCode) { // List is existed, just add or update value items
            // Find created User
            Optional<User> optUser = userRepository.findByAccountId(accountId);

            for (TblListValueView item : request.getValues()) {
                TblListValue tblListValue;
                Optional<TblListValue> optTblListValue = tblListValueRepository.findByListCodeAndValueAndLanguage(listCode, item.getValue(), language);
                if (optTblListValue.isPresent()) { // Item is existed, just update label and position
                    tblListValue = optTblListValue.get();
                } else {
                    tblListValue = new TblListValue();
                    tblListValue.setListCode(listCode);
                    tblListValue.setValue(item.getValue());
                    tblListValue.setLanguage(language);
                    if (optUser.isPresent()) {
                        tblListValue.setCreatedBy(optUser.get());
                    }
                }
                tblListValue.setLabel(item.getLabel());
                tblListValue.setPosition(item.getPosition());
                if (optUser.isPresent()) {
                    tblListValue.setLastModifiedBy(optUser.get());
                }

                // Insert or update layout settings
                tblListValueRepository.save(tblListValue);
            }
        }

        return new Response<List<TblListValueView>>(StatusCode.OK, "Update list successfully");
    }

    @Override
    public Response<Integer> deleteFieldSettings(List<DeleteFieldSettingsRequest> fields, String language) {
        Integer total = 0;
        for (DeleteFieldSettingsRequest field : fields) {
            // Only delete custom field
            Optional<FieldSettings> optFieldSettings = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(field.getModuleCode(), field.getName(), language);
            if (!optFieldSettings.isPresent()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Field is not existed");
            }
            FieldSettings fieldSettings = optFieldSettings.get();
            if (!fieldSettings.getCustom()) {
                return new Response<>(StatusCode.BAD_REQUEST, "Field is not custom");
            }

            // Delete list of field first
            String listCode = CodeGeneratorUtils.generateListCode(field.getModuleCode(), field.getName());
            tblListValueRepository.deleteByListCode(listCode);
            tblListRepository.deleteByTypeAndCode(EListType.CUSTOM, listCode);
            
            Integer result = fieldSettingsRepository.deleteByModuleCodeAndName(field.getModuleCode(), field.getName());
            total += result;
        }

        return new Response<Integer>(StatusCode.OK, "Delete fields success", total);
    }

    @Override
    public Response<List<LeadStatusSettingsViewFull>> getLeadStatusSettings(String language) {
        // Step 1: Get lead status items
        List<TblListValue> leadStatusItems = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(
            CodeGeneratorUtils.generateListCode("LEAD", "status"), 
            language
        );
        
        // Step 2: mapping lead status settings
        if (leadStatusItems.isEmpty()) {
            return new Response<List<LeadStatusSettingsViewFull>>(StatusCode.OK, "Get lead status settings success!!!", new ArrayList<>());
        }

        List<Object[]> results = tblListValueRepository.findLeadStatusSettings();
        List<LeadStatusSettingsViewFull> leadStatusSettingsViews = new ArrayList<LeadStatusSettingsViewFull>();

        for (TblListValue leadStatusItem : leadStatusItems) {
            LeadStatusSettingsViewFull leadStatusSettingsView = new LeadStatusSettingsViewFull();
            leadStatusSettingsView.setLeadStatusLabel(leadStatusItem.getLabel());
            leadStatusSettingsView.setLeadStatusValue(leadStatusItem.getValue());
            leadStatusSettingsView.setLeadStatusPosition(leadStatusItem.getPosition());

            for (Object[] result : results) {
                if (leadStatusItem.getValue().equalsIgnoreCase((String) result[2])) {
                    leadStatusSettingsView.setId((Long) result[0]);
                    leadStatusSettingsView.setWinRate((Integer) result[4]);
                    leadStatusSettingsView.setForecastType((String) result[5]);
                    leadStatusSettingsView.setForecastCategory((String) result[6]);
                    leadStatusSettingsView.setColor((String) result[7]);
                    leadStatusSettingsView.setKeyFields((String) result[8]);
                    break;
                }
            }
            leadStatusSettingsViews.add(leadStatusSettingsView);
        }
        
        return new Response<List<LeadStatusSettingsViewFull>>(StatusCode.OK, "Get lead status settings success!!!", leadStatusSettingsViews);
    }

    @Override
    @Transactional
    public Response<Boolean> updateLeadStatusSettings(List<UpdateLeadStatusSettingsRequest> updateLeadStatusSettingsRequests, Long accountId) {
        if (updateLeadStatusSettingsRequests != null) {
            for (UpdateLeadStatusSettingsRequest request : updateLeadStatusSettingsRequests) {
                if (request.getId() == null) {// Create new lead status settings
                    LeadStatusSettings newLeadStatusSettings = new LeadStatusSettings();
                    leadStatusSettingsMapper.create(request, newLeadStatusSettings);
                    LeadStatusSettings savedLeadStatusSettings = leadStatusSettingsRepository.save(newLeadStatusSettings);

                    // Create corresponding ModuleStagePlaybook record
                    createOrUpdateModuleStagePlaybook("LEAD", savedLeadStatusSettings.getLeadStatusValue());

                } else { // Update lead status settings
                    Optional<LeadStatusSettings> optLeadStatusSettings = leadStatusSettingsRepository.findById(request.getId());
                    if (optLeadStatusSettings.isPresent()) {
                        LeadStatusSettings leadStatusSettings = optLeadStatusSettings.get();
                        String oldStatusValue = leadStatusSettings.getLeadStatusValue();

                        leadStatusSettingsMapper.update(request, leadStatusSettings);
                        LeadStatusSettings savedLeadStatusSettings = leadStatusSettingsRepository.save(leadStatusSettings);

                        // Update corresponding ModuleStagePlaybook record if status value changed
                        String newStatusValue = savedLeadStatusSettings.getLeadStatusValue();
                        if (!oldStatusValue.equals(newStatusValue)) {
                            updateModuleStagePlaybookValue("LEAD", oldStatusValue, newStatusValue);
                        }

                    } else {
                        System.out.println("Lead status settings for {" + request.getLeadStatusValue() + "} is not existed");
                    }
                }
            }
        }
        return new Response<Boolean>(StatusCode.OK, "Update lead status settings success!!!", true);
    }

    @Override
    public Response<List<OpportunityStageSettingsViewFull>> getOpportunityStageSettings(String language) {
        // Step 1: Get opportunity stage items
        List<TblListValue> opportunityStageItems = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(
            CodeGeneratorUtils.generateListCode("OPPORTUNITY", "stage"), 
            language
        );
        
        // Step 2: mapping opportunity stage settings
        if (opportunityStageItems.isEmpty()) {
            return new Response<List<OpportunityStageSettingsViewFull>>(StatusCode.OK, "Get Opportunity stage settings success!!!", new ArrayList<>());
        }

        List<Object[]> results = tblListValueRepository.findOpportunityStageSettings();
        List<OpportunityStageSettingsViewFull> opportunityStageSettingsViews = new ArrayList<OpportunityStageSettingsViewFull>();

        for (TblListValue opportunityStageItem : opportunityStageItems) {
            OpportunityStageSettingsViewFull opportunityStageSettingsView = new OpportunityStageSettingsViewFull();
            opportunityStageSettingsView.setOpportunityStageLabel(opportunityStageItem.getLabel());
            opportunityStageSettingsView.setOpportunityStageValue(opportunityStageItem.getValue());
            opportunityStageSettingsView.setOpportunityStagePosition(opportunityStageItem.getPosition());

            for (Object[] result : results) {
                if (opportunityStageItem.getValue().equalsIgnoreCase((String) result[2])) {
                    opportunityStageSettingsView.setId((Long) result[0]);
                    opportunityStageSettingsView.setWinRate((Integer) result[4]);
                    opportunityStageSettingsView.setForecastType((String) result[5]);
                    opportunityStageSettingsView.setForecastCategory((String) result[6]);
                    opportunityStageSettingsView.setColor((String) result[7]);
                    opportunityStageSettingsView.setKeyFields((String) result[8]);
                    break;
                }
            }
            opportunityStageSettingsViews.add(opportunityStageSettingsView);
        }
        
        return new Response<List<OpportunityStageSettingsViewFull>>(StatusCode.OK, "Get opportunity stage settings success!!!", opportunityStageSettingsViews);
    }

    @Override
    @Transactional
    public Response<Boolean> updateOpportunityStageSettings(
            List<UpdateOpportunityStageSettingsRequest> updateOpportunityStageSettingsRequests, Long accountId) {
        if (updateOpportunityStageSettingsRequests != null) {
            for (UpdateOpportunityStageSettingsRequest request : updateOpportunityStageSettingsRequests) {
                if (request.getId() == null) {// Create new opportunity stage settings
                    OpportunityStageSettings newOpportunityStageSettings = new OpportunityStageSettings();
                    opportunityStageSettingsMapper.create(request, newOpportunityStageSettings);
                    OpportunityStageSettings savedOpportunityStageSettings = opportunityStageSettingsRepository.save(newOpportunityStageSettings);

                    // Create corresponding ModuleStagePlaybook record
                    createOrUpdateModuleStagePlaybook("OPPORTUNITY", savedOpportunityStageSettings.getOpportunityStageValue());

                } else { // Update opportunity stage settings
                    Optional<OpportunityStageSettings> optOpportunityStageSettings = opportunityStageSettingsRepository.findById(request.getId());
                    if (optOpportunityStageSettings.isPresent()) {
                        OpportunityStageSettings opportunityStageSettings = optOpportunityStageSettings.get();
                        String oldStageValue = opportunityStageSettings.getOpportunityStageValue();

                        opportunityStageSettingsMapper.update(request, opportunityStageSettings);
                        OpportunityStageSettings savedOpportunityStageSettings = opportunityStageSettingsRepository.save(opportunityStageSettings);

                        // Update corresponding ModuleStagePlaybook record if stage value changed
                        String newStageValue = savedOpportunityStageSettings.getOpportunityStageValue();
                        if (!oldStageValue.equals(newStageValue)) {
                            updateModuleStagePlaybookValue("OPPORTUNITY", oldStageValue, newStageValue);
                        }

                    } else {
                        System.out.println("Opportunity stage settings for {" + request.getOpportunityStageValue() + "} is not existed");
                    }
                }
            }
        }
        return new Response<Boolean>(StatusCode.OK, "Update Opportunity stage settings success!!!", true);
    }

    // Helper method to create or update ModuleStagePlaybook record
    private void createOrUpdateModuleStagePlaybook(String moduleCode, String stageValue) {
        try {
            // Check if ModuleStagePlaybook already exists
            Optional<ModuleStagePlaybook> existingPlaybook = moduleStagePlaybookRepository
                .findByModuleCodeAndModuleStageValue(moduleCode, stageValue);

            if (existingPlaybook.isPresent()) {
                // Already exists, no need to create
                return;
            }

            // Find the GroupedModulePlaybookProcess for this module
            Optional<GroupedModulePlaybookProcess> optGroupedProcess = groupedModulePlaybookProcessRepository
                .findByModuleCode(moduleCode);

            if (optGroupedProcess.isPresent()) {
                // Create new ModuleStagePlaybook record
                ModuleStagePlaybook newPlaybook = new ModuleStagePlaybook();
                newPlaybook.setModuleCode(moduleCode);
                newPlaybook.setModuleStageValue(stageValue);
                newPlaybook.setGuideForSuccessContent(""); // Default empty content
                // Note: createdBy and lastModifiedBy will be null for system-created records
                // This is acceptable for records created automatically from settings changes

                moduleStagePlaybookRepository.save(newPlaybook);

                System.out.println("Created ModuleStagePlaybook for " + moduleCode + " stage: " + stageValue);
            } else {
                System.out.println("GroupedModulePlaybookProcess not found for module: " + moduleCode);
            }
        } catch (Exception e) {
            System.out.println("Error creating ModuleStagePlaybook for " + moduleCode + " stage " + stageValue + ": " + e.getMessage());
        }
    }

    // Helper method to update ModuleStagePlaybook when stage value changes
    private void updateModuleStagePlaybookValue(String moduleCode, String oldStageValue, String newStageValue) {
        try {
            // Find existing ModuleStagePlaybook with old stage value
            Optional<ModuleStagePlaybook> existingPlaybook = moduleStagePlaybookRepository
                .findByModuleCodeAndModuleStageValue(moduleCode, oldStageValue);

            if (existingPlaybook.isPresent()) {
                ModuleStagePlaybook playbook = existingPlaybook.get();
                playbook.setModuleStageValue(newStageValue);
                moduleStagePlaybookRepository.save(playbook);

                System.out.println("Updated ModuleStagePlaybook for " + moduleCode + " from '" + oldStageValue + "' to '" + newStageValue + "'");
            } else {
                // If old record doesn't exist, create new one with new value
                createOrUpdateModuleStagePlaybook(moduleCode, newStageValue);
            }
        } catch (Exception e) {
            System.out.println("Error updating ModuleStagePlaybook for " + moduleCode + " from '" + oldStageValue + "' to '" + newStageValue + "': " + e.getMessage());
        }
    }

    @Override
    public Response<List<DataPermissionView>> getDataPermissions(Long accountId, String language) {
        return new Response<List<DataPermissionView>>(StatusCode.OK, "Get data permissions success!!!", dataPermissionMapper.toDataPermissionView(dataPermissionRepository.findAll()));
    }

    @Override
    @Transactional
    public Response<List<DataPermissionView>> createOrUpdateDataPermissions(List<CreateDataPermissionRequest> requests,
            Long accountId, String language) {
        // Find created User
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        
        List<DataPermissionView> newPermissionViews = new ArrayList<>();
        for (CreateDataPermissionRequest request : requests) {
            // Check if data permission exists by module code
            Optional<DataPermission> optDataPermission = dataPermissionRepository.findByModuleCode(request.getModuleCode());
            DataPermission newPermission = new DataPermission();
            if (optDataPermission.isPresent()) {
                newPermission = optDataPermission.get();
                dataPermissionMapper.update(request, newPermission);
                if (optUser.isPresent()) {
                    newPermission.setLastModifiedBy(optUser.get());
                }
            } else {
                dataPermissionMapper.create(request, newPermission);
                if (optUser.isPresent()) {
                    newPermission.setCreatedBy(optUser.get());
                    newPermission.setLastModifiedBy(optUser.get());
                }
            }
            newPermission = dataPermissionRepository.save(newPermission);
            newPermissionViews.add(dataPermissionMapper.toDataPermissionView(newPermission));
        }
        return new Response<List<DataPermissionView>>(StatusCode.OK, "Create data permission success!!!", newPermissionViews);
    }

    @Override
    public Response<DataPermissionView> getDataPermissionByModuleCode(String moduleCode, Long accountId, String language) {
        Optional<DataPermission> optDataPermission = dataPermissionRepository.findByModuleCode(moduleCode);
        if (optDataPermission.isPresent()) {
            return new Response<DataPermissionView>(StatusCode.OK, "Get data permission success!!!", dataPermissionMapper.toDataPermissionView(optDataPermission.get()));
        }
        return new Response<DataPermissionView>(StatusCode.BAD_REQUEST, "Permission not found!!!");
    }

    @Override
    public Response<List<DataPermissionShareRuleView>> getDataPermissionShareRules(String moduleCode, Long accountId,
            String language) {
        List<DataPermissionShareRuleView> dataPermissionShareRuleViews = new ArrayList<>();
        List<DataPermissionShareRule> dataPermissionShareRules = dataPermissionShareRuleRepository.findByModuleCode(moduleCode);
        for (DataPermissionShareRule dataPermissionShareRule : dataPermissionShareRules) {
            List<DataPermissionShareFor> dataPermissionShareFors = dataPermissionShareForRepository.findByRuleId(dataPermissionShareRule.getId());
            DataPermissionShareRuleView dataPermissionShareRuleView = dataPermissionShareRuleMapper.toDataPermissionShareRuleView(dataPermissionShareRule);
            dataPermissionShareRuleView.setShareFor(dataPermissionShareFors);
            dataPermissionShareRuleViews.add(dataPermissionShareRuleView);
        }
        return new Response<List<DataPermissionShareRuleView>>(StatusCode.OK, "Get data permission share rules success!!!", dataPermissionShareRuleViews);
    }

    @Override
    @Transactional
    public Response<DataPermissionShareRuleView> createDataPermissionShareRule(
            CreateDataPermissionShareRuleRequest request, Long accountId, String language) {
        // Find created User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        // Create new data permission share rule
        DataPermissionShareRule newDataPermissionShareRule = new DataPermissionShareRule();
        dataPermissionShareRuleMapper.create(request, newDataPermissionShareRule);
        if (optUser.isPresent()) {
            newDataPermissionShareRule.setCreatedBy(optUser.get());
            newDataPermissionShareRule.setLastModifiedBy(optUser.get());
        }
        newDataPermissionShareRule = dataPermissionShareRuleRepository.save(newDataPermissionShareRule);

        // Create new data permission share for
        List<DataPermissionShareFor> newDataPermissionShareFors = new ArrayList<>();
        for (DataPermissionShareFor shareFor : request.getShareFor()) {
            shareFor.setRuleId(newDataPermissionShareRule.getId());
            newDataPermissionShareFors.add(shareFor);
        }
        newDataPermissionShareFors = dataPermissionShareForRepository.saveAll(newDataPermissionShareFors);

        DataPermissionShareRuleView dataPermissionShareRuleView = dataPermissionShareRuleMapper.toDataPermissionShareRuleView(newDataPermissionShareRule);
        dataPermissionShareRuleView.setShareFor(newDataPermissionShareFors);
        return new Response<DataPermissionShareRuleView>(StatusCode.OK, "Create data permission share rule success!!!", dataPermissionShareRuleView);
    }

    @Override
    @Transactional
    public Response<DataPermissionShareRuleView> updateDataPermissionShareRule(
            UpdateDataPermissionShareRuleRequest request, Long accountId, String language) {
        // Find created User
        Optional<User> optUser = userRepository.findByAccountId(accountId);

        // Update data permission share rule
        Optional<DataPermissionShareRule> optDataPermissionShareRule = dataPermissionShareRuleRepository.findById(request.getId());
        if (optDataPermissionShareRule.isPresent()) {
            DataPermissionShareRule dataPermissionShareRule = optDataPermissionShareRule.get();
            dataPermissionShareRuleMapper.update(request, dataPermissionShareRule);
            if (optUser.isPresent()) {
                dataPermissionShareRule.setLastModifiedBy(optUser.get());
            }
            dataPermissionShareRule = dataPermissionShareRuleRepository.save(dataPermissionShareRule);

            // Update data permission share for
            // Delete old data permission share for
            for (DataPermissionShareFor deleteShareFor : request.getDeleteShareFor()) {
                dataPermissionShareForRepository.deleteById(deleteShareFor.getId());
            }
            // Add new data permission share for
            for (DataPermissionShareFor shareFor : request.getAddShareFor()) {
                shareFor.setRuleId(dataPermissionShareRule.getId());
                dataPermissionShareForRepository.save(shareFor);
            }
            DataPermissionShareRuleView dataPermissionShareRuleView = dataPermissionShareRuleMapper.toDataPermissionShareRuleView(dataPermissionShareRule);
            return new Response<DataPermissionShareRuleView>(StatusCode.OK, "Update data permission share rule success!!!", dataPermissionShareRuleView);
        }

        return new Response<DataPermissionShareRuleView>(StatusCode.BAD_REQUEST, "Data permission share rule not found!!!");
    }

    @Override
    public Response<Boolean> deleteDataPermissionShareRule(List<Long> ruleIds) {
        dataPermissionShareRuleRepository.deleteAllById(ruleIds);
        return new Response<Boolean>(StatusCode.OK, "Delete data permission share rule success!!!", true);
    }

    @Override
    public Response<ImageUploadResponse> uploadImages(List<FilePart> imageFiles, Long accountId, String language) {
        if (imageFiles == null || imageFiles.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "No images provided for upload");
        }

        // Create uploads directory if it doesn't exist
        // Use uploads directory - should be mounted as volume in Docker
        Path uploadsDir = Paths.get("uploads");

        // Log the absolute path for debugging
        log.info("Attempting to use uploads directory: {}", uploadsDir.toAbsolutePath());

        if (!ImageUtils.ensureUploadsDirectoryExists(uploadsDir)) {
            String errorMsg = String.format("Failed to create uploads directory: %s (absolute: %s)",
                uploadsDir.toString(), uploadsDir.toAbsolutePath().toString());
            log.error(errorMsg);
            return new Response<>(StatusCode.INTERNAL_SERVER_ERROR, errorMsg);
        }

        List<ImageUploadResponse.UploadedImageInfo> uploadedImages = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // Process each file synchronously
        for (FilePart filePart : imageFiles) {
            processImageFile(filePart, uploadsDir, uploadedImages, errors);
        }

        // Build response
        ImageUploadResponse response = new ImageUploadResponse();
        response.setUploadedImages(uploadedImages);
        response.setSuccessCount(uploadedImages.size());
        response.setTotalCount(imageFiles.size());
        response.setErrors(errors);

        if (uploadedImages.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "No images were successfully uploaded", response);
        } else if (errors.isEmpty()) {
            return new Response<>(StatusCode.OK, "All images uploaded successfully", response);
        } else {
            return new Response<>(StatusCode.BAD_REQUEST, "Some images uploaded successfully", response);
        }
    }

    private void processImageFile(FilePart filePart, Path uploadsDir,
                                 List<ImageUploadResponse.UploadedImageInfo> uploadedImages,
                                 List<String> errors) {
        try {
            String originalFileName = filePart.filename();

            // Validate filename
            String fileNameError = ImageUtils.validateFileName(originalFileName);
            if (fileNameError != null) {
                errors.add(fileNameError);
                return;
            }

            // Validate file type (only allow image files)
            String contentType = ImageUtils.getContentType(filePart);

            if (!ImageUtils.isValidImageType(contentType, originalFileName)) {
                errors.add("Invalid file type for: " + originalFileName + ". Only image files are allowed.");
                return;
            }

            // Generate unique filename with original name + timestamp
            String savedFileName = ImageUtils.generateUniqueFileName(originalFileName);

            Path filePath = uploadsDir.resolve(savedFileName);

            // Save the file using ImageUtils
            long fileSize = ImageUtils.saveFilePart(filePart, filePath);

            if (fileSize > 0) {
                ImageUploadResponse.UploadedImageInfo imageInfo = new ImageUploadResponse.UploadedImageInfo();
                imageInfo.setOriginalFileName(originalFileName);
                imageInfo.setSavedFileName(savedFileName);
                imageInfo.setFilePath("uploads/" + savedFileName);
                imageInfo.setFileSize(fileSize);
                imageInfo.setContentType(contentType);

                uploadedImages.add(imageInfo);

                log.info("Successfully uploaded image: {} as {} ({})",
                        originalFileName, savedFileName, ImageUtils.formatFileSize(fileSize));
            } else {
                errors.add("Failed to save file: " + originalFileName);
            }
        } catch (Exception e) {
            log.error("Error processing file {}: {}", filePart.filename(), e.getMessage());
            errors.add("Error processing file: " + filePart.filename() + " - " + e.getMessage());
        }
    }

    @Override
    public Response<com.tti.oh_crm_service.entity.FileUploadResponse> uploadFiles(List<FilePart> files, String module, Long accountId, String language) {
        if (files == null || files.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "No files provided for upload");
        }

        // Validate module
        if (module == null || module.trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module is required!");
        }

        // Ensure base uploads directory exists (similar to uploadImages method)
        Path uploadsDir = Paths.get("uploads");
        log.info("Attempting to use uploads directory: {}", uploadsDir.toAbsolutePath());

        if (!ImageUtils.ensureUploadsDirectoryExists(uploadsDir)) {
            String errorMsg = String.format("Failed to create uploads directory: %s (absolute: %s)",
                uploadsDir.toString(), uploadsDir.toAbsolutePath().toString());
            log.error(errorMsg);
            return new Response<>(StatusCode.INTERNAL_SERVER_ERROR, errorMsg);
        }

        List<FileUploadResponse.UploadedFileInfo> uploadedFiles = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        // Process each file
        for (FilePart filePart : files) {
            processFile(filePart, module, uploadedFiles, errors);
        }

        // Build response
        FileUploadResponse response = new FileUploadResponse();
        response.setUploadedFiles(uploadedFiles);
        response.setSuccessCount(uploadedFiles.size());
        response.setTotalCount(files.size());
        response.setErrors(errors);
        response.setModule(module.toUpperCase());

        if (uploadedFiles.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "No files were successfully uploaded", response);
        } else if (errors.isEmpty()) {
            return new Response<>(StatusCode.OK, "All files uploaded successfully", response);
        } else {
            return new Response<>(StatusCode.BAD_REQUEST, "Some files uploaded successfully", response);
        }
    }

    private void processFile(FilePart filePart, String module,
                           List<FileUploadResponse.UploadedFileInfo> uploadedFiles,
                           List<String> errors) {
        try {
            String originalFileName = filePart.filename();

            // Validate filename
            String fileNameError = FileUtils.validateFileName(originalFileName);
            if (fileNameError != null) {
                errors.add(fileNameError);
                return;
            }

            // Get content type and determine file category
            String contentType = FileUtils.getContentType(filePart);
            String fileCategory = FileUtils.getFileCategory(contentType, originalFileName);

            // Create module directory structure
            Path moduleDir;
            try {
                moduleDir = FileUtils.createModuleDirectory(module, fileCategory);
            } catch (RuntimeException e) {
                errors.add("Failed to create directory for file: " + originalFileName + " - " + e.getMessage());
                return;
            }

            // Generate unique filename
            String savedFileName = FileUtils.generateUniqueFileName(originalFileName);
            Path filePath = moduleDir.resolve(savedFileName);

            // Save the file
            long fileSize = FileUtils.saveFilePart(filePart, filePath);

            if (fileSize > 0) {
                // Validate file size
                String sizeError = FileUtils.validateFileSize(fileSize);
                if (sizeError != null) {
                    errors.add(sizeError + " for file: " + originalFileName);
                    // Delete the saved file since it's invalid
                    try {
                        Files.deleteIfExists(filePath);
                    } catch (Exception e) {
                        log.warn("Failed to delete invalid file: {}", filePath);
                    }
                    return;
                }

                // Create file info
                FileUploadResponse.UploadedFileInfo fileInfo =
                    new FileUploadResponse.UploadedFileInfo();
                fileInfo.setOriginalFileName(originalFileName);
                fileInfo.setSavedFileName(savedFileName);
                fileInfo.setFilePath(String.format("uploads/%ss/%s/%s", fileCategory, module.toUpperCase(), savedFileName));
                fileInfo.setPublicUrl(FileUtils.generatePublicUrl(fileCategory, module, savedFileName));
                fileInfo.setFileSize(fileSize);
                fileInfo.setContentType(contentType);
                fileInfo.setFileCategory(fileCategory);
                fileInfo.setModule(module.toUpperCase());

                uploadedFiles.add(fileInfo);

                log.info("Successfully uploaded {} file: {} as {} ({}) to module: {}",
                        fileCategory, originalFileName, savedFileName,
                        FileUtils.formatFileSize(fileSize), module);
            } else {
                errors.add("Failed to save file: " + originalFileName);
            }
        } catch (Exception e) {
            log.error("Error processing file {}: {}", filePart.filename(), e.getMessage());
            errors.add("Error processing file: " + filePart.filename() + " - " + e.getMessage());
        }
    }



}