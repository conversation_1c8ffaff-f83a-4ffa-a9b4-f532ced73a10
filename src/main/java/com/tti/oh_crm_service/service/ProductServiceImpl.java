package com.tti.oh_crm_service.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.json.JSONArray;
import org.json.JSONObject;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.CreateProductRequest;
import com.tti.oh_crm_service.entity.GenericTableResponse;
import com.tti.oh_crm_service.entity.GroupItem;
import com.tti.oh_crm_service.entity.ProductConversionView;
import com.tti.oh_crm_service.entity.ProductKanbanResponse;
import com.tti.oh_crm_service.entity.ProductKanbanView;
import com.tti.oh_crm_service.entity.ProductSplitView;
import com.tti.oh_crm_service.entity.ProductSplitViewResponse;
import com.tti.oh_crm_service.entity.ProductTableResponse;
import com.tti.oh_crm_service.entity.ProductView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateProductRequest;
import com.tti.oh_crm_service.mapper.ProductConversionMapper;
import com.tti.oh_crm_service.mapper.ProductMapper;
import com.tti.oh_crm_service.model.LayoutSettings;
import com.tti.oh_crm_service.model.Product;
import com.tti.oh_crm_service.model.ProductConversion;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.ProductRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.repository.LayoutSettingsRepository;
import com.tti.oh_crm_service.repository.ProductConversionRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;

import io.jsonwebtoken.Claims;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {


    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private ProductMapper productMapper;

    @Autowired
    private ProductConversionRepository productConversionRepository;

    @Autowired
    private ProductConversionMapper productConversionMapper;

    @Autowired
    private LayoutSettingsRepository layoutSettingsRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private CoreQueryService<Product> coreQueryService;

    private String validateField(String field) {
        // Danh sách các trường hợp lệ để ngăn SQL Injection
        List<String> validFields = List.of("category", "brand", "type");
        if (!validFields.contains(field)) {
            throw new IllegalArgumentException("Invalid field: " + field);
        }
        return field;
    }

    @SuppressWarnings("unchecked")
    public List<Object[]> getGroupedData(String field) {
        String validField = validateField(field);
        String sql = "SELECT " + validField + ", COUNT(*) FROM products GROUP BY " + field;
        Query query = entityManager.createNativeQuery(sql);
        return query.getResultList();
    }
    
    @Override
    public Response<ProductView> getProductDetail(Claims claims, Locale locale, Long id) {
        try{
            Optional<Product> optProduct = productRepository.findById(id);
            if (optProduct.isEmpty()) {
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product not found");
            }

            Product product = optProduct.get();
            ProductView productView = productMapper.toProductView(product);

            List<ProductConversion> productConversions = productConversionRepository.findByProductId(productView.getId());

            if(productConversions != null && !productConversions.isEmpty()){
                List<ProductConversionView> productConversionViews = productConversionMapper.toProductConversionView(productConversions);

                productView.setProductConversions(productConversionViews);
            }

            return new Response<ProductView>(StatusCode.OK,  "Get product detail successfully", productView);
        }catch(Exception e){
            return new Response<ProductView>(StatusCode.INTERNAL_SERVER_ERROR,  "Internal server error");
        }
    }

    @Override
    public Response<ProductTableResponse> getProducts(
        String filters, 
        String groupBy, 
        String groupValue, 
        String search, 
        String sortBy, 
        String sortDirection, 
        int page, 
        int limit,
        Long accountId,
        String language
    ) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "code",
                "name",
                "category",
                "description"
        );

        GenericTableResponse<Product> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "products", searchFields, "PRODUCT",
                productRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }
        
        Page<Product> productsPage = tableResponse.getData();
        List<ProductView> productViews = productMapper.toProductView(productsPage.getContent());
        for(ProductView productView : productViews){
            List<ProductConversion> productConversions = productConversionRepository.findByProductId(productView.getId());
            
            if(productConversions != null && !productConversions.isEmpty()){
                List<ProductConversionView> productConversionViews = productConversionMapper.toProductConversionView(productConversions);

                productView.setProductConversions(productConversionViews);
            }
            
        }
        Page<ProductView> productViewPage = new PageImpl<>(productViews, productsPage.getPageable(), productsPage.getTotalElements());

        ProductTableResponse productTableResponse = new ProductTableResponse();
        productTableResponse.setGroupItems(tableResponse.getGroupItems());
        productTableResponse.setProducts(productViewPage);
        return new Response<ProductTableResponse>(StatusCode.OK,  "Get products successfully", productTableResponse);
    }

    @Override
    @Transactional
    public Response<ProductView> createProduct(Claims claims, CreateProductRequest createProductRequest, Locale locale) {

        Long accountId = TokenUtils.getAccountId(claims);
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "User not found");
        }

        User user = optUser.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(user);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Product CREATE");

        Optional<List<Product>> optListProduct = productRepository.findByCode(createProductRequest.getCode());
        if (!optListProduct.get().isEmpty()) {
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product code already exists");
        }

        optListProduct = productRepository.findByName(createProductRequest.getName());
        if (!optListProduct.get().isEmpty()) {
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product name already exists");
        }
        
        Product product = productMapper.toProduct(createProductRequest);
        
        if(product.getName().isBlank() || product.getName() == null){
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product name is required");
        }else if(product.getCategory().isBlank() || product.getCategory() == null){
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product category is required");
        }else if(product.getManufacturer().isBlank() || product.getManufacturer() == null){
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product manufacturer is required");
        }else if(product.getCode().isBlank() || product.getCode() == null){
            return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product code is required");
        }else{
            product.setCreatedBy(user);
            if(createProductRequest.getActive() == null){
                product.setActive(false);
            }else{
                product.setActive(true);
            }

            // Save product - HistoryEntityListener will automatically handle CREATE history
            Product savedProduct = productRepository.save(product);

            List<ProductConversion> productConversion = productConversionMapper.toProductConversion(createProductRequest.getProductConversions());
            if(productConversion != null && !productConversion.isEmpty()){
                for(ProductConversion pc : productConversion){
                    pc.setProductId(savedProduct.getId());
                }
                productConversionRepository.saveAll(Set.copyOf(productConversion));
            }

            ProductView productView = productMapper.toProductView(product);
            return new Response<ProductView>(StatusCode.OK,  "Product created successfully", productView);
        }
    }

    @Override
    @Transactional
    public Response<ProductView> updateProduct(Claims claims, UpdateProductRequest updateProductRequest, Locale locale) {
        try{
            Long accountId = TokenUtils.getAccountId(claims);
            Optional<User> optUser = userRepository.findByAccountId(accountId);
            if (optUser.isEmpty()) {
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "User not found");
            }

            User user = optUser.get();

            Optional<Product> optProduct = productRepository.findById(updateProductRequest.getId());
            if (optProduct.isEmpty()) {
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product not found");
            }

            Product product = optProduct.get();

            // Set user context for EntityListener
            UserContext.setCurrentUser(user);


            // Step 1: Clone the original entity for history tracking before any modifications
            Product originalProduct = EntityCloneUtils.cloneEntityForHistory(product);
            EntityContext.setOriginalEntity(originalProduct);

            // Register a transaction synchronization to clear context after commit
            TransactionUtils.registerContextCleanupAfterTransaction("Product UPDATE");

            // Step 2: Apply updates to the entity
            productMapper.updateProduct(updateProductRequest, product);

            // Step 3: set the updated entity state after modifications
            EntityContext.setUpdatedEntity(product);

            if(product.getName().isBlank()){
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product name is required");
            }else if(product.getCategory().isBlank()){
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product category is required");
            }else if(product.getManufacturer().isBlank()){
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product manufacturer is required");
            }else if(product.getCode().isBlank()){
                return new Response<ProductView>(StatusCode.BAD_REQUEST,  "Product code is required");
            }else{
                product.setLastModifiedBy(user);

                // Save product - HistoryEntityListener will automatically handle UPDATE history with field changes
                productRepository.save(product);

                ProductView productView = productMapper.toProductView(product);

                return new Response<ProductView>(StatusCode.OK,  "Product updated successfully", productView);
            }
        }catch(Exception e){
            return new Response<ProductView>(StatusCode.INTERNAL_SERVER_ERROR,  "Internal server error");
        }
    }

    @Override
    @Transactional
    public Response<List<Long>> deleteProducts(List<Long> productIds) {
        try{

            List<Long> listNotFound = new ArrayList<Long>();

            

            for(Long productId : productIds){

                List<ProductConversion> productConversions = productConversionRepository.findByProductId(productId);

                if(productConversions!= null && !productConversions.isEmpty()){
                    productConversionRepository.deleteAll(productConversions);
                }

                Optional<Product> optProduct = productRepository.findById(productId);
                if (optProduct.isEmpty()) {
                    listNotFound.add(productId);
                }
            }

            if(!listNotFound.isEmpty()){
                return new Response<List<Long>>(StatusCode.BAD_REQUEST,  "Product not found", listNotFound);
            }

            for(Long productId : productIds){
                productRepository.deleteById(productId);
            }
            return new Response<List<Long>>(StatusCode.OK,  "Product deleted successfully", productIds);

        }catch(Exception e){
            return new Response<List<Long>>(StatusCode.INTERNAL_SERVER_ERROR,  "Internal server error");
        }
    }

    @Override
    public Response<ProductKanbanResponse> getProductKanban(String filters, String groupBy, String search, Long accountId, String language) {
        // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("code");
        searchFields.put("name");
        searchFields.put("category");
        searchFields.put("description");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            searchFields.put(groupBy);
        }else{
            searchFields.put("category");
        }

        // Get products in kanban view
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "products");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Kanban view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("code");
        viewFields.put("name");

        if(groupBy != null && !groupBy.trim().equalsIgnoreCase("")){
            viewFields.put(groupBy);
        }else{
            viewFields.put("category");
        }   
        jsonFilter.put("view_fields", viewFields);

        // Get group by field
        List<GroupItem> groupItems = new ArrayList<>();
        Long totalRows = -1L;
        Optional<LayoutSettings> optLayoutSettings = layoutSettingsRepository.findByModuleCode("PRODUCT");
        if (optLayoutSettings.isPresent()) {
            String groupByField = optLayoutSettings.get().getGroupByField();
            if (groupByField != null && !groupByField.trim().equalsIgnoreCase("")) {
                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("field_name", groupBy);
                } else {
                    jsonFilter.put("field_name", groupByField);
                }
                List<Object[]> groupValues = productRepository.getGroupValues(jsonFilter.toString());
                totalRows = 0L;
                for (Object[] grpValue : groupValues) {
                    totalRows += (long) grpValue[1];
                    GroupItem groupItem = new GroupItem((String) grpValue[0], (long) grpValue[1]);
                    groupItems.add(groupItem);
                }

                if (groupBy != null && !groupBy.trim().equalsIgnoreCase("")) {
                    jsonFilter.put("group_field", groupBy);
                } else {
                    jsonFilter.put("group_field", groupByField);
                }
            }
        }

        List<Long> countByFilter = productRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        if (totalRows < 0) {
            groupItems.add(0, new GroupItem("total", totalRowsByGroup));
        } else {
            groupItems.add(0, new GroupItem("total", totalRows));
        }
       
        System.out.println("-----JsonFilter-----" + jsonFilter.toString());
        
        List<ProductKanbanView> products = new ArrayList<>();
        try {
            List<Object[]> productObjects = productRepository.findKanban(jsonFilter.toString());
            for (Object[] productObject : productObjects) {
                ProductKanbanView productKanbanView = new ProductKanbanView();
                productKanbanView.setId((Long) productObject[0]);
                productKanbanView.setCode((String) productObject[1]);
                productKanbanView.setName((String) productObject[2]);
                productKanbanView.setGroupField((String) productObject[3]);
                products.add(productKanbanView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<ProductKanbanResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        ProductKanbanResponse productKanbanResponse = new ProductKanbanResponse();
        productKanbanResponse.setGroupItems(groupItems);
        productKanbanResponse.setProducts(products);
        return new Response<ProductKanbanResponse>(StatusCode.OK, "Get products kanban success!!!", productKanbanResponse);
    }

    @Override
    @Transactional
    public Response<ProductSplitViewResponse> getProductsSplitView(String filters, String search, String sortBy,
            String sortDirection, int page, int limit, Long accountId, String language) {
                // Define search fields
        JSONArray searchFields = new JSONArray();
        searchFields.put("id");
        searchFields.put("code");
        searchFields.put("name");
        searchFields.put("category");

        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "products");
        if (search != null && !search.trim().equalsIgnoreCase("")) {
            jsonFilter.put("search_text", search);
            jsonFilter.put("search_fields", searchFields);
        }
        
        jsonFilter.put("sort_by", sortBy);
        jsonFilter.put("sort_direction", sortDirection);
        jsonFilter.put("page", page);
        jsonFilter.put("limit", limit);

        if (filters != null && !filters.trim().equalsIgnoreCase("")) {
            JSONArray jsonArray = new JSONArray(filters);
            jsonFilter.put("filters", jsonArray);
        }

        // Split view projection
        JSONArray viewFields = new JSONArray();
        viewFields.put("id");
        viewFields.put("code");
        viewFields.put("name");
        viewFields.put("category");
        jsonFilter.put("view_fields", viewFields);
       
        System.out.println(jsonFilter.toString());
        
        List<ProductSplitView> products = new ArrayList<>();
        try {
            List<Object[]> productObjects = productRepository.findSplitViewData(jsonFilter.toString());
            for (Object[] productObject : productObjects) {
                ProductSplitView productSplitView = new ProductSplitView();
                productSplitView.setId((Long) productObject[0]);
                productSplitView.setCode((String) productObject[1]);
                productSplitView.setName((String) productObject[2]);
                productSplitView.setCategory((String) productObject[3]);
                products.add(productSplitView);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Response<ProductSplitViewResponse>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        // Get Total rows
        List<Long> countByFilter = productRepository.countByFilter(jsonFilter.toString());
        Long totalRowsByGroup = countByFilter.size() > 0 ? (Long) countByFilter.get(0) : 0L;

        Page<ProductSplitView> productsPage = new PageImpl<>(products, PageRequest.of(page, limit, Sort.by(sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC, sortBy)), totalRowsByGroup);
        ProductSplitViewResponse productSplitViewResponse = new ProductSplitViewResponse();
        productSplitViewResponse.setProducts(productsPage);
        return new Response<ProductSplitViewResponse>(StatusCode.OK, "Get products split view success!!!", productSplitViewResponse);
    }

    @Override
    public Response<Boolean> updateKanbanProduct(Long productId, String fieldName, String fieldValue, Long accountId,
            String language) {
                JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "products");
        jsonFilter.put("id", productId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);
        
        productRepository.updateKanbanData(jsonFilter.toString());

        return new Response<Boolean>(StatusCode.OK, "Update product kanban success!!!", true);
    }

}
