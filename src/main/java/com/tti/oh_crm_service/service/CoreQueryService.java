package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.GenericKanbanResponse;
import com.tti.oh_crm_service.entity.GenericSplitViewResponse;
import com.tti.oh_crm_service.entity.GenericTableResponse;
import com.tti.oh_crm_service.repository.CoreStoreProcedureRepository;

import java.util.List;

public interface CoreQueryService<T> {
    GenericTableResponse<T> getTable(
        String filters,
        String groupBy,
        String groupValue,
        String search,
        String sortBy,
        String sortDirection,
        int page,
        int limit,
        Long accountId,
        String language,
        String tableName,
        List<String> searchFields,
        String moduleCode,
        CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    );

    GenericKanbanResponse getKanban(
        String filters,
        String groupBy,
        String search,
        Long accountId,
        String language,
        String tableName,
        List<String> searchFields,
        List<String> viewFields,
        String moduleCode,
        CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    );

    GenericSplitViewResponse<Object[]> getSplitView(
            String filters,
            String search,
            String sortBy,
            String sortDirection,
            int page,
            int limit,
            String tableName,
            List<String> searchFields,
            List<String> viewFields,
            CoreStoreProcedureRepository<T> coreStoreProcedureRepository
    );
}
