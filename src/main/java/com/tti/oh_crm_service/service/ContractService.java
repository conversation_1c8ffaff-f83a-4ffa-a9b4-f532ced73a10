package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Contract;

import java.text.ParseException;
import java.util.List;

public interface ContractService {
    public Response<ContractTableResponse> getContractsTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<ContractView> createContract(CreateContractRequest request, Long accountId, String language);
    public Response<ContractView> updateContract(UpdateContractRequest request, Long accountId, String language);
    public Response<Integer> deleteContracts(List<Long> ids);
    public Response<ContractDetails> getContractDetails(Long contractId, Long accountId, String language);
    public Response<ContractKanbanResponse> getContractsKanban(String filters, String groupBy, String search, Long accountId, String language) throws ParseException;
    public Response<ContractSplitViewResponse> getContractsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<Boolean> updateKanbanContract(Long contractId, String fieldName, String fieldValue, Long accountId, String language);
    public Response<GenericTableResponse<OrderAttachedView>> getAttachedOrdersForContract(Long contractId, int page, int limit, Long accountId, String language);
    public List<ContractView> getContractsByAccountId(Long accountId);
    public Contract findById(Long id);
}
