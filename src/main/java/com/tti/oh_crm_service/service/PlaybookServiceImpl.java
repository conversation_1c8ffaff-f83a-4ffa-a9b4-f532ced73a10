package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import com.tti.oh_crm_service.enumeration.EPlaybookStatus;
import com.tti.oh_crm_service.mapper.PlaybookMapper;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.*;
import com.tti.oh_crm_service.utils.CodeGeneratorUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.PageImpl;

@Slf4j
@Service
public class PlaybookServiceImpl implements PlaybookService {

    @Autowired
    private PlaybookToolRepository playbookToolRepository;
    @Autowired
    private PlaybookToolSectionRepository playbookToolSectionRepository;
    @Autowired
    private PlaybookToolHistoryRepository playbookToolHistoryRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private PlaybookMapper playbookMapper;
    @Autowired
    private ModuleStagePlaybookRepository moduleStagePlaybookRepository;
    @Autowired
    private GroupedModulePlaybookProcessRepository groupedModulePlaybookProcessRepository;
    @Autowired
    private TblListValueRepository tblListValueRepository;

    // PlaybookTool CRUD operations

    @Override
    @Transactional
    public Response<PlaybookToolListView> createPlaybookTool(CreatePlaybookToolRequest request, Long accountId) {
        // Validate user performing the creation
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required fields
        if (request.getName() == null || request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool name is required!");
        }

        PlaybookTool playbookTool = playbookMapper.toPlaybookTool(request);
        playbookTool.setViewCount(0);
        playbookTool.setCreatedBy(optUser.get());
        playbookTool.setLastModifiedBy(optUser.get());

        PlaybookTool savedPlaybookTool = playbookToolRepository.save(playbookTool);
        PlaybookToolListView response = playbookMapper.toPlaybookToolListView(savedPlaybookTool);

        return new Response<>(StatusCode.OK, "Playbook tool created successfully!", response);
    }

    @Override
    @Transactional
    public Response<PlaybookToolListView> updatePlaybookTool(UpdatePlaybookToolRequest request, Long accountId) {
        // Validate user performing the update
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Find the playbook tool by ID
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(request.getId());
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        PlaybookTool playbookTool = optPlaybookTool.get();

        // Validate name if provided
        if (request.getName() != null && request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool name cannot be empty!");
        }

        // Update fields
        if (request.getName() != null) {
            playbookTool.setName(request.getName());
        }
        if (request.getDescription() != null) {
            playbookTool.setDescription(request.getDescription());
        }

        // Set last modified by
        playbookTool.setLastModifiedBy(optUser.get());

        PlaybookTool savedPlaybookTool = playbookToolRepository.save(playbookTool);
        PlaybookToolListView response = playbookMapper.toPlaybookToolListView(savedPlaybookTool);

        return new Response<>(StatusCode.OK, "Playbook tool updated successfully!", response);
    }

    @Override
    public Response<Page<PlaybookToolListView>> getPlaybookToolsList(int page, int limit, String search, String sortBy, String sortDirection, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        Sort.Direction direction = sortDirection.equalsIgnoreCase("DESC") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, limit, Sort.by(direction, sortBy != null ? sortBy : "id"));

        Page<PlaybookTool> playbookToolsPage;
        if (search != null && !search.trim().isEmpty()) {
            playbookToolsPage = playbookToolRepository.findByNameContainingIgnoreCase(search.trim(), pageable);
        } else {
            playbookToolsPage = playbookToolRepository.findAll(pageable);
        }

        Page<PlaybookToolListView> response = playbookToolsPage.map(playbookMapper::toPlaybookToolListView);

        return new Response<>(StatusCode.OK, "Playbook tools retrieved successfully!", response);
    }

    @Override
    public Response<PlaybookToolDetailView> getPlaybookToolDetail(Long id, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (id == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Find the playbook tool by ID
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(id);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }
        PlaybookTool playbookTool = optPlaybookTool.get();

        // Get available sections (not in trash) separately for mapping
        List<PlaybookToolSection> availableSections = playbookToolSectionRepository.findByPlaybookToolIdAndIsInTrashFalse(id);

        // Create response manually to avoid collection modification issues
        PlaybookToolDetailView response = new PlaybookToolDetailView();
        response.setId(playbookTool.getId());
        response.setName(playbookTool.getName());
        response.setDescription(playbookTool.getDescription());
        response.setViewCount(playbookTool.getViewCount());
        response.setSections(playbookMapper.toPlaybookToolSectionView(availableSections));

        return new Response<>(StatusCode.OK, "Playbook tool detail retrieved successfully!", response);
    }

    @Override
    @Transactional
    public Response<String> deletePlaybookTool(Long id, Long accountId) {
        // Validate user performing the deletion
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (id == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Find the playbook tool by ID
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(id);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        playbookToolRepository.deleteById(id);

        return new Response<>(StatusCode.OK, "Playbook tool deleted successfully!", "Deleted");
    }

    // PlaybookToolSection CRUD operations

    @Override
    @Transactional
    public Response<PlaybookToolSectionView> createPlaybookToolSection(CreatePlaybookToolSectionRequest request, Long playbookToolId, Long accountId) {
        // Validate user performing the creation
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required fields
        if (playbookToolId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        if (request.getName() == null || request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Section name is required!");
        }

        // Verify playbook tool exists
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(playbookToolId);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        PlaybookToolSection section = playbookMapper.toPlaybookToolSection(request);
        section.setIsInTrash(false);
        section.setPlaybookTool(optPlaybookTool.get());
        section.setCreatedBy(optUser.get());
        section.setLastModifiedBy(optUser.get());

        PlaybookToolSection savedSection = playbookToolSectionRepository.save(section);

        // Create history record
        createHistoryRecord(savedSection, EHistoryActionType.CREATE, null, null, null, optUser.get());

        PlaybookToolSectionView response = playbookMapper.toPlaybookToolSectionView(savedSection);

        return new Response<>(StatusCode.OK, "Playbook tool section created successfully!", response);
    }

    @Override
    @Transactional
    public Response<PlaybookToolSectionView> updatePlaybookToolSection(UpdatePlaybookToolSectionRequest request, Long playbookToolId, Long accountId) {
        // Validate user performing the update
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Section ID is required!");
        }

        if (playbookToolId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Verify playbook tool exists
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(playbookToolId);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        // Find the section by ID
        Optional<PlaybookToolSection> optSection = playbookToolSectionRepository.findById(request.getId());
        if (!optSection.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool section not found!");
        }

        PlaybookToolSection existingSection = optSection.get();

        // Validate name if provided
        if (request.getName() != null && request.getName().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Section name cannot be empty!");
        }

        // Track changes for history
        boolean nameChanged = false;
        boolean contentChanged = false;
        String originalName = existingSection.getName();
        String originalContent = existingSection.getContent();

        if (request.getName() != null && !request.getName().equals(originalName)) {
            existingSection.setName(request.getName());
            nameChanged = true;
        }

        if (request.getContent() != null && !request.getContent().equals(originalContent)) {
            existingSection.setContent(request.getContent());
            contentChanged = true;
        }

        // Set last modified by if any changes were made
        if (nameChanged || contentChanged) {
            existingSection.setLastModifiedBy(optUser.get());
        }

        PlaybookToolSection savedSection = playbookToolSectionRepository.save(existingSection);

        // Create history records for changed fields
        if (nameChanged) {
            createHistoryRecord(savedSection, EHistoryActionType.UPDATE, "name", originalName, request.getName(), optUser.get());
        }
        if (contentChanged) {
            createHistoryRecord(savedSection, EHistoryActionType.UPDATE, "content", originalContent, request.getContent(), optUser.get());
        }

        PlaybookToolSectionView response = playbookMapper.toPlaybookToolSectionView(savedSection);

        return new Response<>(StatusCode.OK, "Playbook tool section updated successfully!", response);
    }
    

    // Bulk PlaybookToolSection operations

    @Override
    @Transactional
    public Response<PlaybookToolWithSectionsResponse> updatePlaybookToolWithSections(UpdatePlaybookToolWithSectionsRequest request, Long playBookbookToolId, Long accountId) {
        // Validate user performing the operation
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (playBookbookToolId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Find the playbook tool
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(playBookbookToolId);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        PlaybookTool playbookTool = optPlaybookTool.get();

        // Update playbook tool basic info if provided
        boolean toolUpdated = false;
        if (request.getName() != null && !request.getName().trim().isEmpty()) {
            playbookTool.setName(request.getName());
            toolUpdated = true;
        }
        if (request.getDescription() != null) {
            playbookTool.setDescription(request.getDescription());
            toolUpdated = true;
        }

        if (toolUpdated) {
            playbookTool.setLastModifiedBy(optUser.get());
            playbookToolRepository.save(playbookTool);
        }

        // Initialize operation summary
        PlaybookToolWithSectionsResponse.BulkOperationSummary summary = new PlaybookToolWithSectionsResponse.BulkOperationSummary();
        summary.setSectionsCreated(0);
        summary.setSectionsUpdated(0);
        summary.setSectionsSkipped(0);
        summary.setErrors(new ArrayList<>());

        // Process sections if provided
        if (request.getSections() != null && !request.getSections().isEmpty()) {
            processSectionsBulk(playbookTool, request.getSections(), request.getDeletedSectionIds(), optUser.get(), summary);
        }

        // Get updated playbook tool with sections
        List<PlaybookToolSection> updatedSections = playbookToolSectionRepository.findByPlaybookToolIdAndIsInTrashFalse(playbookTool.getId());
        List<PlaybookToolSectionView> sectionViews = playbookMapper.toPlaybookToolSectionView(updatedSections);

        // Build response
        PlaybookToolWithSectionsResponse response = new PlaybookToolWithSectionsResponse();
        response.setId(playbookTool.getId());
        response.setName(playbookTool.getName());
        response.setDescription(playbookTool.getDescription());
        response.setViewCount(playbookTool.getViewCount());
        response.setSections(sectionViews);
        response.setOperationSummary(summary);

        String message = String.format("Playbook tool updated successfully! Created: %d, Updated: %d, Skipped: %d sections",
                                      summary.getSectionsCreated(), summary.getSectionsUpdated(), summary.getSectionsSkipped());

        return new Response<>(StatusCode.OK, message, response);
    }

    // Helper method to process sections in bulk
    private void processSectionsBulk(PlaybookTool playbookTool, List<PlaybookToolSectionRequest> updatingSectionRequests, List<Long> deletedSectionIds,
                                    User user, PlaybookToolWithSectionsResponse.BulkOperationSummary summary) {
        for (PlaybookToolSectionRequest sectionRequest : updatingSectionRequests) {
            try {
                if (sectionRequest.getId() == null) {
                    // Create new section
                    createSectionFromRequest(playbookTool, sectionRequest, user, summary);
                } else {
                    // Update existing section
                    updateSectionFromRequest(sectionRequest, user, summary);
                }
            } catch (Exception e) {
                summary.getErrors().add("Error processing section '" + sectionRequest.getName() + "': " + e.getMessage());
                summary.setSectionsSkipped(summary.getSectionsSkipped() + 1);
            }
        }
        
        for (Long deletedId : deletedSectionIds) {
            try {
                deleteSectionFromRequest(deletedId, user, summary);
            }
            catch (Exception e) {
                summary.getErrors().add("Error processing deleted section '" + deletedId + "': " + e.getMessage());
                summary.setSectionsSkipped(summary.getSectionsSkipped() + 1);
            }
        }
    }

    // Helper method to create new section
    private void createSectionFromRequest(PlaybookTool playbookTool, PlaybookToolSectionRequest sectionRequest,
                                        User user, PlaybookToolWithSectionsResponse.BulkOperationSummary summary) {
        // Validate required fields for creation
        if (sectionRequest.getName() == null || sectionRequest.getName().trim().isEmpty()) {
            throw new RuntimeException("Section name is required for creation");
        }

        PlaybookToolSection newSection = new PlaybookToolSection();
        newSection.setName(sectionRequest.getName());
        newSection.setContent(sectionRequest.getContent() != null ? sectionRequest.getContent() : "");
        newSection.setIsInTrash(false);
        newSection.setPlaybookTool(playbookTool);
        newSection.setCreatedBy(user);
        newSection.setLastModifiedBy(user);

        PlaybookToolSection savedSection = playbookToolSectionRepository.save(newSection);

        // Create history record
        createHistoryRecord(savedSection, EHistoryActionType.CREATE, null, null, null, user);

        summary.setSectionsCreated(summary.getSectionsCreated() + 1);
    }

    // Helper method to update existing section
    private void updateSectionFromRequest(PlaybookToolSectionRequest sectionRequest, User user,
                                        PlaybookToolWithSectionsResponse.BulkOperationSummary summary) {
        Optional<PlaybookToolSection> optSection = playbookToolSectionRepository.findById(sectionRequest.getId());
        if (optSection.isEmpty()) {
            throw new RuntimeException("Section with ID " + sectionRequest.getId() + " not found");
        }

        PlaybookToolSection existingSection = optSection.get();

        // Track changes for history
        boolean nameChanged = false;
        boolean contentChanged = false;
        String originalName = existingSection.getName();
        String originalContent = existingSection.getContent();

        // Update name if provided and different
        if (sectionRequest.getName() != null && !sectionRequest.getName().trim().isEmpty()
            && !sectionRequest.getName().equals(originalName)) {
            existingSection.setName(sectionRequest.getName());
            nameChanged = true;
        }

        // Update content if provided and different
        if (sectionRequest.getContent() != null && !sectionRequest.getContent().equals(originalContent)) {
            existingSection.setContent(sectionRequest.getContent());
            contentChanged = true;
        }

        // Save if any changes were made
        if (nameChanged || contentChanged) {
            existingSection.setLastModifiedBy(user);
            PlaybookToolSection savedSection = playbookToolSectionRepository.save(existingSection);

            // Create history records for changed fields
            if (nameChanged) {
                createHistoryRecord(savedSection, EHistoryActionType.UPDATE, "name", originalName, sectionRequest.getName(), user);
            }
            if (contentChanged) {
                createHistoryRecord(savedSection, EHistoryActionType.UPDATE, "content", originalContent, sectionRequest.getContent(), user);
            }

            summary.setSectionsUpdated(summary.getSectionsUpdated() + 1);
        } else {
            summary.setSectionsSkipped(summary.getSectionsSkipped() + 1);
        }
    }
    
    private void deleteSectionFromRequest(Long deletedId, User user,
                                          PlaybookToolWithSectionsResponse.BulkOperationSummary summary) {
        Optional<PlaybookToolSection> optSection = playbookToolSectionRepository.findById(deletedId);
        if (optSection.isEmpty()) {
            throw new RuntimeException("Section to delete with ID " + deletedId + " not found");
        }
        PlaybookToolSection section = optSection.get();
        section.setIsInTrash(true);
        section.setLastModifiedBy(user);
        playbookToolSectionRepository.save(section);

        // Create history record for deleting
        createHistoryRecord(section, EHistoryActionType.DELETE, null, null, null, user);
        
        summary.setSectionsDeleted(summary.getSectionsDeleted() + 1);
    }

    // PlaybookToolHistory operations

    @Override
    public Response<Page<PlaybookToolHistoryView>> getPlaybookToolHistories(Long playbookToolId, int page, int limit, String sortBy, String sortDirection, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (playbookToolId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool ID is required!");
        }

        // Verify playbook tool exists
        Optional<PlaybookTool> optPlaybookTool = playbookToolRepository.findById(playbookToolId);
        if (!optPlaybookTool.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool not found!");
        }

        Sort.Direction direction = sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        String sortField = sortBy != null ? sortBy : "historyDate";
        Pageable pageable = PageRequest.of(page, limit, Sort.by(direction, sortField));

        Page<PlaybookToolHistory> historiesPage = playbookToolHistoryRepository.findByPlaybookToolIdOrderByHistoryDateDesc(playbookToolId, pageable);
        Page<PlaybookToolHistoryView> response = historiesPage.map(playbookMapper::toPlaybookToolHistoryView);

        return new Response<>(StatusCode.OK, "Playbook tool histories retrieved successfully!", response);
    }

    @Override
    @Transactional
    public Response<String> restorePlaybookToolSectionFromHistory(Long historyId, Long accountId) {
        // Validate user performing the restoration
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (historyId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "History ID is required!");
        }

        // Find the history record
        Optional<PlaybookToolHistory> optHistory = playbookToolHistoryRepository.findById(historyId);
        if (!optHistory.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "History record not found!");
        }

        PlaybookToolHistory history = optHistory.get();

        // Validate that this is a restorable history record (UPDATE or DELETE)
        if (history.getActionType() != EHistoryActionType.UPDATE && history.getActionType() != EHistoryActionType.DELETE) {
            return new Response<>(StatusCode.BAD_REQUEST, "Only UPDATE or DELETE history records can be restored!");
        }

        // Get the playbook tool section
        PlaybookToolSection section = history.getPlaybookToolSection();
        if (section == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Associated playbook tool section not found!");
        }

        String restoredValue = null;
        String currentValue = null;
        String fieldName = history.getFieldName();

        if (history.getActionType() == EHistoryActionType.DELETE) {
            // For DELETE records, restore by setting isInTrash = false
            section.setIsInTrash(false);
            section.setLastModifiedBy(optUser.get());
            restoredValue = "false";
            currentValue = "true";
            fieldName = "isInTrash";
        } else if (history.getActionType() == EHistoryActionType.UPDATE) {
            // For UPDATE records, restore the original value
            if (history.getFieldName() == null) {
                return new Response<>(StatusCode.BAD_REQUEST, "Cannot restore: field name is not specified in history record!");
            }

            if ("name".equals(history.getFieldName())) {
                currentValue = section.getName();
                section.setName(history.getOriginalValue());
                section.setLastModifiedBy(optUser.get());
                restoredValue = history.getOriginalValue();
            } else if ("content".equals(history.getFieldName())) {
                currentValue = section.getContent();
                section.setContent(history.getOriginalValue());
                section.setLastModifiedBy(optUser.get());
                restoredValue = history.getOriginalValue();
            } else {
                return new Response<>(StatusCode.BAD_REQUEST, "Cannot restore: unsupported field '" + history.getFieldName() + "'!");
            }
        }

        // Save the restored section
        PlaybookToolSection savedSection = playbookToolSectionRepository.save(section);

        // Create a new history record for the restoration
        createHistoryRecord(savedSection, EHistoryActionType.UPDATE, fieldName, currentValue, restoredValue, optUser.get());

        String message = String.format("Playbook tool section restored successfully from %s history record!",
                                     history.getActionType().toString().toLowerCase());
        return new Response<>(StatusCode.OK, message, "Restored");
    }

    // Helper method to create history records
    private void createHistoryRecord(PlaybookToolSection section, EHistoryActionType actionType,
                                   String fieldName, String originalValue, String newValue, User user) {
        PlaybookToolHistory history = new PlaybookToolHistory();
        history.setActionType(actionType);
        history.setFieldName(fieldName);
        history.setOriginalValue(originalValue);
        history.setNewValue(newValue);
        history.setPlaybookToolSection(section);
        history.setPlaybookTool(section.getPlaybookTool());
        history.setUser(user);

        // Generate description based on action type
        String description = generateHistoryDescription(actionType, fieldName, section, user);
        history.setDescription(description);

        playbookToolHistoryRepository.save(history);
    }

    // Helper method to generate history descriptions
    private String generateHistoryDescription(EHistoryActionType actionType, String fieldName,
                                             PlaybookToolSection section, User user) {
        String userName = user != null ? (user.getFirstName() + " " + user.getLastName()) : "System";
        String sectionName = section.getName();
        String toolName = section.getPlaybookTool().getName();

        switch (actionType) {
            case CREATE:
                return String.format("%s created section '%s' in playbook tool '%s'", userName, sectionName, toolName);
            case UPDATE:
                if ("name".equals(fieldName)) {
                    return String.format("%s updated name of section in playbook tool '%s'", userName, toolName);
                } else if ("content".equals(fieldName)) {
                    return String.format("%s updated content of section '%s' in playbook tool '%s'", userName, sectionName, toolName);
                } else if ("isInTrash".equals(fieldName)) {
                    return String.format("%s restored section '%s' in playbook tool '%s'", userName, sectionName, toolName);
                } else {
                    return String.format("%s updated section '%s' in playbook tool '%s'", userName, sectionName, toolName);
                }
            case DELETE:
                return String.format("%s deleted section '%s' from playbook tool '%s'", userName, sectionName, toolName);
            default:
                return String.format("%s performed action on section '%s' in playbook tool '%s'", userName, sectionName, toolName);
        }
    }

    // GroupedModulePlaybookProcess operations

    @Override
    public Response<List<GroupedModulePlaybookProcessView>> getAllGroupedModulePlaybookProcesses(Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        List<GroupedModulePlaybookProcess> groupedProcesses = groupedModulePlaybookProcessRepository.findAll();
        List<GroupedModulePlaybookProcessView> response = playbookMapper.toGroupedModulePlaybookProcessView(groupedProcesses);

        return new Response<>(StatusCode.OK, "Grouped module playbook processes retrieved successfully!", response);
    }

    @Override
    @Transactional
    public Response<GroupedModulePlaybookProcessView> updateGroupedModulePlaybookProcessStatus(UpdateGroupedModulePlaybookProcessStatusRequest request, Long accountId) {
        // Validate user performing the update
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getModuleCode() == null || request.getModuleCode().trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module code is required!");
        }

        if (request.getStatus() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Status is required!");
        }

        // Find the GroupedModulePlaybookProcess by moduleCode
        Optional<GroupedModulePlaybookProcess> optGroupedProcess = groupedModulePlaybookProcessRepository.findByModuleCode(request.getModuleCode());
        if (!optGroupedProcess.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Grouped module playbook process not found for module: " + request.getModuleCode());
        }

        GroupedModulePlaybookProcess groupedProcess = optGroupedProcess.get();

        // Update the status
        groupedProcess.setStatus(request.getStatus());
        groupedProcess.setLastModifiedBy(optUser.get());

        // Save the updated process
        GroupedModulePlaybookProcess updatedProcess = groupedModulePlaybookProcessRepository.save(groupedProcess);

        // Convert to view and return
        GroupedModulePlaybookProcessView response = playbookMapper.toGroupedModulePlaybookProcessView(updatedProcess);

        String statusText = request.getStatus() == EPlaybookStatus.PUBLISHED ? "published" : "draft";
        return new Response<>(StatusCode.OK, "Grouped module playbook process status updated to " + statusText + " successfully!", response);
    }

    // ModuleStagePlaybook operations

    @Override
    public Response<List<ModuleStagePlaybookView>> getModuleStagePlaybooksByModuleCode(String moduleCode, Long accountId, String language) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (moduleCode == null || moduleCode.trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module code is required!");
        }

        // Verify grouped process exists for the module code
        Optional<GroupedModulePlaybookProcess> optGroupedProcess = groupedModulePlaybookProcessRepository.findByModuleCode(moduleCode);
        if (!optGroupedProcess.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Grouped module playbook process not found for module: " + moduleCode);
        }

        List<ModuleStagePlaybook> moduleStagePlaybooks = moduleStagePlaybookRepository.findByModuleCode(moduleCode);
        List<ModuleStagePlaybookView> response = playbookMapper.toModuleStagePlaybookView(moduleStagePlaybooks);

        List<TblListValue> opportunityStageItems = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(
                CodeGeneratorUtils.generateListCode("OPPORTUNITY", "stage"),
                language
        );
        List<TblListValue> leadStatusItems = tblListValueRepository.findByListCodeAndLanguageOrderByPositionAsc(
                CodeGeneratorUtils.generateListCode("LEAD", "status"),
                language
        );
        response.forEach(moduleStagePlaybookView -> {
            if (moduleStagePlaybookView.getModuleCode().equals("OPPORTUNITY")) {
                opportunityStageItems.forEach(opportunityStageItem -> {
                    if (opportunityStageItem.getValue().equals(moduleStagePlaybookView.getModuleStageValue())) {
                        moduleStagePlaybookView.setModuleStageLabel(opportunityStageItem.getLabel());
                        moduleStagePlaybookView.setModuleStagePosition(opportunityStageItem.getPosition());
                    }
                });
            } else if (moduleStagePlaybookView.getModuleCode().equals("LEAD")) {
                leadStatusItems.forEach(leadStatusItem -> {
                    if (leadStatusItem.getValue().equals(moduleStagePlaybookView.getModuleStageValue())) {
                        moduleStagePlaybookView.setModuleStageLabel(leadStatusItem.getLabel());
                        moduleStagePlaybookView.setModuleStagePosition(leadStatusItem.getPosition());
                    }
                });
            }
        });
        // Sort the response list by moduleStagePosition
        response.sort(Comparator.comparingInt(ModuleStagePlaybookView::getModuleStagePosition));

        return new Response<>(StatusCode.OK, "Module stage playbooks retrieved successfully!", response);
    }

    @Override
    public Response<ModuleStagePlaybookDetailView> getModuleStagePlaybookDetail(Long id, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (id == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook ID is required!");
        }

        // Find the module stage playbook by ID
        Optional<ModuleStagePlaybook> optModuleStagePlaybook = moduleStagePlaybookRepository.findById(id);
        if (!optModuleStagePlaybook.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found!");
        }

        ModuleStagePlaybook moduleStagePlaybook = optModuleStagePlaybook.get();

        // Create response manually to avoid entity modification issues
        ModuleStagePlaybookDetailView response = new ModuleStagePlaybookDetailView();
        response.setId(moduleStagePlaybook.getId());
        response.setModuleCode(moduleStagePlaybook.getModuleCode());
        response.setModuleStageValue(moduleStagePlaybook.getModuleStageValue());
        response.setGuideForSuccessContent(moduleStagePlaybook.getGuideForSuccessContent());

        // Load and map playbook tools with their sections
        List<PlaybookTool> playbookTools = moduleStagePlaybook.getPlaybookTools();
        List<ModuleStagePlaybookToolView> playbookToolViews = new ArrayList<>();

        if (playbookTools != null && !playbookTools.isEmpty()) {
            for (PlaybookTool tool : playbookTools) {
                // Load sections separately (only non-trashed sections)
                List<PlaybookToolSection> sections = playbookToolSectionRepository.findByPlaybookToolIdAndIsInTrashFalse(tool.getId());

                // Create tool view manually
                ModuleStagePlaybookToolView toolView = new ModuleStagePlaybookToolView();
                toolView.setId(tool.getId());
                toolView.setName(tool.getName());
                toolView.setDescription(tool.getDescription());
                toolView.setViewCount(tool.getViewCount());

                // Extract section names
                List<String> sectionNames = sections.stream()
                    .map(PlaybookToolSection::getName)
                    .toList();
                toolView.setSectionNames(sectionNames);

                playbookToolViews.add(toolView);
            }
        }

        response.setPlaybookTools(playbookToolViews);

        return new Response<>(StatusCode.OK, "Module stage playbook detail retrieved successfully!", response);
    }

    @Override
    public Response<ModuleStagePlaybookDetailView> getModuleStagePlaybookDetailByModuleCodeAndStage(String moduleCode, String moduleStageValue, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (moduleCode == null || moduleCode.trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module code is required!");
        }
        if (moduleStageValue == null || moduleStageValue.trim().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage value is required!");
        }
        
        Optional<ModuleStagePlaybook> optModuleStagePlaybook = moduleStagePlaybookRepository.findByModuleCodeAndModuleStageValue(moduleCode, moduleStageValue);
        if (!optModuleStagePlaybook.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found for module: " + moduleCode + " and stage: " + moduleStageValue);
        }

        ModuleStagePlaybook moduleStagePlaybook = optModuleStagePlaybook.get();
        
        ModuleStagePlaybookDetailView response = new ModuleStagePlaybookDetailView();
        response.setId(moduleStagePlaybook.getId());
        response.setModuleCode(moduleStagePlaybook.getModuleCode());
        response.setModuleStageValue(moduleStagePlaybook.getModuleStageValue());
        response.setGuideForSuccessContent(moduleStagePlaybook.getGuideForSuccessContent());
        
        List<PlaybookTool> playbookTools = moduleStagePlaybook.getPlaybookTools();
        List<ModuleStagePlaybookToolView> playbookToolViews = new ArrayList<>();

        if (playbookTools != null && !playbookTools.isEmpty()) {
            for (PlaybookTool tool : playbookTools) {
                List<PlaybookToolSection> sections = playbookToolSectionRepository.findByPlaybookToolIdAndIsInTrashFalse(tool.getId());
                
                ModuleStagePlaybookToolView toolView = new ModuleStagePlaybookToolView();
                toolView.setId(tool.getId());
                toolView.setName(tool.getName());
                toolView.setDescription(tool.getDescription());
                toolView.setViewCount(tool.getViewCount());
                
                List<String> sectionNames = sections.stream()
                    .map(PlaybookToolSection::getName)
                    .toList();
                toolView.setSectionNames(sectionNames);

                playbookToolViews.add(toolView);
            }
        }

        response.setPlaybookTools(playbookToolViews);

        return new Response<>(StatusCode.OK, "Module stage playbook detail retrieved successfully!", response);
    }

    @Override
    @Transactional
    public Response<ModuleStagePlaybookDetailView> updateModuleStagePlaybookGuideForSuccessContent(UpdateModuleStagePlaybookContentRequest request, Long accountId) {
        // Validate user performing the update
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook ID is required!");
        }

        // Find the module stage playbook by ID
        Optional<ModuleStagePlaybook> optModuleStagePlaybook = moduleStagePlaybookRepository.findById(request.getId());
        if (!optModuleStagePlaybook.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found!");
        }

        ModuleStagePlaybook moduleStagePlaybook = optModuleStagePlaybook.get();

        // Update the guide for success content
        if (request.getGuideForSuccessContent() != null) {
            moduleStagePlaybook.setGuideForSuccessContent(request.getGuideForSuccessContent());
            moduleStagePlaybook.setLastModifiedBy(optUser.get());
        }

        ModuleStagePlaybook savedModuleStagePlaybook = moduleStagePlaybookRepository.save(moduleStagePlaybook);
        ModuleStagePlaybookDetailView response = playbookMapper.toModuleStagePlaybookDetailView(savedModuleStagePlaybook);

        return new Response<>(StatusCode.OK, "Module stage playbook content updated successfully!", response);
    }

    @Override
    public Response<Page<ModuleStagePlaybookToolView>> getModuleStagePlaybookTools(Long moduleStagePlaybookId, int page, int limit, String sortBy, String sortDirection, Long accountId) {
        // Validate user performing the request
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (moduleStagePlaybookId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook ID is required!");
        }

        // Find the module stage playbook by ID with playbook tools
        ModuleStagePlaybook moduleStagePlaybook = moduleStagePlaybookRepository.findByIdWithPlaybookTools(moduleStagePlaybookId);
        if (moduleStagePlaybook == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found!");
        }

        // Get playbook tools from the module stage playbook
        List<PlaybookTool> playbookTools = moduleStagePlaybook.getPlaybookTools();
        if (playbookTools == null) {
            playbookTools = List.of();
        }

        // Create a list to hold tools with their sections loaded
        List<PlaybookTool> toolsWithSections = new ArrayList<>();
        for (PlaybookTool tool : playbookTools) {
            // Create a new instance to avoid collection modification issues
            PlaybookTool toolCopy = new PlaybookTool();
            toolCopy.setId(tool.getId());
            toolCopy.setName(tool.getName());
            toolCopy.setDescription(tool.getDescription());
            toolCopy.setViewCount(tool.getViewCount());

            // Load sections separately
            List<PlaybookToolSection> sections = playbookToolSectionRepository.findByPlaybookToolIdAndIsInTrashFalse(tool.getId());
            toolCopy.setPlaybookToolSections(sections);

            toolsWithSections.add(toolCopy);
        }

        playbookTools = toolsWithSections;

        // Apply sorting
        Sort.Direction direction = sortDirection.equalsIgnoreCase("ASC") ? Sort.Direction.ASC : Sort.Direction.DESC;
        String sortField = sortBy != null ? sortBy : "id";

        // Sort the list manually since we're not using repository pagination
        playbookTools = playbookTools.stream()
            .sorted((tool1, tool2) -> {
                int comparison = 0;
                switch (sortField) {
                    case "name":
                        comparison = tool1.getName().compareToIgnoreCase(tool2.getName());
                        break;
                    case "viewCount":
                        comparison = tool1.getViewCount().compareTo(tool2.getViewCount());
                        break;
                    default: // "id"
                        comparison = tool1.getId().compareTo(tool2.getId());
                        break;
                }
                return direction == Sort.Direction.DESC ? -comparison : comparison;
            })
            .toList();

        // Apply pagination manually
        int start = page * limit;
        int end = Math.min(start + limit, playbookTools.size());
        List<PlaybookTool> paginatedTools = playbookTools.subList(start, end);

        // Convert to view objects
        List<ModuleStagePlaybookToolView> toolViews = playbookMapper.toModuleStagePlaybookToolView(paginatedTools);

        // Create pageable and page objects
        Pageable pageable = PageRequest.of(page, limit, Sort.by(direction, sortField));
        Page<ModuleStagePlaybookToolView> response = new PageImpl<>(toolViews, pageable, playbookTools.size());

        return new Response<>(StatusCode.OK, "Module stage playbook tools retrieved successfully!", response);
    }

    @Override
    @Transactional
    public Response<String> addPlaybookToolsToModuleStagePlaybook(AddPlaybookToolsRequest request, Long accountId) {
        // Validate user performing the operation
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getModuleStagePlaybookId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook ID is required!");
        }

        if (request.getPlaybookToolIds() == null || request.getPlaybookToolIds().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool IDs are required!");
        }

        // Find the module stage playbook
        Optional<ModuleStagePlaybook> optModuleStagePlaybook = moduleStagePlaybookRepository.findById(request.getModuleStagePlaybookId());
        if (!optModuleStagePlaybook.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found!");
        }

        ModuleStagePlaybook moduleStagePlaybook = optModuleStagePlaybook.get();

        // Get existing playbook tools
        List<PlaybookTool> existingTools = moduleStagePlaybook.getPlaybookTools();
        if (existingTools == null) {
            existingTools = new ArrayList<>();
            moduleStagePlaybook.setPlaybookTools(existingTools);
        }

        // Get existing tool IDs to avoid duplicates
        List<Long> existingToolIds = existingTools.stream()
            .map(PlaybookTool::getId)
            .toList();

        // Find and add new playbook tools
        int addedCount = 0;
        for (Long toolId : request.getPlaybookToolIds()) {
            if (!existingToolIds.contains(toolId)) {
                Optional<PlaybookTool> optTool = playbookToolRepository.findById(toolId);
                if (optTool.isPresent()) {
                    existingTools.add(optTool.get());
                    addedCount++;
                }
            }
        }

        // Save the updated module stage playbook
        moduleStagePlaybookRepository.save(moduleStagePlaybook);

        String message = String.format("Successfully added %d playbook tool(s) to module stage playbook!", addedCount);
        return new Response<>(StatusCode.OK, message, "Added");
    }

    @Override
    @Transactional
    public Response<String> removePlaybookToolsFromModuleStagePlaybook(RemovePlaybookToolsRequest request, Long accountId) {
        // Validate user performing the operation
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (!optUser.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!");
        }

        // Validate required parameters
        if (request.getModuleStagePlaybookId() == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook ID is required!");
        }

        if (request.getPlaybookToolIds() == null || request.getPlaybookToolIds().isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Playbook tool IDs are required!");
        }

        // Find the module stage playbook
        Optional<ModuleStagePlaybook> optModuleStagePlaybook = moduleStagePlaybookRepository.findById(request.getModuleStagePlaybookId());
        if (!optModuleStagePlaybook.isPresent()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Module stage playbook not found!");
        }

        ModuleStagePlaybook moduleStagePlaybook = optModuleStagePlaybook.get();

        // Get existing playbook tools
        List<PlaybookTool> existingTools = moduleStagePlaybook.getPlaybookTools();
        if (existingTools == null || existingTools.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "No playbook tools found to remove!");
        }

        // Remove specified playbook tools
        int removedCount = 0;
        List<PlaybookTool> toolsToRemove = new ArrayList<>();

        for (Long toolId : request.getPlaybookToolIds()) {
            for (PlaybookTool tool : existingTools) {
                if (tool.getId().equals(toolId)) {
                    toolsToRemove.add(tool);
                    removedCount++;
                    break;
                }
            }
        }

        // Remove the tools from the list
        existingTools.removeAll(toolsToRemove);

        // Save the updated module stage playbook
        moduleStagePlaybookRepository.save(moduleStagePlaybook);

        String message = String.format("Successfully removed %d playbook tool(s) from module stage playbook!", removedCount);
        return new Response<>(StatusCode.OK, message, "Removed");
    }
}
