package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.FieldSettingsRepository;
import com.tti.oh_crm_service.repository.HistoryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.Column;
import jakarta.persistence.OneToMany;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.JoinColumn;
import java.lang.reflect.Field;
import java.util.Collection;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Slf4j
public class HistoryTrackingService {

    @Autowired
    private HistoryRepository historyRepository;

    @Autowired
    private FieldSettingsRepository fieldSettingsRepository;

    // Cache for field mappings to avoid repeated reflection calls
    private final Map<Class<?>, Map<String, String>> fieldToColumnCache = new HashMap<>();

    // Cache for FieldSettings to avoid repeated database calls
    private final Map<String, FieldSettings> fieldSettingsCache = new HashMap<>();

    // Date formatter for consistent date string representation
    private final SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * Record history for entity creation
     */
    public void recordCreateHistory(Object entity, User user) {
        try {
            String moduleCode = getModuleCode(entity.getClass());
            Long entityId = getEntityId(entity);

            History history = new History();
            history.setActionType(EHistoryActionType.CREATE);
            history.setRelatedToModule(moduleCode);
            history.setRelatedToId(entityId);
            history.setUser(user);

            historyRepository.save(history);
        } catch (Exception e) {
            log.error("Error recording create history for entity: " + entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Record history for entity updates by comparing original and updated entities
     */
    public void recordUpdateHistory(Object originalEntity, Object updatedEntity, User user) {
        if (originalEntity == null || updatedEntity == null) {
            log.warn("Cannot record update history - original or updated entity is null");
            return;
        }

        if (!originalEntity.getClass().equals(updatedEntity.getClass())) {
            log.warn("Cannot record update history - entity types don't match: {} vs {}",
                    originalEntity.getClass().getSimpleName(), updatedEntity.getClass().getSimpleName());
            return;
        }

        try {
            String moduleCode = getModuleCode(updatedEntity.getClass());
            Long entityId = getEntityId(updatedEntity);

            log.info("🔍 Starting field comparison for {} with ID: {} (User: {})",
                    moduleCode, entityId, user != null ? user.getId() : "null");

            if (entityId == null) {
                log.warn("Cannot record update history - entity ID is null for {}", moduleCode);
                return;
            }

            List<History> historyRecords = compareEntitiesAndCreateHistory(
                originalEntity, updatedEntity, moduleCode, entityId, user);

            if (!historyRecords.isEmpty()) {
                try {
                    // Validate all history records before saving
                    for (History history : historyRecords) {
                        if (history == null) {
                            log.error("❌ Null history record found, skipping save operation");
                            return;
                        }
                        if (history.getUser() == null) {
                            log.error("❌ History record has null user, skipping save operation");
                            return;
                        }
                        if (history.getRelatedToModule() == null || history.getRelatedToModule().trim().isEmpty()) {
                            log.error("❌ History record has null/empty module code, skipping save operation");
                            return;
                        }
                    }

                    log.info("💾 Saving {} history records to database...", historyRecords.size());
                    historyRepository.saveAll(historyRecords);
                    log.info("✅ Successfully recorded {} field changes for {} with ID: {}",
                            historyRecords.size(), moduleCode, entityId);

                    // Log summary of changes
                    historyRecords.forEach(history ->
                        log.info("   📝 Field '{}' changed: '{}' → '{}'",
                                history.getFieldName(),
                                history.getFieldOriginalValue(),
                                history.getFieldNewValue())
                    );
                } catch (Exception saveException) {
                    log.error("❌ Failed to save history records for {} with ID: {} - {}",
                             moduleCode, entityId, saveException.getMessage(), saveException);
                    // Don't rethrow - we don't want to break the main transaction
                }
            } else {
                log.info("ℹ️ No field changes detected for {} with ID: {}", moduleCode, entityId);
            }
        } catch (Exception e) {
            log.error("❌ Error recording update history for entity: " + updatedEntity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Record update history in a separate transaction to avoid conflicts with main transaction
     * This method should be used when you want to ensure history tracking doesn't interfere
     * with the main business operation
     *
     * @param originalEntity The original entity state before changes
     * @param updatedEntity The updated entity state after changes
     * @param user The user who performed the update
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void recordUpdateHistoryInNewTransaction(Object originalEntity, Object updatedEntity, User user) {
        try {
            log.info("🔄 Starting update history tracking in new transaction for: {}",
                    updatedEntity.getClass().getSimpleName());

            recordUpdateHistory(originalEntity, updatedEntity, user);

            log.info("✅ Successfully completed update history tracking in new transaction for: {}",
                    updatedEntity.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("❌ Error in new transaction update history tracking for entity: {} - {}",
                     updatedEntity.getClass().getSimpleName(), e.getMessage(), e);
            // Don't rethrow - we don't want to affect the main transaction
        }
    }

    /**
     * Record create history in a separate transaction to avoid conflicts with main transaction
     * This method should be used when you want to ensure history tracking doesn't interfere
     * with the main business operation
     *
     * @param entity The entity that was created
     * @param user The user who performed the creation
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void recordCreateHistoryInNewTransaction(Object entity, User user) {
        try {
            log.info("🔄 Starting create history tracking in new transaction for: {}",
                    entity.getClass().getSimpleName());

            recordCreateHistory(entity, user);

            log.info("✅ Successfully completed create history tracking in new transaction for: {}",
                    entity.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("❌ Error in new transaction create history tracking for entity: {} - {}",
                     entity.getClass().getSimpleName(), e.getMessage(), e);
            // Don't rethrow - we don't want to affect the main transaction
        }
    }

    /**
     * Compare two entities and create history records for changed fields
     */
    private List<History> compareEntitiesAndCreateHistory(Object originalEntity, Object updatedEntity,
                                                         String moduleCode, Long entityId, User user) {
        List<History> historyRecords = new ArrayList<>();
        Class<?> entityClass = updatedEntity.getClass();

        log.info("🔍 Starting detailed field comparison for entity: {}", entityClass.getSimpleName());

        // Get field to column mapping for this entity class
        Map<String, String> fieldToColumnMap = getFieldToColumnMapping(entityClass);
        log.info("📋 Field to column mapping loaded: {} mappings found", fieldToColumnMap.size());

        // Get all fields from the entity class and its superclasses
        List<Field> allFields = getAllFields(entityClass);
        log.info("🔎 Found {} total fields to examine (including superclass fields)", allFields.size());

        int fieldsProcessed = 0;
        int fieldsSkipped = 0;
        int fieldsChanged = 0;
        int fieldsUnchanged = 0;

        for (Field field : allFields) {
            try {
                // Skip static, transient, and ID fields
                if (shouldSkipField(field)) {
                    fieldsSkipped++;
                    log.info("⏭️ Skipping field '{}' (reason: system/audit field)", field.getName());
                    continue;
                }

                fieldsProcessed++;
                field.setAccessible(true);
                Object originalValue = field.get(originalEntity);
                Object updatedValue = field.get(updatedEntity);

                // Special handling for @ManyToOne relationships - compare IDs instead of object references
                if (field.isAnnotationPresent(ManyToOne.class)) {
                    log.info("🔗 Processing @ManyToOne field '{}': originalEntity={}, updatedEntity={}",
                             field.getName(),
                             originalValue != null ? originalValue.getClass().getSimpleName() : "null",
                             updatedValue != null ? updatedValue.getClass().getSimpleName() : "null");

                    Long originalId = extractEntityId(originalValue);
                    Long updatedId = extractEntityId(updatedValue);

                    log.info("🔗 Comparing @ManyToOne field '{}' by ID: original='{}' vs updated='{}'",
                             field.getName(),
                             originalId,
                             updatedId);

                    // Use the IDs for comparison
                    originalValue = originalId;
                    updatedValue = updatedId;
                } else {
                    // Log field comparison details for regular fields
                    log.info("🔍 Comparing field '{}': original='{}' vs updated='{}'",
                             field.getName(),
                             formatValueForLog(originalValue),
                             formatValueForLog(updatedValue));
                }

                // Check if values are different
                if (!Objects.equals(originalValue, updatedValue)) {
                    fieldsChanged++;
                    String columnName = fieldToColumnMap.get(field.getName());

                    log.info("🔄 Field CHANGED: '{}' → column '{}' | '{}' → '{}'",
                            field.getName(), columnName,
                            formatValueForLog(originalValue),
                            formatValueForLog(updatedValue));

                    if (columnName != null) {
                        History history = createHistoryRecord(
                            moduleCode, entityId, columnName, field.getName(),
                            originalValue, updatedValue, user);

                        if (history != null) {
                            historyRecords.add(history);
                            log.info("✅ History record created for field '{}'", field.getName());
                        } else {
                            log.warn("⚠️ Failed to create history record for field '{}'", field.getName());
                        }
                    } else {
                        log.warn("⚠️ No column mapping found for field '{}' - skipping history record", field.getName());
                    }
                } else {
                    fieldsUnchanged++;
                    log.info("✅ Field unchanged: '{}' = '{}'", field.getName(), formatValueForLog(originalValue));
                }
            } catch (Exception e) {
                log.error("❌ Error comparing field '{}' for entity: {}",
                         field.getName(), entityClass.getSimpleName(), e);
            }
        }

        log.info("📊 Field comparison summary for {} ID {}: {} total fields, {} processed, {} skipped, {} changed, {} unchanged",
                moduleCode, entityId, allFields.size(), fieldsProcessed, fieldsSkipped, fieldsChanged, fieldsUnchanged);

        return historyRecords;
    }

    /**
     * Create a history record for a field change
     */
    private History createHistoryRecord(String moduleCode, Long entityId, String columnName,
                                       String fieldName, Object originalValue, Object updatedValue, User user) {
        try {
            log.info("🏗️ Creating history record for field '{}' (column: '{}')", fieldName, columnName);

            // Validate required fields
            if (moduleCode == null || moduleCode.trim().isEmpty()) {
                log.error("❌ Cannot create history record: moduleCode is null or empty");
                return null;
            }

            if (entityId == null) {
                log.error("❌ Cannot create history record: entityId is null");
                return null;
            }

            if (columnName == null || columnName.trim().isEmpty()) {
                log.error("❌ Cannot create history record: columnName is null or empty for field '{}'", fieldName);
                return null;
            }

            if (user == null) {
                log.error("❌ Cannot create history record: user is null for field '{}'", fieldName);
                return null;
            }

            // Get field settings to determine field type
            FieldSettings fieldSettings = getFieldSettings(moduleCode, columnName);
            EFieldType fieldType = fieldSettings != null ? fieldSettings.getFieldType() : EFieldType.ONE_LINE;

            if (fieldSettings != null) {
                log.info("📋 Found FieldSettings for {}.{}: type={}", moduleCode, columnName, fieldType);
            } else {
                log.info("⚠️ No FieldSettings found for {}.{}, using default type: {}", moduleCode, columnName, fieldType);
            }

            String formattedOriginalValue = formatValue(originalValue);
            String formattedNewValue = formatValue(updatedValue);

            // Validate formatted values length (TEXT(2000) constraint)
            if (formattedOriginalValue != null && formattedOriginalValue.length() > 2000) {
                log.warn("⚠️ Original value too long ({}), truncating to 2000 chars", formattedOriginalValue.length());
                formattedOriginalValue = formattedOriginalValue.substring(0, 1997) + "...";
            }

            if (formattedNewValue != null && formattedNewValue.length() > 2000) {
                log.warn("⚠️ New value too long ({}), truncating to 2000 chars", formattedNewValue.length());
                formattedNewValue = formattedNewValue.substring(0, 1997) + "...";
            }

            log.info("📝 History record details:");
            log.info("   Module: {}, Entity ID: {}", moduleCode, entityId);
            log.info("   Field: {} → Column: {}", fieldName, columnName);
            log.info("   Type: {}", fieldType);
            log.info("   Original: {} → Formatted: '{}'", formatValueForLog(originalValue), formattedOriginalValue);
            log.info("   New: {} → Formatted: '{}'", formatValueForLog(updatedValue), formattedNewValue);
            log.info("   User: {}", user != null ? user.getId() : "null");

            History history = new History();
            history.setActionType(EHistoryActionType.UPDATE);
            history.setRelatedToModule(moduleCode);
            history.setRelatedToId(entityId);
            history.setFieldName(columnName);
            history.setFieldType(fieldType);
            history.setFieldOriginalValue(formattedOriginalValue);
            history.setFieldNewValue(formattedNewValue);
            history.setUser(user);

            log.info("✅ History record created successfully for field '{}'", fieldName);
            return history;
        } catch (Exception e) {
            log.error("❌ Error creating history record for field '{}' (column: '{}'): {}",
                     fieldName, columnName, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Extract ID from an entity object
     */
    private Long getEntityId(Object entity) {
        if (entity == null) {
            return null;
        }

        try {
            // Try to find the ID field
            Field idField = findIdField(entity.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                Object idValue = idField.get(entity);
                if (idValue instanceof Long) {
                    return (Long) idValue;
                } else if (idValue instanceof Integer) {
                    return ((Integer) idValue).longValue();
                } else if (idValue != null) {
                    return Long.valueOf(idValue.toString());
                }
            }
        } catch (Exception e) {
            log.error("Error extracting ID from entity: " + entity.getClass().getSimpleName(), e);
        }

        return null;
    }

    /**
     * Extract ID from an entity object for relationship comparison
     * This is used specifically for @ManyToOne relationship fields
     * Handles Hibernate proxies and uninitialized entities
     */
    private Long extractEntityId(Object entity) {
        if (entity == null) {
            return null;
        }

        String entityClassName = entity.getClass().getName();
        log.info("Extracting ID from entity: {}", entityClassName);

        try {
            java.lang.reflect.Method getIdMethod = entity.getClass().getMethod("getId");
            if (getIdMethod != null) {
                Object idValue = getIdMethod.invoke(entity);
                if (idValue != null) {
                    log.info("Successfully extracted ID using getId() method: {}", idValue);
                    if (idValue instanceof Long) {
                        return (Long) idValue;
                    } else if (idValue instanceof Integer) {
                        return ((Integer) idValue).longValue();
                    } else {
                        return Long.valueOf(idValue.toString());
                    }
                }
            }
        } catch (Exception methodException) {
            log.info("getId() method failed: {}", methodException.getMessage());
        }

        log.warn("Could not extract ID from entity: {}", entityClassName);
        return null;
    }

    /**
     * Get module code based on entity class
     */
    private String getModuleCode(Class<?> entityClass) {
        // Convert class name to module code (e.g., Order -> ORDER)
        String className = entityClass.getSimpleName();
        return className.toUpperCase();
    }

    /**
     * Find the ID field in entity hierarchy
     */
    private Field findIdField(Class<?> clazz) {
        while (clazz != null && clazz != Object.class) {
            for (Field field : clazz.getDeclaredFields()) {
                if (field.getName().equals("id")) {
                    return field;
                }
            }
            clazz = clazz.getSuperclass();
        }
        return null;
    }

    /**
     * Get field to database column mapping for an entity class
     */
    private Map<String, String> getFieldToColumnMapping(Class<?> entityClass) {
        return fieldToColumnCache.computeIfAbsent(entityClass, clazz -> {
            log.info("🗺️ Building field-to-column mapping for entity: {}", clazz.getSimpleName());

            Map<String, String> mapping = new HashMap<>();
            List<Field> allFields = getAllFields(clazz);

            log.info("📋 Processing {} fields for mapping", allFields.size());

            for (Field field : allFields) {
                if (shouldSkipField(field)) {
                    log.info("⏭️ Skipping field '{}' from mapping", field.getName());
                    continue;
                }

                String columnName = getColumnName(field);
                if (columnName != null) {
                    mapping.put(field.getName(), columnName);
                    log.info("🔗 Mapped field '{}' → column '{}'", field.getName(), columnName);
                } else {
                    log.info("⚠️ No column name found for field '{}'", field.getName());
                }
            }

            log.info("✅ Field mapping completed for {}: {} mappings created", clazz.getSimpleName(), mapping.size());
            if (log.isTraceEnabled()) {
                mapping.forEach((field, column) ->
                    log.info("   {} → {}", field, column));
            }

            return mapping;
        });
    }

    /**
     * Get all fields from a class and its superclasses
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * Check if a field should be skipped during comparison
     */
    private boolean shouldSkipField(Field field) {
        String fieldName = field.getName();
        Class<?> fieldType = field.getType();

        // Skip static fields
        if (java.lang.reflect.Modifier.isStatic(field.getModifiers())) {
            log.trace("⏭️ Skipping static field: {}", fieldName);
            return true;
        }

        // Skip transient fields
        if (field.isAnnotationPresent(jakarta.persistence.Transient.class)) {
            log.trace("⏭️ Skipping @Transient field: {}", fieldName);
            return true;
        }

        // Skip ID fields
        if (field.isAnnotationPresent(jakarta.persistence.Id.class) || "id".equals(fieldName)) {
            log.trace("⏭️ Skipping ID field: {}", fieldName);
            return true;
        }

        // Skip Collection fields (List, Set, etc.)
        if (Collection.class.isAssignableFrom(fieldType)) {
            log.trace("⏭️ Skipping Collection field: {} (type: {})", fieldName, fieldType.getSimpleName());
            return true;
        }

        // Skip @OneToMany relationship fields
        if (field.isAnnotationPresent(OneToMany.class)) {
            log.trace("⏭️ Skipping @OneToMany field: {} (type: {})", fieldName, fieldType.getSimpleName());
            return true;
        }

        // Skip @ManyToMany relationship fields
        if (field.isAnnotationPresent(ManyToMany.class)) {
            log.trace("⏭️ Skipping @ManyToMany field: {} (type: {})", fieldName, fieldType.getSimpleName());
            return true;
        }

        // Skip @OneToOne relationship fields
        if (field.isAnnotationPresent(OneToOne.class)) {
            log.trace("⏭️ Skipping @OneToOne field: {} (type: {})", fieldName, fieldType.getSimpleName());
            return true;
        }

        // For @ManyToOne relationship fields, we want to track ID changes, not skip them
        // The comparison will be handled specially in compareEntitiesAndCreateHistory
        if (field.isAnnotationPresent(ManyToOne.class)) {
            log.trace("🔗 @ManyToOne field will be processed for ID comparison: {} (type: {})", fieldName, fieldType.getSimpleName());
            // Don't skip - we want to track relationship changes by comparing IDs
        }

        // Skip audit fields that are automatically managed
        if (fieldName.equals("createdAt") || fieldName.equals("lastModifiedAt") ||
            fieldName.equals("createdBy") || fieldName.equals("lastModifiedBy")) {
            log.trace("⏭️ Skipping audit field: {}", fieldName);
            return true;
        }

        // Skip common relationship field patterns
        if (fieldName.endsWith("List") || fieldName.endsWith("Set") || fieldName.endsWith("Collection")) {
            log.trace("⏭️ Skipping relationship collection field: {} (pattern match)", fieldName);
            return true;
        }

        log.trace("✅ Field will be processed: {} (type: {})", fieldName, fieldType.getSimpleName());
        return false;
    }

    /**
     * Get database column name from field annotation
     */
    private String getColumnName(Field field) {
        // Handle @ManyToOne relationships - look for @JoinColumn annotation
        if (field.isAnnotationPresent(ManyToOne.class)) {
            JoinColumn joinColumn = field.getAnnotation(JoinColumn.class);
            if (joinColumn != null && !joinColumn.name().isEmpty()) {
                log.trace("🔗 @ManyToOne field '{}' mapped to foreign key column: '{}'",
                         field.getName(), joinColumn.name());
                return joinColumn.name();
            } else {
                // Default foreign key naming convention: fieldName + "_id"
                String defaultFkColumn = convertToSnakeCase(field.getName()) + "_id";
                log.trace("🔗 @ManyToOne field '{}' using default foreign key column: '{}'",
                         field.getName(), defaultFkColumn);
                return defaultFkColumn;
            }
        }

        // Handle regular fields with @Column annotation
        Column columnAnnotation = field.getAnnotation(Column.class);
        if (columnAnnotation != null && !columnAnnotation.name().isEmpty()) {
            return columnAnnotation.name();
        }

        // If no @Column annotation or name is empty, convert field name to snake_case
        return convertToSnakeCase(field.getName());
    }

    /**
     * Convert camelCase to snake_case
     */
    private String convertToSnakeCase(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * Get field settings from repository with caching
     */
    private FieldSettings getFieldSettings(String moduleCode, String fieldName) {
        String language = "vi"; // Default language is vi, could be made configurable
        String cacheKey = moduleCode + ":" + fieldName + ":" + language;

        log.info("🔍 Looking up FieldSettings for {}.{} (language: {})", moduleCode, fieldName, language);

        return fieldSettingsCache.computeIfAbsent(cacheKey, key -> {
            try {
                log.info("📡 Querying database for FieldSettings: {}", key);
                FieldSettings result = fieldSettingsRepository.findByModuleCodeAndNameAndLanguage(moduleCode, fieldName, language)
                    .orElse(null);

                if (result != null) {
                    log.info("✅ Found FieldSettings for {}.{}: type={}, label='{}'",
                             moduleCode, fieldName, result.getFieldType(), result.getLabel());
                } else {
                    log.info("❌ No FieldSettings found for {}.{} (language: {})", moduleCode, fieldName, language);
                }

                return result;
            } catch (Exception e) {
                log.warn("⚠️ Error querying FieldSettings for module: {} field: {} language: {} - {}",
                        moduleCode, fieldName, language, e.getMessage());
                return null;
            }
        });
    }

    /**
     * Format value for storage in history
     */
    private String formatValue(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof Date) {
            return dateFormatter.format((Date) value);
        }

        return value.toString();
    }

    /**
     * Format value for logging purposes (more readable than storage format)
     */
    private String formatValueForLog(Object value) {
        if (value == null) {
            return "null";
        }

        if (value instanceof String) {
            String str = (String) value;
            if (str.isEmpty()) {
                return "\"\"";
            }
            return "\"" + str + "\"";
        }

        if (value instanceof Date) {
            return dateFormatter.format((Date) value);
        }

        if (value instanceof Number) {
            return value.toString();
        }

        // For other objects, show class name and toString
        return value.getClass().getSimpleName() + ":" + value.toString();
    }
}
