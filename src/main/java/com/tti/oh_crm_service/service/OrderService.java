package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.entity.*;

import java.util.List;

public interface OrderService {
    public Response<OrderTableResponse> getOrdersTable(String filters, String groupBy, String groupValue, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<OrderView> createOrder(CreateOrderRequest request, Long accountId, String language);
    public Response<OrderView> updateOrder(UpdateOrderRequest request, Long accountId, String language);
    public Response<Integer> deleteOrders(List<Long> ids);
    public Response<OrderDetails> getOrderDetails(Long orderId, Long accountId, String language);
    public Response<OrderKanbanResponse> getOrdersKanban(String filters, String groupBy, String search, Long accountId, String language);
    public Response<OrderSplitViewResponse> getOrdersSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language);
    public Response<Boolean> updateKanbanOrder(Long orderId, String fieldName, String fieldValue, Long accountId, String language);
    public List<OrderView> getOrdersByAccountId(Long accountId);
    public Response<GenericTableResponse<OrderProductView>> getProductsForOrder(Long orderId, int page, int limit, Long accountId, String language);
    public Response<List<OrderProductView>> addProductsToOrder(Long orderId, List<AddProductToOrderRequest> request, Long accountId, String language);
    public Response<OrderProductView> updateOrderProduct(Long orderId, UpdateOrderProductRequest request, Long accountId, String language);
    public Response<String> deleteOrderProducts(Long orderId, List<Long> orderProductIds, Long accountId, String language);
}
