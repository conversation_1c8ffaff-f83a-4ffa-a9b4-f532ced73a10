package com.tti.oh_crm_service.service;

import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapper.ContactMapper;
import com.tti.oh_crm_service.mapper.OpportunityMapper;
import com.tti.oh_crm_service.mapper.QuoteMapper;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.*;
import com.tti.oh_crm_service.repository.AccountRepository;
import com.tti.oh_crm_service.repository.ContactOpportunityRoleRepository;
import com.tti.oh_crm_service.repository.ContactRepository;
import com.tti.oh_crm_service.repository.UserRepository;
import com.tti.oh_crm_service.repository.QuoteRepository;
import com.tti.oh_crm_service.utils.EntityCloneUtils;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service("contactService")
@Slf4j
public class ContactServiceImpl implements ContactService {

    @Autowired
    private ContactMapper contactMapper;

    @Autowired
    private ContactRepository contactRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CoreQueryService<Contact> coreQueryService;
    
    @Autowired
    private ActivityService activityService;

    @Autowired
    private ContactOpportunityRoleRepository contactOpportunityRoleRepository;

    @Autowired
    private OpportunityMapper opportunityMapper;

    @Autowired
    private QuoteRepository quoteRepository;

    @Autowired
    private QuoteMapper quoteMapper;

    @Autowired
    private AccountRepository accountRepository;

    @Override
    public Response<ContactTableResponse> getContactsTable(
            String filters,
            String groupBy,
            String groupValue,
            String search,
            String sortBy,
            String sortDirection,
            int page,
            int limit,
            Long accountId,
            String language
    ) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "salutation",
                "first_name",
                "last_name",
                "title",
                "email",
                "phone",
                "mobile",
                "fax",
                "department",
                "birthday",
                "description",
                "street",
                "zip_postal_code"
        );

        GenericTableResponse<Contact> tableResponse = coreQueryService.getTable(
                filters, groupBy, groupValue, search, sortBy, sortDirection,
                page, limit, accountId, language, "contacts", searchFields, "CONTACT",
                contactRepository
        );

        if (tableResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }
        
        Page<Contact> contactsPage = tableResponse.getData();

        List<ContactView> contactViews = contactMapper.toContactView(contactsPage.getContent());
        
        ContactTableResponse contactTableResponse = new ContactTableResponse();
        contactTableResponse.setGroupItems(tableResponse.getGroupItems());
        contactTableResponse.setContacts(new PageImpl<>(contactViews, contactsPage.getPageable(), contactsPage.getTotalElements()));
        return new Response<ContactTableResponse>(StatusCode.OK, "Get contacts table success!!!", contactTableResponse);
    }

    @Override
    @Transactional
    public Response<ContactView> createContact(CreateContactRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<User> optOwner = userRepository.findById(request.getOwnerId());
        if (optOwner.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Owner not found!!!");
        }

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Contact CREATE");

        Contact contact = new Contact();
        contactMapper.create(request, contact, new UserMapStructContext(optUser.get(), optOwner.get()));

        // Save contact - HistoryEntityListener will automatically handle CREATE history
        contactRepository.save(contact);

        ContactView contactView = contactMapper.toContactView(contact);
        return new Response<>(StatusCode.OK, "Create contact success!!!", contactView);
    }

    @Override
    @Transactional
    public Response<ContactView> updateContact(UpdateContactRequest request, Long accountId, String language) {
        Optional<User> optUser = userRepository.findByAccountId(accountId);
        if (optUser.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "User not found!!!");
        }
        Optional<Contact> optContact = contactRepository.findById(request.getId());
        if (optContact.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact not found!!!");
        }

        Contact contact = optContact.get();

        // Set user context for EntityListener
        UserContext.setCurrentUser(optUser.get());

        // Step 1: Clone the original entity for history tracking before any modifications
        Contact originalContact = EntityCloneUtils.cloneEntityForHistory(contact);
        EntityContext.setOriginalEntity(originalContact);

        // Register a transaction synchronization to clear context after commit
        TransactionUtils.registerContextCleanupAfterTransaction("Contact UPDATE");

        // Step 2: Apply updates to the entity
        contactMapper.update(request, contact, optUser.get());

        // Handle relationship updates (if any)
        updateContactRelationships(request, contact);

        // Step 3: set the updated entity state after modifications
        EntityContext.setUpdatedEntity(contact);

        // Save contact - HistoryEntityListener will automatically handle UPDATE history with field changes
        contactRepository.save(contact);

        ContactView contactView = contactMapper.toContactView(contact);
        return new Response<>(StatusCode.OK, "Update contact success!!!", contactView);
    }

    @Override
    public Response<Integer> deleteContacts(List<Long> ids) {
        // Check contacts exist
        for (Long id : ids) {
            Optional<Contact> optContact = contactRepository.findById(id);
            if (optContact.isEmpty()) {
                return new Response<Integer>(StatusCode.BAD_REQUEST, "Contact not found!!!");
            }
        }
        Integer result = contactRepository.deleteByIdIn(ids);
        return new Response<Integer>(StatusCode.OK, "Delete leads success!!!", result);
    }

    @Override
    public Response<ContactDetails> getContactDetails(Long contactId, Long accountId, String language) {
        Optional<Contact> optContact = contactRepository.findById(contactId);
        if (optContact.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact not found!!!");
        }
        ContactDetails contactDetails = contactMapper.toContactDetails(optContact.get());
        
        // Get attached activities
        List<AttachedActivityShortView> attachedActivities = activityService.getAttachedActivitiesByModuleAndType(
                contactId, "CONTACT", null, accountId, language
        );
        contactDetails.setAttachedActivities(attachedActivities);

        // Get related opportunities count and top 2
        contactDetails.setCountRelatedOpportunities(contactOpportunityRoleRepository.countByContactId(contactId));
        Pageable topTwoOpportunities = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "lastModifiedAt"));
        List<ContactOpportunityRole> roles = contactOpportunityRoleRepository.findByContactIdWithOpportunity(contactId, topTwoOpportunities);
        contactDetails.setAttachedOpportunities(
            roles.stream()
                 .map(ContactOpportunityRole::getOpportunity)
                 .map(opportunityMapper::toOpportunityAttachedView) 
                 .toList()
        );

        // Get related quotes count and top 2
        contactDetails.setCountRelatedQuotes(quoteRepository.countByContactId(contactId));
        Pageable topTwoQuotesPageable = PageRequest.of(0, 2, Sort.by(Sort.Direction.DESC, "lastModifiedAt"));
        Page<Quote> topTwoQuotesPage = quoteRepository.findByContactIdOrderByLastModifiedAtDesc(contactId, topTwoQuotesPageable);
        contactDetails.setAttachedQuotes(quoteMapper.toQuoteAttachedView(topTwoQuotesPage.getContent()));

        return new Response<>(StatusCode.OK, "Get contact details success!!!", contactDetails);
    }

    @Override
    public Response<ContactKanbanResponse> getContactsKanban(String filters, String groupBy, String search, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = Arrays.asList(
                "salutation",
                "first_name",
                "last_name",
                "title",
                "email",
                "phone",
                "mobile",
                "fax",
                "department",
                "birthday",
                "description",
                "street",
                "zip_postal_code"
        );
        
        List<String> viewFields = Arrays.asList(
                "id",
                "salutation",
                "first_name",
                "last_name",
                "title",
                "phone",
                "mobile",
                "department"
        );

        GenericKanbanResponse kanbanResponse = coreQueryService.getKanban(
                filters, groupBy, search, accountId, language, "contacts", searchFields, viewFields, "CONTACT", contactRepository
        );

        if (kanbanResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<ContactKanbanView> contactKanbanViews = new ArrayList<>();
        for (Object[] contactObject : kanbanResponse.getItemObjects()) {
            ContactKanbanView contactKanbanView = new ContactKanbanView();
            contactKanbanView.setId((Long) contactObject[0]);
            contactKanbanView.setSalutation((String) contactObject[1]);
            contactKanbanView.setFirstName((String) contactObject[2]);
            contactKanbanView.setLastName((String) contactObject[3]);
            contactKanbanView.setTitle((String) contactObject[4]);
            contactKanbanView.setPhone((String) contactObject[5]);
            contactKanbanView.setMobile((String) contactObject[6]);
            contactKanbanView.setDepartment((String) contactObject[7]);
            contactKanbanView.setGroupField((String) contactObject[8]);
            contactKanbanViews.add(contactKanbanView);
        }

        ContactKanbanResponse contactKanbanResponse = new ContactKanbanResponse();
        contactKanbanResponse.setGroupItems(kanbanResponse.getGroupItems());
        contactKanbanResponse.setContacts(contactKanbanViews);
        return new Response<>(StatusCode.OK, "Get contacts kanban success!!!", contactKanbanResponse);
    }

    @Override
    public Response<ContactSplitViewResponse> getContactsSplitView(String filters, String search, String sortBy, String sortDirection, int page, int limit, Long accountId, String language) {
        // Define search fields
        List<String> searchFields = List.of(
                "salutation",
                "first_name",
                "last_name",
                "title",
                "email",
                "phone",
                "mobile",
                "fax",
                "department",
                "birthday",
                "description",
                "street",
                "zip_postal_code"
        );

        // Split view projection
        List<String> viewFields = List.of(
                "id",
                "salutation",
                "first_name",
                "last_name",
                "title",
                "phone",
                "mobile",
                "department"
        );

        GenericSplitViewResponse<Object[]> genericResponse = coreQueryService.getSplitView(
                filters, search, sortBy, sortDirection, page, limit,
                "contacts", searchFields, viewFields, contactRepository
        );

        if (genericResponse == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Params are incorrect!!!");
        }

        List<ContactSplitView> contacts = new ArrayList<>();
        for (Object[] contactObject : genericResponse.getData().getContent()) {
            ContactSplitView contactSplitView = new ContactSplitView();
            contactSplitView.setId((Long) contactObject[0]);
            contactSplitView.setSalutation((String) contactObject[1]);
            contactSplitView.setFirstName((String) contactObject[2]);
            contactSplitView.setLastName((String) contactObject[3]);
            contactSplitView.setTitle((String) contactObject[4]);
            contactSplitView.setPhone((String) contactObject[5]);
            contactSplitView.setMobile((String) contactObject[6]);
            contactSplitView.setDepartment((String) contactObject[7]);
            contacts.add(contactSplitView);
        }

        Page<ContactSplitView> contactsPage = new PageImpl<>(
                contacts, 
                genericResponse.getData().getPageable(), 
                genericResponse.getData().getTotalElements()
        );
        ContactSplitViewResponse contactSplitViewResponse = new ContactSplitViewResponse();
        contactSplitViewResponse.setContacts(contactsPage);
        return new Response<>(StatusCode.OK, "Get contacts split view success!!!", contactSplitViewResponse);
    }

    @Override
    public Response<Boolean> updateKanbanContact(Long contactId, String fieldName, String fieldValue, Long accountId, String language) {
        JSONObject jsonFilter = new JSONObject();
        jsonFilter.put("table_name", "contacts");
        jsonFilter.put("id", contactId);
        jsonFilter.put("field_name", fieldName);
        jsonFilter.put("field_value", fieldValue);

        contactRepository.updateKanbanData(jsonFilter.toString());

        return new Response<>(StatusCode.OK, "Update contact kanban success!!!", true);
    }

    @Override
    public Response<GenericTableResponse<OpportunityContactRoleView>> getAttachedOpportunityForContact(Long contactId, int page, int limit, Long accountId, String language) {
        // check if contact exists
        if (contactId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact ID is null!!!");
        }
        Optional<Contact> optContact = contactRepository.findById(contactId);
        if (optContact.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        List<ContactOpportunityRole> attachedOpportunites = contactOpportunityRoleRepository.findByContactIdWithOpportunity(contactId, pageable);
        
        long totalCount = contactOpportunityRoleRepository.countByContactId(contactId);

        List<OpportunityContactRoleView> attachedOpportunityViews = opportunityMapper.toOpportunityContactRoleView(attachedOpportunites, true);
        Page<OpportunityContactRoleView> contactRolePage = new PageImpl<>(attachedOpportunityViews, pageable, totalCount);

        GenericTableResponse<OpportunityContactRoleView> responseData = new GenericTableResponse<>(contactRolePage, null);

        return new Response<>(StatusCode.OK, "Get attached opportunites for contact success!!!", responseData);
    }

    @Override
    public Response<GenericTableResponse<QuoteAttachedView>> getAttachedQuoteForContact(Long contactId, int page, int limit, Long accountId, String language) {
        // check if contact exists
        if (contactId == null) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact ID is null!!!");
        }
        Optional<Contact> optContact = contactRepository.findById(contactId);
        if (optContact.isEmpty()) {
            return new Response<>(StatusCode.BAD_REQUEST, "Contact not found!!!");
        }

        Pageable pageable = PageRequest.of(page, limit);
        Page<Quote> attachedQuotes = quoteRepository.findByContactIdOrderByLastModifiedAtDesc(contactId, pageable);
        Page<QuoteAttachedView> quotePageViews = attachedQuotes.map(quote -> quoteMapper.toQuoteAttachedView(quote));
        return new Response<>(StatusCode.OK, "Get attached quotes for contact success!!!", new GenericTableResponse<>(quotePageViews, null));
    }

    @Override
    public List<ContactView> getContactsByAccountId(Long accountId) {
        List<Contact> contacts = contactRepository.findByAccountId(accountId);
        return contactMapper.toContactView(contacts);
    }

    @Override
    public Contact findById(Long id) {
        Optional<Contact> optContact = contactRepository.findById(id);
        return optContact.orElse(null);
    }

    /**
     * Update contact relationships based on the provided IDs in the request
     */
    private void updateContactRelationships(UpdateContactRequest request, Contact contact) {
        if (request.getAccountId() != null) {
            Optional<Account> optAccount = accountRepository.findById(request.getAccountId());
            if (optAccount.isPresent()) {
                contact.setAccount(optAccount.get());
            }
        }
    }
}