package com.tti.oh_crm_service.model;

import java.util.Date;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "activity")
@EntityListeners({HistoryEntityListener.class})
@Data
@EqualsAndHashCode(callSuper=false)
public class Activity extends AuditableBaseEntity {
    @Column(name = "type", columnDefinition = "VARCHAR(100) default ''")
    private String type;

    @Column(name = "title", columnDefinition = "VARCHAR(1000) default ''")
    private String title;

    @Column(name = "task_type", columnDefinition = "VARCHAR(100) default ''")
    private String taskType;

    @Column(name = "description", columnDefinition = "TEXT(2000)")
    private String description;

    // value must be a module code from the list of modules in modules table
    // other modules except LEAD, CONTACT
    @Column(name = "related_to_module", columnDefinition = "VARCHAR(50) default ''")
    private String relatedToModule;

    @Column(name = "related_to_id")
    private Long relatedToId;

    @Column(name = "deadline", columnDefinition = "DATETIME")
    private Date deadline;

    @Column(name = "priority", columnDefinition = "VARCHAR(100) default ''")
    private String priority;

    @Column(name = "status", columnDefinition = "VARCHAR(100) default ''")
    private String status;

    @Column(name = "place", columnDefinition = "VARCHAR(1000) default ''")
    private String place;

    @Column(name = "start_time", columnDefinition = "DATETIME")
    private Date startTime;

    @Column(name = "end_time", columnDefinition = "DATETIME")
    private Date endTime;

    @Column(name = "phone", columnDefinition = "VARCHAR(20) default ''")
    private String phone;

    @Column(name = "call_time", columnDefinition = "INTEGER")
    private Integer callTime;

    @Column(name = "call_type", columnDefinition = "VARCHAR(100) default ''")
    private String callType;

    @Column(name = "call_result", columnDefinition = "TEXT(2000)")
    private String callResult;
}
