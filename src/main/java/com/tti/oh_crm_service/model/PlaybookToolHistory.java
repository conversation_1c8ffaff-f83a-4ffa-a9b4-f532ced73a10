package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;
import java.util.List;

@Entity
@Table(name = "playbook_tool_histories")
@Data
public class PlaybookToolHistory extends AuditableBaseEntity {
    @Column(name = "action_type", columnDefinition = "ENUM('CREATE', 'UPDATE', 'DELETE', 'READ')")
    @Enumerated(EnumType.STRING)
    private EHistoryActionType actionType;
    @Column(name = "field_name")
    private String fieldName;
    @Column(name = "original_value", columnDefinition = "LONGTEXT")
    private String originalValue;
    @Column(name = "new_value", columnDefinition = "LONGTEXT")
    private String newValue;
    @CreationTimestamp
    @Column(name = "history_date", nullable = false, updatable = false)
    private Date historyDate;
    // description is a formatted string such as "{{user}} created playbook tool {{playbookTool.name}}"
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "playbook_tool_section_id")
    private PlaybookToolSection playbookToolSection;
    @ManyToOne(fetch = FetchType.LAZY)
    private PlaybookTool playbookTool;
    @ManyToOne(fetch = FetchType.LAZY)
    private User user;
    
    
    
    
}
