package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "campaigns")
@EntityListeners({HistoryEntityListener.class})
@Data
public class Campaign {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;

    @Column(name = "active", nullable = false, columnDefinition = "boolean default true")
    private Boolean active;

    @Column(name = "status", nullable = false, columnDefinition = "VARCHAR(100) default ''")
    private String status = "";

    @ManyToOne(fetch = FetchType.LAZY)
    private Campaign campaignParent;

    @Column(name = "start_date")
    private Date startDate;

    @Column(name = "end_date")
    private Date endDate;

    @Column(name = "type", nullable = false, columnDefinition = "VARCHAR(100) default ''")
    private String type = "";

    @Column(name = "description", nullable = true, columnDefinition = "TEXT(2000)")
    private String description;

    @Column(name = "number_of_sent", nullable = false, columnDefinition = "INTEGER default 0")
    private Integer numberOfSent = 0;

    @Column(name = "expected_response", nullable = false, columnDefinition = "INTEGER default 0")
    private Integer expectedResponse = 0;

    @Column(name = "budget_cost", nullable = false, columnDefinition = "INTEGER default 0")
    private Integer budgetCost = 0;

    @Column(name = "actual_cost", nullable = false, columnDefinition = "INTEGER default 0")
    private Integer actualCost = 0;

    @Column(name = "expected_revenue", nullable = false, columnDefinition = "INTEGER default 0")
    private Integer expectedRevenue = 0;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
}
