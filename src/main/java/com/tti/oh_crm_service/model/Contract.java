package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

@Data
@Table(name = "contracts")
@EntityListeners({HistoryEntityListener.class})
@Entity
public class Contract {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;
    @Column(name = "contract_number", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String contractNumber = "";
    @Column(name = "start_date", nullable = false)
    private Date startDate;
    @Column(name = "end_date") 
    private Date endDate;
    @Column(name = "duration_by_month", columnDefinition = "INT default 0")
    private Integer durationByMonth;
    @Column(name = "status", columnDefinition = "VARCHAR(255) default ''")
    private String status = "";
    @Column(name = "notification_days_before_end", columnDefinition = "INT default 0")
    private Integer notificationDaysBeforeEnd = 0;
    @Column(name = "shipping_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingCountryCode = "";
    @Column(name = "shipping_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingProvinceCode = "";
    @Column(name = "shipping_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String shippingDistrictCode = "";
    @Column(name = "shipping_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String shippingWardCode = "";
    @Column(name = "shipping_street", columnDefinition = "VARCHAR(255) default ''")
    private String shippingStreet = "";
    @Column(name = "shipping_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String shippingZipPostalCode = "";
    @Column(name = "billing_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingCountryCode = "";
    @Column(name = "billing_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingProvinceCode = "";
    @Column(name = "billing_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String billingDistrictCode = "";
    @Column(name = "billing_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String billingWardCode = "";
    @Column(name = "billing_street", columnDefinition = "VARCHAR(255) default ''")
    private String billingStreet = "";
    @Column(name = "billing_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String zipPostalCode = "";
    @Column(name = "special_terms", columnDefinition = "TEXT")
    private String specialTerms = "";
    @Column(name = "description", columnDefinition = "TEXT")
    private String description = "";
    // column "Nội dung khách hàng ký"
    @Column(name = "signed_content", columnDefinition = "VARCHAR(255) default ''")
    private String signedContent = "";
    @Column(name = "signed_date")
    private Date signedDate;
    // column "Ngày công ty ký"
    @Column(name = "user_signed_date")
    private Date userSignedDate;
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;
    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;
    // column "Được ký bởi Liên hệ"
    @ManyToOne(fetch = FetchType.LAZY)
    private Contact signedByContact;
    // column "Được công ty ký bởi"
    @ManyToOne(fetch = FetchType.LAZY)
    private User signedByUser;
    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;
    @ManyToOne(fetch = FetchType.LAZY)
    private Account account;
    
//    @ManyToOne(fetch = FetchType.LAZY)
//    private PriceQuote priceQuote;
}
