package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "opportunity_stage_settings")
@Data
public class OpportunityStageSettings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "opportunity_stage_value", nullable = false, unique = true)
    private String opportunityStageValue;

    @Column(name = "win_rate", nullable = false, columnDefinition = "integer default 0")
    private Integer winRate = 0;

    @Column(name = "forecast_type", nullable = false, columnDefinition = "ENUM('OPEN', 'END')")
    private String forecastType = "OPEN";

    @Column(name = "forecast_category", nullable = false, columnDefinition = "ENUM('LOW', 'IN_PROGRESS', 'HIGH', 'WIN', 'LOSE')")
    private String forecastCategory = "LOW";

    @Column(name = "color", nullable = false, columnDefinition = "VARCHAR(10) default ''")
    private String color = "";

    @Column(name = "key_fields", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String keyFields = "";

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
}
