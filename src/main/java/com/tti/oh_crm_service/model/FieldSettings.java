package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHintType;
import com.tti.oh_crm_service.enumeration.EMeasurementType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

@Entity
@Table(name = "field_settings", uniqueConstraints={@UniqueConstraint(columnNames = {"module_code", "name", "language"})})
@Data
public class FieldSettings {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "module_code", nullable = false)
    private String moduleCode;

    @Column(name = "group_code", nullable = false)
    private String groupCode;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "label")
    private String label;

    @Column(name = "hint")
    private String hint;

    @Column(name = "hint_type", columnDefinition = "ENUM('TOOLTIP', 'PLACEHOLDER')")
    @Enumerated(EnumType.STRING)
    private EHintType hintType;

    @Column(name = "field_type", columnDefinition = "ENUM('ONE_LINE', 'MULTIPLE_LINES', 'DATE', 'DATE_TIME', 'LIST', 'CHOICE', 'MULTIPLE_CHOICES', 'INTEGER', 'DECIMAL', 'LINK', 'CURRENCY', 'PHONE_NUMBER', 'EMAIL', 'LOOKUP', 'MEASUREMENT')")
    @Enumerated(EnumType.STRING)
    private EFieldType fieldType;

    @Column(name = "list_code")
    private String listCode;

    @Column(name = "list_custom")
    private Boolean listCustom;

    @Column(name = "length")
    private Integer length;

    @Column(name = "active", columnDefinition = "boolean default true")
    private Boolean active;

    @Column(name = "edit_options")
    private String editOptions;

    @Column(name = "required", columnDefinition = "boolean default false")
    private Boolean required;

    @Column(name = "check_zero", columnDefinition = "boolean default false")
    private Boolean checkZero;

    @Column(name = "column_span", columnDefinition = "integer default 1")
    private Integer columnSpan;

    @Column(name = "show_options")
    private String showOptions;

    @Column(name = "check_duplicated", columnDefinition = "boolean default false")
    private Boolean checkDuplicated;

    @Column(name = "custom", columnDefinition = "boolean default false")
    private Boolean custom;

    @Column(name = "language")
    private String language;

    @Column(name = "position", columnDefinition = "integer default 0")
    private Integer position;

    @Column(name = "choice_default", columnDefinition = "boolean default false")
    private Boolean choiceDefault;

    @Column(name = "only_number", columnDefinition = "boolean default false")
    private Boolean onlyNumber;

    @Column(name = "sort_by_alphaBet", columnDefinition = "boolean default false")
    private Boolean sortByAlphaBet;

    @Column(name = "measurement_type")
    private EMeasurementType measurementType;

    @Column(name = "measurement_level")
    private Integer measurementLevel;

    @Column(name = "measurement_base_unit")
    private String measurementBaseUnit;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
}