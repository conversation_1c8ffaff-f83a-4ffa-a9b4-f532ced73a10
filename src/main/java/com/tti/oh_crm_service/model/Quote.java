package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Table(name = "quotes")
@EntityListeners({HistoryEntityListener.class})
@Entity
public class Quote extends AuditableBaseEntity {
    @Column(name = "name", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String name = "";
    @Column(name = "code", columnDefinition = "VARCHAR(255) default ''")
    private String code = "";
    @Column(name = "status", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String status = "";
    @Column(name = "due_date")
    private Date dueDate;
    @Column(name = "description", columnDefinition = "TEXT")
    private String description = "";
    // chiết khấu
    @Column(name = "discount", columnDefinition = "FLOAT default 0")
    private Float discount = 0f;
    @Column(name = "tax_cost", columnDefinition = "INT default 0")
    private Integer taxCost = 0;
    // phí Ship
    @Column(name = "shipping_cost", columnDefinition = "INT default 0")
    private Integer shippingCost = 0;
    // thành tiền
    @Column(name = "total_cost", columnDefinition = "INT default 0")
    private Integer totalCost = 0;
    @Column(name = "contact_phone", columnDefinition = "VARCHAR(20) default ''")
    private String contactPhone = "";
    @Column(name = "contact_email", columnDefinition = "VARCHAR(50) default ''")
    private String contactEmail = "";
    @Column(name = "contact_fax", columnDefinition = "VARCHAR(50) default ''")
    private String contactFax = "";
    @Column(name = "shipping_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingCountryCode = "";
    @Column(name = "shipping_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingProvinceCode = "";
    @Column(name = "shipping_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String shippingDistrictCode = "";
    @Column(name = "shipping_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String shippingWardCode = "";
    @Column(name = "shipping_street", columnDefinition = "VARCHAR(255) default ''")
    private String shippingStreet = "";
    @Column(name = "shipping_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String shippingZipPostalCode = "";
    @Column(name = "billing_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingCountryCode = "";
    @Column(name = "billing_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingProvinceCode = "";
    @Column(name = "billing_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String billingDistrictCode = "";
    @Column(name = "billing_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String billingWardCode = "";
    @Column(name = "billing_street", columnDefinition = "VARCHAR(255) default ''")
    private String billingStreet = "";
    @Column(name = "billing_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String billingZipPostalCode = "";
    @ManyToOne(fetch = FetchType.LAZY)
    private Contact contact;
    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    private Opportunity opportunity;
    @ManyToOne(fetch = FetchType.LAZY)
    private Account account;
}
