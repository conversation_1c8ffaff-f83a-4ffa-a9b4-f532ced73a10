package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import lombok.Data;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

@Entity
@Table(name = "leads")
@EntityListeners({HistoryEntityListener.class})
@Data
public class Lead {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "salutation", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String salutation = "";

    @Column(name = "first_name", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String firstName = "";

    @Column(name = "last_name", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String lastName = "";

    @Column(name = "title", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String title = "";

    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;

    @Column(name = "description", nullable = true, columnDefinition = "TEXT(2000)")
    private String description = "";

    @Column(name = "status", nullable = true, columnDefinition = "VARCHAR(100) default ''")
    private String status = "";

    @Column(name = "email", nullable = true, columnDefinition = "VARCHAR(50) default ''")
    private String email = "";
    
    @Column(name = "phone", nullable = true, columnDefinition = "VARCHAR(20) default ''")
    private String phone = "";

    @Column(name = "mobile", nullable = true, columnDefinition = "VARCHAR(20) default ''")
    private String mobile = "";

    @Column(name = "rating", nullable = true, columnDefinition = "VARCHAR(100) default ''")
    private String rating = "";

    @Column(name = "company", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String company = "";

    @Column(name = "website", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String website = "";

    @Column(name = "industry", nullable = true, columnDefinition = "VARCHAR(100) default ''")
    private String industry = "";

    @Column(name = "no_of_employees", nullable = true, columnDefinition = "INT default 0")
    private Integer noOfEmployees = 0;

    @Column(name = "source", nullable = true, columnDefinition = "VARCHAR(100) default ''")
    private String source = "";

    // tags format: <tag1>,<tag2>
    @Column(name = "tags", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String tags = "";

    @Column(name = "country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String countryCode = "";

    @Column(name = "province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String provinceCode = "";

    @Column(name = "district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String districtCode = "";

    @Column(name = "ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String wardCode = "";

    @Column(name = "street", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String street = "";

    @Column(name = "zip_postal_code", nullable = true, columnDefinition = "VARCHAR(20) default ''")
    private String zipPostalCode = "";

    @Column(name = "full_address", nullable = true, columnDefinition = "VARCHAR(255) default ''")
    private String fullAddress = "";

    @CreationTimestamp
    @Column(name = "created_at", nullable = true, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = true)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
}