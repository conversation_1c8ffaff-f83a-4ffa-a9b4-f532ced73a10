package com.tti.oh_crm_service.model;

import java.io.Serializable;
import java.util.Objects;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Embeddable
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrganizationLevelLabelId implements Serializable {
    
    @Column(name = "level", nullable = false)
    private Integer level;
    
    @Column(name = "language", nullable = false)
    private String language;
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrganizationLevelLabelId that = (OrganizationLevelLabelId) o;
        return Objects.equals(level, that.level) && Objects.equals(language, that.language);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(level, language);
    }
}
