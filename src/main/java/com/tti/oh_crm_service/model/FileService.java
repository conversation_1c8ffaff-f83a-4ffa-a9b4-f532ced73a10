package com.tti.oh_crm_service.model;

import java.io.IOException;

import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;

import reactor.core.publisher.Mono;

@Service
public class FileService {
        public Mono<Workbook> getExcelFileAsWorkbook(FilePart file) {
        return DataBufferUtils.join(file.content())
          .map(data ->  {
            Workbook workbook;
            try {
                workbook = WorkbookFactory.create(data.asInputStream());
            } catch (EncryptedDocumentException e) {
                e.printStackTrace();
                return null;
            } catch (IOException e) {
                e.printStackTrace();
                return null;
            }
            return workbook;
          });
    }
}
