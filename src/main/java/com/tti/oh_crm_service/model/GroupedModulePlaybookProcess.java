package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.enumeration.EPlaybookStatus;
import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Table(name = "grouped_module_playbook_processes")
@Data
public class GroupedModulePlaybookProcess extends AuditableBaseEntity {
    // Now only available for LEAD and OPPORTUNITY
    @Column(name = "module_code", nullable = false, unique = true)
    private String moduleCode;
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;
    @Column(name = "status", nullable = false, columnDefinition = "ENUM('DRAFT', 'PUBLISHED')")
    @Enumerated(EnumType.STRING)
    private EPlaybookStatus status;

    
}
