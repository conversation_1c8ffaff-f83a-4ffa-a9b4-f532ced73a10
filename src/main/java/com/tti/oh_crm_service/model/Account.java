package com.tti.oh_crm_service.model;

import java.util.Date;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import lombok.Data;

@Entity
@Table(name = "accounts")
@EntityListeners({HistoryEntityListener.class})
@Data
public class Account {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;
   
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", columnDefinition = "TEXT(2000)")
    private String description = "";

    @Column(name = "mobile", columnDefinition = "VARCHAR(20) default ''")
    private String mobile;

    @Column(name = "email", columnDefinition = "VARCHAR(50) default ''")
    private String email = "";

    @Column(name = "account_parent_id")
    private Long accountParentId;

    @Column(name = "type", columnDefinition = "VARCHAR(100) default ''")
    private String type = "";

    @Column(name = "industry", columnDefinition = "VARCHAR(100) default ''")
    private String industry = "";

    @Column(name = "website", columnDefinition = "VARCHAR(255) default ''")
    private String website = "";

    @Column(name = "phone", columnDefinition = "VARCHAR(20) default ''")
    private String phone = "";

    @Column(name = "no_of_employees", nullable = true, columnDefinition = "INT default 0")
    private Integer noOfEmployees = 0;

    @Column(name = "billing_country_code", length = 2, columnDefinition = "CHAR(10) default ''")
    private String billingCountryCode = "";

    @Column(name = "billing_province_code", length = 2, columnDefinition = "CHAR(10) default ''")
    private String billingProvinceCode = "";

    @Column(name = "billing_district_code", length = 3, columnDefinition = "CHAR(10) default ''")
    private String billingDistrictCode = "";

    @Column(name = "billing_ward_code", length = 5, columnDefinition = "CHAR(10) default ''")
    private String billingWardCode = "";

    @Column(name = "billing_street", columnDefinition = "VARCHAR(255) default ''")
    private String billingStreet = "";

    @Column(name = "billing_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String billingZipPostalCode = "";

    @Column(name = "shipping_country_code", length = 2, columnDefinition = "CHAR(10) default ''")
    private String shippingCountryCode = "";

    @Column(name = "shipping_province_code", length = 2, columnDefinition = "CHAR(10) default ''")
    private String shippingProvinceCode = "";

    @Column(name = "shipping_district_code", length = 3, columnDefinition = "CHAR(10) default ''")
    private String shippingDistrictCode = "";

    @Column(name = "shipping_ward_code", length = 5, columnDefinition = "CHAR(10) default ''")
    private String shippingWardCode = "";

    @Column(name = "shipping_street", columnDefinition = "VARCHAR(255) default ''")
    private String shippingStreet = "";

    @Column(name = "shipping_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String shippingZipPostalCode = "";

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", referencedColumnName = "id", nullable = false)
    private User owner;

}
