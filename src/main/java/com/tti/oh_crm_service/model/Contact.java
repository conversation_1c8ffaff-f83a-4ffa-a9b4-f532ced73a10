package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;
import java.util.Set;

@Data
@Table(name = "contacts")
@EntityListeners({HistoryEntityListener.class})
@Entity
public class Contact {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;
    @Column(name = "salutation", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String salutation = "";
    @Column(name = "title", columnDefinition = "VARCHAR(255) default ''")
    private String title = "";
    @Column(name = "first_name", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String firstName = "";
    @Column(name = "last_name", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String lastName = "";
    @Column(name = "description", columnDefinition = "TEXT")
    private String description = "";
    @Column(name = "email", columnDefinition = "VARCHAR(50) default ''")
    private String email;
    @Column(name = "phone", columnDefinition = "VARCHAR(20) default ''")
    private String phone;
    @Column(name = "mobile", columnDefinition = "VARCHAR(20) default ''")
    private String mobile;
    @Column(name = "fax", columnDefinition = "VARCHAR(20) default ''")
    private String fax;
    @Column(name = "department", columnDefinition = "VARCHAR(255) default ''")
    private String department;
    @Column(name = "birthday")
    private Date birthday;
    @Column(name = "country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String countryCode = "";
    @Column(name = "province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String provinceCode = "";
    @Column(name = "district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String districtCode = "";
    @Column(name = "ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String wardCode = "";
    @Column(name = "street", columnDefinition = "VARCHAR(255) default ''")
    private String street = "";
    @Column(name = "zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String zipPostalCode = "";
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;
    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;
    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;
    @ManyToOne(fetch = FetchType.LAZY)
    private Account account;

    @OneToMany(mappedBy = "contact", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ContactOpportunityRole> opportunityRoles;
}
