package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.enumeration.EPermission;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;

@Entity
@Table(name = "profile_permission_module_link", uniqueConstraints={@UniqueConstraint(columnNames = {"profile_id", "permission", "module_code"})})
@Data
public class ProfilePermissionModuleLink {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "profile_id", nullable = false)
    private Long profileId;

    @Column(name = "permission", nullable = false, columnDefinition = "ENUM('VIEW', 'CREATE', 'EDIT', 'DELETE', 'IMPORT', 'EXPORT', 'SEND_EMAIL', 'MASS_EMAIL', 'DELETE_EMAIL', 'TEMPLATE_MANAGEMENT', 'MASS_UPDATE', 'MASS_DELETE', 'CHANGE_OWNER', 'MASS_TRANSFER', 'CONVERT', 'PRINT_VIEW', 'USER_MANAGEMENT', 'ROLE_MANAGEMENT', 'PROFILE_MANAGEMENT', 'MODULE_CUSTOMIZATION', 'CURRENCY_MANAGEMENT')")
    @Enumerated(EnumType.STRING)
    private EPermission permission;

    @Column(name = "module_code", nullable = true)
    private String moduleCode;
}
