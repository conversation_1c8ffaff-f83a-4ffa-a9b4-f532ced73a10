package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import lombok.Data;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

@Entity
@Table(name = "departments")
@Data
public class Department extends AuditableBaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;
    @Column(name = "name", nullable = false)
    private String name;
    @Column(name = "code", nullable = false, unique = true)
    private String code;
    @ManyToOne(fetch = FetchType.LAZY)
    private Department parentDepartment;
    // department head
    @ManyToOne(fetch = FetchType.LAZY)
    private User departmentHead;
    // debt limit
    @Column(name = "debt_limit")
    private Double debtLimit;
    // số thứ tự của phòng ban trong tổ chức
    @Column(name = "order_number")
    private Integer orderNumber;
    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;
    @Column(name = "description")
    private String description;
    @Column(name = "organization_level", nullable = false)
    private Integer organizationLevel;
}
