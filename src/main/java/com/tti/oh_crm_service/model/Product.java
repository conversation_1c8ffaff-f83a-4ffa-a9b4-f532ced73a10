package com.tti.oh_crm_service.model;

import java.util.Date;
import java.util.List;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.FetchType;
import jakarta.persistence.Table;
import jakarta.persistence.CascadeType;
import lombok.Data;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

@Entity
@Table(name = "products")
@EntityListeners({HistoryEntityListener.class})
@Data
public class Product {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "code", nullable = false)
    private String code;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "series")
    private String series;

    @Column(name = "batch_id")
    private String batchId;

    @Column(name = "expiry_date")
    private Date expiryDate;

    @Column(name = "min_inventory")
    private Integer minInventory;

    @Column(name = "machine_id")
    private String machineId;

    @Column(name = "follow_sku")
    private Boolean followSku;

    @Column(name = "description", nullable = true, columnDefinition = "TEXT(2000)")
    private String description;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "category")
    private String category;

    @Column(name = "manufacturer")
    private String manufacturer;
    
    @Column(name = "price_buy")
    private Long priceBuy;

    @Column(name = "price_sale")
    private Long priceSale;

    @Column(name = "price_sale1")
    private Long priceSale1;

    @Column(name = "price_sale2")
    private Long priceSale2;

    @Column(name = "price_sale3")
    private Long priceSale3;

    @Column(name = "fixed_price_sale")
    private Long fixedPriceSale;

    @Column(name = "color")
    private String color;

    @Column(name = "size")
    private String size;

    @Column(name = "tax_id")
    private String taxId;

    @Column(name = "stock_unit")
    private String stockUnit;

    @Column(name = "commission_rate")
    private Double commissionRate;

    @Column(name = "warranty_duration")
    private Integer warrantyDuration;

    @Column(name = "warranty_unit")
    private String warrantyUnit;

    @Column(name = "warranty_content", nullable = true, columnDefinition = "TEXT(2000)")
    private String warrantyContent;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;

    @OneToMany(mappedBy = "product", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderProduct> orderProducts;

}