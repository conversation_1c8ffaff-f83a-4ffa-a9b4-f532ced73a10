package com.tti.oh_crm_service.model;

import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "organization_level_label")
@Data
public class OrganizationLevelLabel {
    
    @EmbeddedId
    private OrganizationLevelLabelId id;
    
    @Column(name = "label", nullable = false)
    private String label;
    
    // Convenience constructors
    public OrganizationLevelLabel() {
        this.id = new OrganizationLevelLabelId();
    }
    
    public OrganizationLevelLabel(Integer level, String language, String label) {
        this.id = new OrganizationLevelLabelId(level, language);
        this.label = label;
    }
    
    // Convenience getters and setters for composite key fields
    public Integer getLevel() {
        return id != null ? id.getLevel() : null;
    }
    
    public void setLevel(Integer level) {
        if (id == null) {
            id = new OrganizationLevelLabelId();
        }
        id.setLevel(level);
    }
    
    public String getLanguage() {
        return id != null ? id.getLanguage() : null;
    }
    
    public void setLanguage(String language) {
        if (id == null) {
            id = new OrganizationLevelLabelId();
        }
        id.setLanguage(language);
    }
}
