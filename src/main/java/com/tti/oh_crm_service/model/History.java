package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "histories")
@Data
@EqualsAndHashCode(callSuper=false)
public class History {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "field_name", columnDefinition = "VARCHAR(255) default ''")
    private String fieldName;

    @Column(name = "field_type", columnDefinition = "ENUM('ONE_LINE', 'MULTIPLE_LINES', 'DATE', 'DATE_TIME', 'LIST', 'CHOICE', 'MULTIPLE_CHOICES', 'INTEGER', 'DECIMAL', 'LINK', 'CURRENCY', 'PHONE_NUMBER', 'EMAIL', 'LOOKUP', 'MEASUREMENT')")
    @Enumerated(EnumType.STRING)
    private EFieldType fieldType;

    @Column(name = "field_original_value", columnDefinition = "TEXT(2000)")
    private String fieldOriginalValue;

    @Column(name = "field_new_value", columnDefinition = "TEXT(2000)")
    private String fieldNewValue;

    @ManyToOne(fetch = FetchType.LAZY)
    private User user;

    // value must be a module code from the list of modules in modules table
    @Column(name = "related_to_module", columnDefinition = "VARCHAR(50) default ''")
    private String relatedToModule;

    @Column(name = "related_to_id")
    private Long relatedToId;

    @Column(name = "action_type", columnDefinition = "ENUM('CREATE', 'UPDATE', 'DELETE', 'READ')")
    @Enumerated(EnumType.STRING)
    private EHistoryActionType actionType;

    @CreationTimestamp
    @Column(name = "history_date", nullable = false, updatable = false)
    private Date historyDate;
}
