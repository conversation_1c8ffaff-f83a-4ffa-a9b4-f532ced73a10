package com.tti.oh_crm_service.model;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Data;

@Entity
@Table(name = "activity_belong_to", uniqueConstraints={@UniqueConstraint(columnNames = {"activity_id", "belong_to_id", "belong_to_module"})})
@Data
public class ActivityBelongTo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    // value must be a module code from the list of modules in modules table
    // only LEAD, CONTACT
    @Column(name = "belong_to_module", columnDefinition = "VARCHAR(50) default ''")
    private String belongToModule;

    @Column(name = "belong_to_id", nullable = false)
    private Long belongToId;

    @Column(name = "activity_id", nullable = false)
    private Long activityId;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;

    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;

    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
}
