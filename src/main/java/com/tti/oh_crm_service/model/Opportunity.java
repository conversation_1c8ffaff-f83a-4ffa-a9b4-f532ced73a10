package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;
import java.util.Set;

@Data
@Table(name = "opportunities")
@EntityListeners({HistoryEntityListener.class})
@Entity
public class Opportunity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;
    @Column(name = "name", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String name = "";
    @Column(name = "expected_revenue", columnDefinition = "BIGINT default 0")
    private Long expectedRevenue = 0L;
    @Column(name = "end_date")
    private Date endDate;
    @Column(name = "stage", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String stage = "";
    @Column(name = "success_probability", columnDefinition = "FLOAT default 0")
    private Float successProbability = 0f;
    @Column(name = "forecast_category", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String forecastCategory = "";
    @Column(name = "next_step", columnDefinition = "VARCHAR(255) default ''")
    private String nextStep = "";
    @Column(name = "type", columnDefinition = "VARCHAR(255) default ''")
    private String type = "";
    @Column(name = "lead_source", columnDefinition = "VARCHAR(255) default ''")
    private String leadSource = "";
    @Column(name = "description", columnDefinition = "TEXT")
    private String description = "";
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private Date createdAt;
    @UpdateTimestamp
    @Column(name = "last_modified_at", nullable = false)
    private Date lastModifiedAt;
    @ManyToOne(fetch = FetchType.LAZY)
    private User createdBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User lastModifiedBy;
    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;
    @ManyToOne(fetch = FetchType.LAZY)
    private Campaign campaign;
    @ManyToOne(fetch = FetchType.LAZY)
    private Account account;
    // add Contract entity
    // add Quote entity

    @OneToMany(mappedBy = "opportunity", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ContactOpportunityRole> contactRoles;
}
