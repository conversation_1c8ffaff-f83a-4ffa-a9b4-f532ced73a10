package com.tti.oh_crm_service.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity
@Table(name = "notes")
@Data
@EqualsAndHashCode(callSuper=false)
public class Note extends AuditableBaseEntity {

    @Column(name = "title", columnDefinition = "VARCHAR(1000) default ''")
    private String title;

    @Column(name = "content", columnDefinition = "TEXT(2000)")
    private String content;

    // value must be a module code from the list of modules in modules table
    @Column(name = "related_to_module", columnDefinition = "VARCHAR(50) default ''")
    private String relatedToModule;

    @Column(name = "related_to_id")
    private Long relatedToId;
}
