package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.listener.HistoryEntityListener;
import com.tti.oh_crm_service.listener.OrderEntityListener;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "orders")
@Entity
@EntityListeners({HistoryEntityListener.class, OrderEntityListener.class})
public class Order extends AuditableBaseEntity {
    @Column(name = "code", columnDefinition = "VARCHAR(255) default ''")
    private String code = "";
    @Column(name = "status", nullable = false, columnDefinition = "VARCHAR(255) default ''")
    private String status = "";
    @Column(name = "type", columnDefinition = "VARCHAR(255) default ''")
    private String type = "";
    @Column(name = "order_date")
    private Date orderDate;
    @Column(name = "cost", columnDefinition = "INT default 0")
    private Integer cost = 0;
    @Column(name = "billing_number", columnDefinition = "VARCHAR(255) default ''")
    private String billingNumber = "";
    @Column(name = "billing_date")
    private Date billingDate;
    // giá trị đã xuất hóa đơn
    @Column(name = "billing_value", columnDefinition = "INT default 0")
    private Integer billingValue = 0;
    @Column(name = "shipping_status", columnDefinition = "VARCHAR(255) default ''")
    private String shippingStatus = "";
    @Column(name = "payment_status", columnDefinition = "VARCHAR(255) default ''")
    private String paymentStatus = "";
    // Số tiền đã thu
    @Column(name = "paid_cost", columnDefinition = "INT default 0")
    private Integer paidCost = 0;
    @Column(name = "remaining_cost", columnDefinition = "INT default 0")
    private Integer remainingCost;
    @Column(name = "shipping_date")
    private Date deliveryDate;
    @Column(name = "payment_date")
    private Date paymentDate;
    @Column(name = "shipping_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingCountryCode = "";
    @Column(name = "shipping_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String shippingProvinceCode = "";
    @Column(name = "shipping_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String shippingDistrictCode = "";
    @Column(name = "shipping_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String shippingWardCode = "";
    @Column(name = "shipping_street", columnDefinition = "VARCHAR(255) default ''")
    private String shippingStreet = "";
    @Column(name = "shipping_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String shippingZipPostalCode = "";
    @Column(name = "billing_country_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingCountryCode = "";
    @Column(name = "billing_province_code", length = 2, columnDefinition = "CHAR(2) default ''")
    private String billingProvinceCode = "";
    @Column(name = "billing_district_code", length = 3, columnDefinition = "CHAR(3) default ''")
    private String billingDistrictCode = "";
    @Column(name = "billing_ward_code", length = 5, columnDefinition = "CHAR(5) default ''")
    private String billingWardCode = "";
    @Column(name = "billing_street", columnDefinition = "VARCHAR(255) default ''")
    private String billingStreet = "";
    @Column(name = "billing_zip_postal_code", columnDefinition = "VARCHAR(20) default ''")
    private String billingZipPostalCode = "";
    @ManyToOne(fetch = FetchType.LAZY)
    private Contact contact;
    @ManyToOne(fetch = FetchType.LAZY)
    private Opportunity opportunity;
    @ManyToOne(fetch = FetchType.LAZY)
    private Contract contract;
    @ManyToOne(fetch = FetchType.LAZY)
    private User owner;
    @ManyToOne(fetch = FetchType.LAZY)
    private Account account;
    
    @OneToMany(mappedBy = "order", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private List<OrderProduct> orderProducts;
}
