package com.tti.oh_crm_service.model;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Table(name = "playbook_tools")
@Data
public class PlaybookTool extends AuditableBaseEntity {
    @Column(name = "name", nullable = false)
    private String name;
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    @Column(name = "view_count", nullable = false)
    private Integer viewCount = 0;
    
    @OneToMany(mappedBy = "playbookTool", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PlaybookToolSection> playbookToolSections;

    @ManyToMany(mappedBy = "playbookTools")
    private List<ModuleStagePlaybook> moduleStagePlaybooks;

    @OneToMany(mappedBy = "playbookTool", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PlaybookToolHistory> playbookToolHistories;
}
