package com.tti.oh_crm_service.model;

import jakarta.persistence.*;
import lombok.Data;

import java.util.List;

@Entity
@Table(name = "playbook_tool_sections")
@Data
public class PlaybookToolSection extends AuditableBaseEntity {
    @Column(name = "name", nullable = false)
    private String name;
    // longtext for rich-text editor data
    @Column(name = "content", columnDefinition = "LONGTEXT")
    private String content;
    // column to check if this section is deleted
    @Column(name = "is_in_trash", nullable = false, columnDefinition = "boolean default false")
    private Boolean isInTrash = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    private PlaybookTool playbookTool;

    @OneToMany(mappedBy = "playbookToolSection", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<PlaybookToolHistory> histories;


}
