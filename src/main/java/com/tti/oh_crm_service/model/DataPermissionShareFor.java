package com.tti.oh_crm_service.model;

import com.tti.oh_crm_service.enumeration.ESharingType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

@Entity
@Table(name = "data_permission_share_for")   
@Data
public class DataPermissionShareFor {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(nullable = false)
    private Long id;

    @Column(name = "rule_id", nullable = false)
    private Long ruleId;

    @Column(name = "share_for", columnDefinition = "ENUM('USER', 'DEPARTMENT')")
    @Enumerated(EnumType.STRING)
    private ESharingType shareFor;

    @Column(name = "share_for_id", nullable = false)
    private Long shareForId;
}
