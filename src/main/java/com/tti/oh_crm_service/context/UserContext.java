package com.tti.oh_crm_service.context;

import com.tti.oh_crm_service.model.User;

/**
 * Thread-local context to store User information for Entity Listeners
 * This allows passing user context to @EntityListeners without modifying method signatures
 */
public class UserContext {
    
    private static final ThreadLocal<User> userContext = new ThreadLocal<>();
    
    /**
     * Set the current user for the thread
     */
    public static void setCurrentUser(User user) {
        userContext.set(user);
    }
    
    /**
     * Get the current user for the thread
     */
    public static User getCurrentUser() {
        return userContext.get();
    }
    
    /**
     * Clear the user context for the thread
     */
    public static void clear() {
        userContext.remove();
    }
}
