package com.tti.oh_crm_service.context;

/**
 * Thread-local context to store entity states for history tracking
 * This allows capturing original and updated entity states at the service layer
 */
public class EntityContext {
    
    private static final ThreadLocal<Object> originalEntityContext = new ThreadLocal<>();
    private static final ThreadLocal<Object> updatedEntityContext = new ThreadLocal<>();
    
    /**
     * Set the original entity state before update
     */
    public static void setOriginalEntity(Object entity) {
        originalEntityContext.set(entity);
    }
    
    /**
     * Set the updated entity state after update
     */
    public static void setUpdatedEntity(Object entity) {
        updatedEntityContext.set(entity);
    }
    
    /**
     * Get the original entity state
     */
    public static Object getOriginalEntity() {
        return originalEntityContext.get();
    }
    
    /**
     * Get the updated entity state
     */
    public static Object getUpdatedEntity() {
        return updatedEntityContext.get();
    }
    
    /**
     * Check if both original and updated entities are available
     */
    public static boolean hasEntityStates() {
        return originalEntityContext.get() != null && updatedEntityContext.get() != null;
    }
    
    /**
     * Clear all entity contexts for the thread
     */
    public static void clear() {
        originalEntityContext.remove();
        updatedEntityContext.remove();
    }
}