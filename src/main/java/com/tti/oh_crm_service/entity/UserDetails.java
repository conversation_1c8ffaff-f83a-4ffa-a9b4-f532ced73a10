package com.tti.oh_crm_service.entity;

import com.tti.oh_crm_service.enumeration.EGender;
import lombok.Data;

import java.util.Date;

@Data
public class UserDetails {
    private Long id;
    private Long accountId;
    private String firstName;
    private String lastName;
    private String email;
    private String mobile;
    private String phone;
    private String photo;
    private EGender gender;
    private RoleView role;
    private ProfileView profile;
    private DepartmentView department;
    private String zipPostalCode;
    private String street;
    private String ward;
    private String district;
    private String province;
    private String country;
    private Date createdAt;
    private Date lastModifiedAt;
    private UserShortView createdBy;
    private UserShortView lastModifiedBy;
}
