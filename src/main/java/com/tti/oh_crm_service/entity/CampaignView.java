package com.tti.oh_crm_service.entity;

import java.util.Date;

import lombok.Data;

@Data
public class CampaignView {
    private Long id;
    private String name;
    private LookUpView owner;
    private Boolean active;
    private String status = "";
    private LookUpView campaignParent;
    private Date startDate;
    private Date endDate;
    private String type = "";
    private String description;
    private Integer numberOfSent = 0;
    private Integer expectedResponse = 0;
    private Integer budgetCost = 0;
    private Integer actualCost = 0;
    private Integer expectedRevenue = 0;
}
