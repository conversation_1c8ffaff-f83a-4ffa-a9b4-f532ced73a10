package com.tti.oh_crm_service.entity;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class CampaignDetails {
    private Long id;
    private String name;
    private LookUpView owner;
    private Boolean active;
    private String status = "";
    private LookUpView campaignParent;
    private Date startDate;
    private Date endDate;
    private String type = "";
    private String description;
    private Integer numberOfSent = 0;
    private Integer expectedResponse = 0;
    private Integer budgetCost = 0;
    private Integer actualCost = 0;
    private Integer expectedRevenue = 0;
    private Integer numberOfResponses;
    private Integer numberOfLeads;
    private Integer numberOfConvertedLeads;
    private Integer numberOfContacts;
    private Integer numberOfOpportunities;
    private Integer numberOfWonOpportunities;
    private Long valueOfOpportunities;
    private Long valueOfWonOpportunities;
    private Integer numberOfDeals;
    private Integer numberOfClosedDeals;
    private Date createdAt;
    private Date lastModifiedAt;
    private UserShortView createdBy;
    private UserShortView lastModifiedBy;
    private List<ActivityView> activities;
    private List<CampaignShortView> campaigns;

}
