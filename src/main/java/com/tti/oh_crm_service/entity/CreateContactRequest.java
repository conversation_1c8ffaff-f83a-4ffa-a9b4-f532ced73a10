package com.tti.oh_crm_service.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class CreateContactRequest {
    @NotNull
    private String salutation;
    private Long ownerId;
    private Long accountId;
    @NotNull
    private String firstName;
    @NotNull
    private String lastName;
    private String title;
    private String description;
    private String email;
    private String phone;
    private String mobile;
    private String fax;
    private String department;
    private Date birthday;
    private String countryCode;
    private String provinceCode;
    private String districtCode;
    private String wardCode;
    private String street;
    private String zipPostalCode;
}
