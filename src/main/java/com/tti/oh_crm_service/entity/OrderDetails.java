package com.tti.oh_crm_service.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class OrderDetails {
    private Long id;
    private String code;
    private String status;
    private String type;
    private Date orderDate;
    private Integer cost;
    private String billingNumber;
    private Date billingDate;
    private Integer billingValue;
    private String shippingStatus;
    private String paymentStatus;
    private Integer paidCost;
    private Integer remainingCost;
    private Date shippingDate;
    private Date paymentDate;
    private String shippingCountryCode;
    private String shippingProvinceCode;
    private String shippingDistrictCode;
    private String shippingWardCode;
    private String shippingStreet;
    private String shippingZipPostalCode;
    private String billingCountryCode;
    private String billingProvinceCode;
    private String billingDistrictCode;
    private String billingWardCode;
    private String billingStreet;
    private String billingZipPostalCode;
    private LookUpView contact;
    private LookUpView opportunity;
    private LookUpView contract;
    private LookUpView owner;
    private LookUpView account;
    private List<AttachedActivityShortView> attachedActivities;
}