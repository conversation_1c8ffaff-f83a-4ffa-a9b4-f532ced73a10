package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.List;

@Data
public class PlaybookToolWithSectionsResponse {
    private Long id;
    private String name;
    private String description;
    private Integer viewCount;
    private List<PlaybookToolSectionView> sections;
    private BulkOperationSummary operationSummary;
    
    @Data
    public static class BulkOperationSummary {
        private int sectionsCreated;
        private int sectionsUpdated;
        private int sectionsSkipped;
        private List<String> errors;
    }
}
