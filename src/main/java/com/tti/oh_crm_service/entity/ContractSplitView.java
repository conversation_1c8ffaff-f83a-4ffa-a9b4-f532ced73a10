package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.Date;

@Data
public class ContractSplitView {
    private Long id;
    private String contractNumber;
    private Date startDate;
    private Date endDate;
    private Integer durationByMonth;
    private String status;
    private Integer notificationDaysBeforeEnd;
    private String specialTerms;
    private String description;
    private String signedContent;
    private Date signedDate;
    private Date userSignedDate;
    private LookUpView owner;
    private LookUpView account;
}