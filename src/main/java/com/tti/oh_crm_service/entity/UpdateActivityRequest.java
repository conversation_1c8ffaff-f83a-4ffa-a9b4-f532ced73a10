package com.tti.oh_crm_service.entity;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class UpdateActivityRequest {
    private Long id;
    private String type;
    private String title;
    private String taskType;
    private String description;
    private String belongToModule;
    private String relatedToModule;
    private Long relatedToId;
    private Date deadline;
    private String priority;
    private String status;
    private String place;
    private Date startTime;
    private Date endTime;
    private String phone;
    private Integer callTime;
    private String callType;
    private String callResult;
    private List<Long> deleteBelongToIds;
    private List<Long> addBelongToIds;
    private List<Long> deleteAssignToUserIds;
    private List<Long> addAssignToUserIds;
}
