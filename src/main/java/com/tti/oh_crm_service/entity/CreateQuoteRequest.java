package com.tti.oh_crm_service.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class CreateQuoteRequest {
    @NotNull
    private String name;
    private String code;
    @NotNull
    private String status;
    private Date dueDate;
    private String description;
    private Float discount;
    private Integer taxCost;
    private Integer shippingCost;
    private Long contactId;
    @NotNull
    private Long opportunityId;
    private Long accountId;
    private String contactPhone;
    private String contactEmail;
    private String contactFax;
    private String shippingCountryCode;
    private String shippingProvinceCode;
    private String shippingDistrictCode;
    private String shippingWardCode;
    private String shippingStreet;
    private String shippingZipPostalCode;
    private String billingCountryCode;
    private String billingProvinceCode;
    private String billingDistrictCode;
    private String billingWardCode;
    private String billingStreet;
    private String billingZipPostalCode;
}