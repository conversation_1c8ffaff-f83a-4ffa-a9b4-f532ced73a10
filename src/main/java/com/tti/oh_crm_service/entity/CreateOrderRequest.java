package com.tti.oh_crm_service.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

@Data
public class CreateOrderRequest {
    private String code;
    @NotNull
    private String status;
    private Date orderDate;
    private Integer cost;
    private String type;
    private String billingNumber;
    private Date billingDate;
    private Integer billingValue;
    private String shippingStatus;
    private String paymentStatus;
    private Integer paidCost;
    private Integer remainingCost;
    private Date shippingDate;
    private Date paymentDate;
    private String shippingCountryCode;
    private String shippingProvinceCode;
    private String shippingDistrictCode;
    private String shippingWardCode;
    private String shippingStreet;
    private String shippingZipPostalCode;
    private String billingCountryCode;
    private String billingProvinceCode;
    private String billingDistrictCode;
    private String billingWardCode;
    private String billingStreet;
    private String billingZipPostalCode;
    private Long contactId;
    private Long opportunityId;
    @NotNull
    private Long contractId;
    private Long ownerId;
    @NotNull
    private Long accountId;
}