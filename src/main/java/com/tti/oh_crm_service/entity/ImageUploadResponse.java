package com.tti.oh_crm_service.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ImageUploadResponse {
    private List<UploadedImageInfo> uploadedImages;
    private Integer successCount;
    private Integer totalCount;
    private List<String> errors;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UploadedImageInfo {
        private String originalFileName;
        private String savedFileName;
        private String filePath;
        private Long fileSize;
        private String contentType;
    }
}
