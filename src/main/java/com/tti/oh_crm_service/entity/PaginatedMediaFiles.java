package com.tti.oh_crm_service.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaginatedMediaFiles {
    private List<MediaFileInfo> content;
    private Integer totalElements;
    private Integer currentPage;
    private Integer pageSize;
    private Integer totalPages;
    
    public boolean hasNext() {
        return currentPage < totalPages - 1;
    }
    
    public boolean hasPrevious() {
        return currentPage > 0;
    }
    
    public boolean isEmpty() {
        return content == null || content.isEmpty();
    }
}
