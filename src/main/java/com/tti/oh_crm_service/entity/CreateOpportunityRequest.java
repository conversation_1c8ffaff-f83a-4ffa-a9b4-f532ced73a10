package com.tti.oh_crm_service.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

@Data
public class CreateOpportunityRequest {
    @NotNull
    private String name;
    private Long expectedRevenue;
    private Date endDate;
    @NotNull
    private String stage;
    private Float successProbability;
    @NotNull
    private String forecastCategory;
    private String nextStep;
    private String type;
    private String leadSource;
    private String description;
    private Long ownerId;
    private Long campaignId;
    private Long accountId;
    // used for creating the associated contact in ContactOpportunityRole table
    private Long attachedContactId;
}