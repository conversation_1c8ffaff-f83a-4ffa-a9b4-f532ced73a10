package com.tti.oh_crm_service.entity;

import lombok.Data;

import java.util.Date;

import jakarta.validation.constraints.NotNull;

@Data
public class UpdateOrderRequest {
    private Long id;
    private String status;
    private String code;
    private String type;
    private Date orderDate;
    private Integer cost;
    private String billingNumber;
    private Date billingDate;
    private Integer billingValue;
    private String shippingStatus;
    private String paymentStatus;
    private Integer paidCost;
    private Date deliveryDate;
    private Date paymentDate;
    private String shippingCountryCode;
    private String shippingProvinceCode;
    private String shippingDistrictCode;
    private String shippingWardCode;
    private String shippingStreet;
    private String shippingZipPostalCode;
    private String billingCountryCode;
    private String billingProvinceCode;
    private String billingDistrictCode;
    private String billingWardCode;
    private String billingStreet;
    private String billingZipPostalCode;
    private Long contactId;
    private Long opportunityId;
    private Long contractId;
    private Long ownerId;
    private Long accountId;
}