package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.Date;

@Data
public class UpdateContractRequest {
    private Long id;
    private String contractNumber;
    private Date startDate;
    private Date endDate;
    private Integer durationByMonth;
    private String status;
    private Integer notificationDaysBeforeEnd;
    private String countryCode;
    private String provinceCode;
    private String districtCode;
    private String wardCode;
    private String street;
    private String zipPostalCode;
    private String specialTerms;
    private String description;
    private String signedContent;
    private Date signedDate;
    private Date userSignedDate;
    private Long signedByContactId;
    private Long signedByUserId;
    private Long accountId;
}