package com.tti.oh_crm_service.entity;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class ActivityView {
    private Long id;
    private String type;
    private String title;
    private String taskType;
    private String description;
    private List<LookUpMultipleView> assignToUsers;
    private String belongToModule;
    private List<LookUpMultipleView> belongTos;
    private String relatedToModule;
    private LookUpView relatedTo;
    private Date deadline;
    private String priority;
    private String status;
    private String place;
    private Date startTime;
    private Date endTime;
    private String phone;
    private Integer callTime;
    private String callType;
    private String callResult;
}
