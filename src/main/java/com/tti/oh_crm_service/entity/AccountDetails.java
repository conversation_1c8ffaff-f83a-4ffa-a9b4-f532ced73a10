package com.tti.oh_crm_service.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=false)
public class AccountDetails extends AccountView {
    private Long countRelatedContacts;
    private Long countRelatedOpportunities;
    private Long countRelatedCampaigns;
    private List<AttachedActivityShortView> attachedActivities;
    private List<ContactAttachedView> attachedContacts;
    private List<OpportunityAttachedView> attachedOpportunities;
}
