package com.tti.oh_crm_service.entity;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.Date;

@Data
public class CreateContractRequest {
    @NotNull
    private String contractNumber;
    @NotNull
    private Date startDate;
    private Date endDate;
    private Integer durationByMonth;
    private String status;
    private Integer notificationDaysBeforeEnd;
    private String countryCode;
    private String provinceCode;
    private String districtCode;
    private String wardCode;
    private String street;
    private String zipPostalCode;
    private String specialTerms;
    private String description;
    private String signedContent;
    private Date signedDate;
    private Date userSignedDate;
    private Long signedByContactId;
    private Long signedByUserId;
    @NotNull
    private Long ownerId;
    private Long accountId;
}