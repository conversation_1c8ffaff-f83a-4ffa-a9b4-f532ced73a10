package com.tti.oh_crm_service.entity;

import java.util.Date;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;

import lombok.Data;

@Data
public class HistoryView {
    private Long id;
    private String fieldName;
    private EFieldType fieldType;
    private String fieldOriginalValue;
    private String fieldNewValue;
    private LookUpView user;
    private String relatedToModule;
    private LookUpView relatedTo;
    private EHistoryActionType actionType;
    private Date historyDate;
}
