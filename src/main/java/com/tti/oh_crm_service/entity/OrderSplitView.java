package com.tti.oh_crm_service.entity;

import lombok.Data;

import java.util.Date;

@Data
public class OrderSplitView {
    private Long id;
    private String code;
    private String status;
    private String type;
    private Date orderDate;
    private Integer cost;
    private String billingNumber;
    private Date billingDate;
    private Integer billingValue;
    private String shippingStatus;
    private String paymentStatus;
    private Integer paidCost;
    private Integer remainingCost;
    private Date shippingDate;
    private Date paymentDate;
    private LookUpView contact;
    private LookUpView opportunity;
    private LookUpView contract;
    private LookUpView owner;
    private LookUpView account;
}