package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class ContractDetails {
    private Long id;
    private String contractNumber;
    private Date startDate;
    private Date endDate;
    private Integer durationByMonth;
    private String status;
    private Integer notificationDaysBeforeEnd;
    private String countryCode;
    private String provinceCode;
    private String districtCode;
    private String wardCode;
    private String street;
    private String zipPostalCode;
    private String specialTerms;
    private String description;
    private String signedContent;
    private Date signedDate;
    private Date userSignedDate;
    private LookUpView signedByContact;
    private LookUpView signedByUser;
    private LookUpView owner;
    private LookUpView account;
    private Long countRelatedOrders;
    private List<OrderAttachedView> attachedOrders;
    private List<AttachedActivityShortView> attachedActivities;
}