package com.tti.oh_crm_service.entity;

import java.util.Date;
import java.util.List;

import lombok.Data;

@Data
public class CreateActivityRequest {
    private String type;
    private String title;
    private String taskType;
    private String description;
    private List<Long> assignToUserIds;
    private String belongToModule;
    private List<Long> belongToIds;
    private String relatedToModule;
    private Long relatedToId;
    private Date deadline;
    private String priority;
    private String status;
    private String place;
    private Date startTime;
    private Date endTime;
    private String phone;
    private Integer callTime;
    private String callType;
    private String callResult;
}
