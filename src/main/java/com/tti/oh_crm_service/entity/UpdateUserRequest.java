package com.tti.oh_crm_service.entity;

import com.tti.oh_crm_service.enumeration.ESalutation;
import com.tti.oh_crm_service.enumeration.EGender;

import lombok.Data;

@Data
public class UpdateUserRequest {
    private Long id;
    private ESalutation salutation;
    private String firstName;
    private String lastName;
    private String email;
    private String mobile;
    private String phone;
    private String photo;
    private EGender gender;
    private Long roleId;
    private Long profileId;
    private Long departmentId;
    private String zipPostalCode;
    private String street;
    private String ward;
    private String district;
    private String province;
    private String country;
}
