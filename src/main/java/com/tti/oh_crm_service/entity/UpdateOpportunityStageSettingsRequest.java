package com.tti.oh_crm_service.entity;

import com.tti.oh_crm_service.enumeration.EForecastCategory;
import com.tti.oh_crm_service.enumeration.EForecastType;

import lombok.Data;

@Data
public class UpdateOpportunityStageSettingsRequest {
    private Long id;
    private String opportunityStageValue;
    private Integer winRate;
    private EForecastType forecastType;
    private EForecastCategory forecastCategory;
    private String color;
    private String keyFields;
}