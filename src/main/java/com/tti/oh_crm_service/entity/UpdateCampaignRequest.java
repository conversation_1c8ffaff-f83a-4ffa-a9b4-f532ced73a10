package com.tti.oh_crm_service.entity;

import java.util.Date;

import lombok.Data;

@Data
public class UpdateCampaignRequest {
    private long id;
    private String name;
    private Long ownerId;
    private Boolean active;
    private String status;
    private Long campaignParentId;
    private Date startDate;
    private Date endDate;
    private String type;
    private String description;
    private Integer numberOfSent;
    private Integer expectedResponse;
    private Integer budgetCost;
    private Integer actualCost;
    private Integer expectedRevenue;
}
