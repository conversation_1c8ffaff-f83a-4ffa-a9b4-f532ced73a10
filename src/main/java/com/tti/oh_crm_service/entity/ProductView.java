package com.tti.oh_crm_service.entity;

import java.util.Date;
import java.util.List;


import lombok.Data;


@Data
public class ProductView {

    private Long id;

    private String code;

    private String name;

    private String series;

    private String batchId;

    private Date expiryDate;

    private Integer minInventory;

    private String machineId;

    private Boolean followSku;

    private String description;

    private Boolean active;

    private String category;

    private String manufacturer;
    
    private Long priceBuy;

    private Long priceSale;

    private Long priceSale1;

    private Long priceSale2;

    private Long priceSale3;

    private Long fixedPriceSale;

    private List<ProductConversionView> productConversions;

    private String color;

    private String size;

    private String taxId;

    private String stockUnit;

    private Double commissionRate;

    private Integer warrantyDuration;

    private String warrantyUnit;

    private String warrantyContent;
}
