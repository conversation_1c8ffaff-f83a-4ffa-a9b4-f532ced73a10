package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class OpportunityDetails {
    private Long id;
    private String name;
    private Long expectedRevenue;
    private Date endDate;
    private String stage;
    private Float successProbability;
    private String forecastCategory;
    private String nextStep;
    private String type;
    private String leadSource;
    private String description;
    private LookUpView owner;
    private LookUpView campaign;
    private LookUpView account;
    private Long countRelatedContacts;
    private Long countRelatedQuotes;
    private List<AttachedActivityShortView> attachedActivities;
    private List<OpportunityContactRoleView> attachedContactRoles;
    private List<QuoteAttachedView> attachedQuotes;
}