package com.tti.oh_crm_service.entity;

import lombok.Data;
import java.util.Date;

@Data
public class UpdateOpportunityRequest {
    private Long id;
    private String name;
    private Long expectedRevenue;
    private Date endDate;
    private String stage;
    private Float successProbability;
    private String forecastCategory;
    private String nextStep;
    private String type;
    private String leadSource;
    private String description;
    private Long accountId;
    private Long campaignId;
}