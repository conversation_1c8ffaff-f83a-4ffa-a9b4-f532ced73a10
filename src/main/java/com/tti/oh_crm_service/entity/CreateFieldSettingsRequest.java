package com.tti.oh_crm_service.entity;

import java.util.List;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHintType;
import com.tti.oh_crm_service.enumeration.EMeasurementType;

import lombok.Data;

@Data
public class CreateFieldSettingsRequest {
    private String moduleCode;
    private String groupCode;
    private String name;
    private String label;
    private String hint;
    private EHintType hintType;
    private EFieldType fieldType;
    private String listCode;
    private Boolean listCustom;
    private Integer length;
    private Boolean active;
    private String editOptions;
    private Boolean required;
    private Integer columnSpan;
    private String showOptions;
    private Boolean checkDuplicated;
    private Boolean custom;
    private Integer position;
    private Boolean checkZero;
    private Boolean choiceDefault;
    private Boolean onlyNumber;
    private Boolean sortByAlphaBet;
    private EMeasurementType measurementType;
    private Integer measurementLevel;
    private String measurementBaseUnit;
    private List<TblListValueView> listAddedItems;
}
