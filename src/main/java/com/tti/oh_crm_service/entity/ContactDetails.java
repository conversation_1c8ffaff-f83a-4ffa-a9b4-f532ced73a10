package com.tti.oh_crm_service.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ContactDetails {
    private Long id;
    private String salutation;
    private LookUpView account;
    private LookUpView owner;
    private String title;
    private String firstName;
    private String lastName;
    private String description;
    private String email;
    private String phone;
    private String mobile;
    private String fax;
    private String department;
    private Date birthday;
    private String countryCode;
    private String provinceCode;
    private String districtCode;
    private String wardCode;
    private String street;
    private String zipPostalCode;
    private Long countRelatedOpportunities;
    private Long countRelatedQuotes;
    private List<AttachedActivityShortView> attachedActivities;
    private List<OpportunityAttachedView> attachedOpportunities;
    private List<QuoteAttachedView> attachedQuotes;
}