package com.tti.oh_crm_service.entity;

import lombok.Data;

@Data
public class AccountView {

    private Long id;
    private String name;
    private LookUpView owner;
    private String description = "";
    private String phone = "";
    private String email = "";
    private LookUpView accountParent;
    private String type = "";
    private String industry = "";
    private String website = "";
    private String mobile = "";
    private Integer noOfEmployees;
    private String billingCountryCode = "";
    private String billingProvinceCode = "";
    private String billingDistrictCode = "";
    private String billingWardCode = "";
    private String billingStreet = "";
    private String billingZipPostalCode = "";
    private String shippingCountryCode = "";
    private String shippingProvinceCode = "";
    private String shippingDistrictCode = "";
    private String shippingWardCode = "";
    private String shippingStreet = "";
    private String shippingZipPostalCode = "";

}
