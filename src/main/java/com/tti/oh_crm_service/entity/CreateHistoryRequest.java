package com.tti.oh_crm_service.entity;

import java.util.Date;

import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;

import lombok.Data;

@Data
public class CreateHistoryRequest {
    private String fieldName;
    private EFieldType fieldType;
    private String fieldOriginalValue;
    private String fieldNewValue;
    private Long userId;
    private String relatedToModule;
    private Long relatedToId;
    private EHistoryActionType actionType;
    private Date historyDate;
}
