package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.entity.CreateProductRequest;
import com.tti.oh_crm_service.entity.ProductKanbanResponse;
import com.tti.oh_crm_service.entity.ProductSplitViewResponse;
import com.tti.oh_crm_service.entity.ProductTableResponse;
import com.tti.oh_crm_service.entity.ProductView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateProductInKanbanRequest;
import com.tti.oh_crm_service.entity.UpdateProductRequest;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.ProductService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/product")
@SecurityRequirement(name = "ohcrm-service")
public class ProductController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private ProductService productService;

    //get deatail by id
    @GetMapping(path ="/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductView>>> getProductDetail(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "id") Long id) {
            Claims claims = TokenUtils.getToken(jwtService, authToken);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.getProductDetail(claims,locale,id)));
    }


    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductTableResponse>>> getProducts(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "groupValue", required = false) String groupValue,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.getProducts(filters,
        groupBy,
        groupValue,
        search,
        sortBy,
        sortDirection,
        page,
        limit,
        accountId,
        language)));
    }

    @PostMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductView>>> createProduct(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateProductRequest request
    ) {

        Claims claims = TokenUtils.getToken(jwtService, authToken);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.createProduct(claims, request, locale)));
    }

    @PutMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductView>>> updateProduct(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateProductRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.updateProduct(claims, request, locale)));
    }

    @DeleteMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<Long>>>> deleteProducts(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<Long> productIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(productService.deleteProducts(productIds)));
    }

    @GetMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductKanbanResponse>>> getProductsKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.getProductKanban(
            filters,
            groupBy,
            search,
            accountId,
            language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateProductInKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateProductInKanbanRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.updateKanbanProduct(request.getId(), request.getFieldName(), request.getFieldValue(), accountId, language)));
    }

    @GetMapping(path = "split")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProductSplitViewResponse>>> getProductsSplit(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(productService.getProductsSplitView(
            filters,
            search,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }
}
