package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import com.google.common.base.CaseFormat;
import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.Note;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.NoteService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/note")
@SecurityRequirement(name = "ohcrm-service")
public class NoteController {
    @Autowired
    private NoteService noteService;

    @Autowired
    private JwtService jwtService;

    @GetMapping(path ="/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<NoteView>>> getNoteDetail(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "id") Long id) {
        return Mono.justOrEmpty(ResponseEntity.ok(noteService.getNoteDetail(id)));
    }


    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<NoteView>>>> getNotes(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "relatedModule", required = true) String relatedModule,
        @RequestParam(name = "relatedId", required = false) Long relatedId,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {

        String language = LocaleUtils.getLanguage(locale);
        sortBy = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, sortBy);
        return Mono.justOrEmpty(ResponseEntity.ok(noteService.getNotesByRelated(relatedModule, relatedId, sortBy, sortDirection, page, limit, language)));
    }

    @PostMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<NoteView>>> createNote(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateNoteRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(noteService.createNote( request, accountId)));
    }

    @PutMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<NoteView>>> updateNote(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateNoteRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(noteService.updateNote(request, accountId)));
    }

    @DeleteMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<Long>>>> deleteNotes(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<Long> noteIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(noteService.deleteNotes(noteIds)));
    }
}
