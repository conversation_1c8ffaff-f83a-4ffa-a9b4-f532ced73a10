package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import com.tti.oh_crm_service.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.config.TenantContextHolder;
import com.tti.oh_crm_service.constant.StatusCode;
import com.tti.oh_crm_service.enumeration.EListType;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.OrganizationLevelLabelService;
import com.tti.oh_crm_service.service.SettingService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/settings")
@SecurityRequirement(name = "ohcrm-service")
public class SettingController {

    @Autowired
    private SettingService settingService;

    @Autowired
    private JwtService jwtService;
    
    @Autowired
    private OrganizationLevelLabelService organizationLevelLabelService;

    @PostMapping(path = "/roles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<RoleView>>> createRole(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateRoleRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createRole(request, accountId, language)));
    }

    @GetMapping(path = "/roles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<RoleView>>>> getRoles(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getRoles(accountId, language)));
    }

    @GetMapping(path = "/roles/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<RoleView>>> getRoleDetails(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "roleId") Long roleId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getRoleDetails(roleId, accountId, language)));
    }
    
    @PutMapping(path = "/roles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<RoleView>>> updateRole(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateRoleRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateRole(request, accountId, language)));
    }
    
    @DeleteMapping(path = "/roles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<RoleView>>> transferAndDeleteRole(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody TransferAndDeleteRoleRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteRoles(request, accountId, language)));
    }

    @PostMapping(path = "/profiles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProfileView>>> createProfile(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateProfileRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createProfile(request, accountId, language)));
    }

    @GetMapping(path = "/profiles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ProfileView>>>> getProfiles(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getProfiles(accountId, language)));
    }

    @GetMapping(path = "/profiles/permissions")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ProfilePermissionView>>>> getProfilePermissions(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "profileId", required = false) Long profileId
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getProfilePermissions(profileId)));
    }

    @PostMapping(path = "/profiles/permissions")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateProfilePermissions(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateProfilePermissionsRequest request
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateProfilePermissions(request)));
    }

    @PutMapping(path = "/profiles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ProfileView>>> updateProfile(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateProfileRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateProfile(request, accountId, language)));
    }
    
    @DeleteMapping(path = "/profiles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> deleteProfiles(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<Long> profileIds
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteProfiles(profileIds, accountId, language)));
    }

    @PostMapping(path = "/modules")
    public Mono<ResponseEntity<Response<ModuleView>>> createModule(
        @RequestBody CreateModuleRequest request
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createModule(request)));
    }

    @GetMapping(path = "/modules")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ModuleView>>>> getModules(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = true) Locale locale
    ) {

        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getModules(accountId, language)));
    }

    @PostMapping(path = "/modules/update-display-name")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ModuleView>>> updateModuleDisplayName(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateDisplayNameRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateModuleDisplayName(accountId, request)));
    }

    @PostMapping(path = "/modules/update")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ModuleView>>>> updateModules(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<ModuleView> modules
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateModules(accountId, modules)));
    }

    @PostMapping(path = "/departments")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DepartmentView>>> createDepartment(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateDepartmentRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createDepartment(request, accountId, language)));
    }

    @PutMapping(path = "/departments")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DepartmentView>>> updateDepartment(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateDepartmentRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateDepartment(request, accountId, language)));
    }

    @GetMapping(path = "/departments")
    public Mono<ResponseEntity<Response<List<DepartmentView>>>> getDepartments() {
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDepartments()));
    }

    @GetMapping(path = "/departments/tree")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<DepartmentViewTreeNode>>>> getDepartmentsTree(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDepartmentsTree(accountId, language)));
    }

    @GetMapping(path = "/departments/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DepartmentDetails>>> getDepartmentDetails(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "departmentId") Long departmentId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDepartmentDetails(departmentId, accountId, language)));
    }

    @DeleteMapping(path = "/departments")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> deleteDepartment(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody TransferAndDeleteDepartmentRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteDepartment(request, accountId, language)));
    }

    @PostMapping(path = "/users")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<UserView>>> createUser(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateUserRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return settingService.createUser(request, authToken, organizationId, accountId, language)
                .map(ResponseEntity::ok);
    }

    @GetMapping(path = "/users")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<UserView>>>> getUsers(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getUsers()));
    }

    @GetMapping(path = "/users/role")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<UserView>>>> getUsersByRole(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "roleId") Long roleId,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getUsersByRole(roleId, page, limit, language)));
    }

    @PutMapping(path = "/users")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<UserView>>> updateUser(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateUserRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateUser(request, accountId, language)));
    }

    @DeleteMapping(path = "/users")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> deleteUser(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "userId") Long userId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteUser(userId, accountId, language)));
    }

    @GetMapping(path = "/users/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<UserDetails>>> getUserDetails(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "userId") Long userId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getUserDetails(userId, accountId, language)));
    }
    
    @GetMapping(path = "/module")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ModuleSettings>>> getModuleSettings(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "moduleCode") String moduleCode
    ) {
        // String[] tokenParts = authToken.split(" ");
        // Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        // long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getModuleSettings(moduleCode, language)));
    }

    @PostMapping(path = "/module_layout")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<LayoutSettingsView>>> createOrUpdateLayout (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateLayoutSettingsRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createLayoutSettings(request, accountId,  language)));
    }

    @PutMapping(path = "/module_layout")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<ListValueRelatedDataItem>>>> createOrUpdateLayout (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateLayoutSettingsRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateLayoutSettings(request, accountId,  language)));
    }

    @PostMapping(path = "/layout/group")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<LayoutGroupSettingsView>>> createOrUpdateLayoutGroup (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateGroupSettingsRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createGroupSettings(request, accountId, language)));
    }

    @PutMapping(path = "/layout/group")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<LayoutGroupSettingsView>>> createOrUpdateLayoutGroup (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateGroupSettingsRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateGroupSettings(request, accountId, language)));
    }

    @PostMapping(path = "/fields")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<FieldSettingsView>>>> createFields (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<CreateFieldSettingsRequest> request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createFieldSettings(request, accountId, language)));
    }

    @PutMapping(path = "/fields")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<FieldSettingsView>>>> updateFields (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<UpdateFieldSettingsRequest> request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateFieldSettings(request, accountId, language)));
    }

    @DeleteMapping(path = "/field")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Integer>>> deleteFieldSetting (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<DeleteFieldSettingsRequest> fields
    ) {
        // String[] tokenParts = authToken.split(" ");
        // Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        // long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteFieldSettings(fields, language)));
    }
    
    @GetMapping(path = "/list_by_type")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<TblListView>>>> getListCommon(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "type") EListType type
    ) {
        // String[] tokenParts = authToken.split(" ");
        // Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        // long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getListByType(type, language)));
    }

    @GetMapping(path = "/list_of_values")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<TblListValueView>>>> getListOfValues(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "listCode") String listCode
    ) {
        // String[] tokenParts = authToken.split(" ");
        // Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        // long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getListOfValues(listCode, language)));
    }

    @PostMapping(path = "/list/update_for_field")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<TblListValueView>>>> createOrUpdateListOfValues (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateFieldListRequest request
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createOrUpdateListOfValuesForField(request, accountId, language)));
    }

    @GetMapping(path = "/lead_status")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<LeadStatusSettingsViewFull>>>> getLeadStatusSettings(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getLeadStatusSettings(language)));
    }

    @PutMapping(path = "/lead_status")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateLeadStatusSettings (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<UpdateLeadStatusSettingsRequest> updateLeadStatusSettingsRequests
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateLeadStatusSettings(updateLeadStatusSettingsRequests, accountId)));
    }

    @GetMapping(path = "/opportunity_stage")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<OpportunityStageSettingsViewFull>>>> getOpportunityStageSettings(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getOpportunityStageSettings(language)));
    }

    @PutMapping(path = "/opportunity_stage")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateOpportunityStageSettings (
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<UpdateOpportunityStageSettingsRequest> updateOpportunityStageSettingsRequests
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        long accountId = Long.valueOf(claims.get("accountId").toString());

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateOpportunityStageSettings(updateOpportunityStageSettingsRequests, accountId)));
    }

    
    @GetMapping(path = "/organization-levels")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<OrganizationLevelLabelView>>>> getOrganizationLevels(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        String language = LocaleUtils.getLanguage(locale);
        List<OrganizationLevelLabelView> labels = organizationLevelLabelService.getOrganizationLevelLabels(language);
        return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.OK, "Get organization level labels success!!!", labels)));
    }

    @GetMapping(path = "/organization-levels/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrganizationLevelLabelView>>> getOrganizationLevelLabel(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "level") Integer level
    ) {
        try {
            String language = LocaleUtils.getLanguage(locale);
            OrganizationLevelLabelView label = organizationLevelLabelService.getOrganizationLevelLabel(level, language);
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.OK, "Get organization level label success!!!", label)));
        } catch (IllegalArgumentException e) {
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.BAD_REQUEST, e.getMessage())));
        }
    }

    @PostMapping(path = "/organization-levels")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrganizationLevelLabelView>>> createOrganizationLevelLabel(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateOrganizationLevelLabelRequest request
    ) {
        try {
            String language = LocaleUtils.getLanguage(locale);
            OrganizationLevelLabelView label = organizationLevelLabelService.createOrganizationLevelLabel(
                request.getLevel(), 
                language, 
                request.getLabel()
            );
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.OK, "Create organization level label success!!!", label)));
        } catch (Exception e) {
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.INTERNAL_SERVER_ERROR, "Error creating organization level label: " + e.getMessage())));
        }
    }

    @PutMapping(path = "/organization-levels")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrganizationLevelLabelView>>> updateOrganizationLevelLabel(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateOrganizationLevelLabelRequest request
    ) {
        try {
            String language = LocaleUtils.getLanguage(locale);
            OrganizationLevelLabelView label = organizationLevelLabelService.updateOrganizationLevelLabel(
                request.getLevel(), 
                language, 
                request.getLabel()
            );
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.OK, "Update organization level label success!!!", label)));
        } catch (IllegalArgumentException e) {
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.BAD_REQUEST, e.getMessage())));
        }
    }

    @DeleteMapping(path = "/organization-levels")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<String>>> deleteOrganizationLevelLabel(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "level") Integer level
    ) {
        try {
            String language = LocaleUtils.getLanguage(locale);
            String result = organizationLevelLabelService.deleteOrganizationLevelLabel(level, language);
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.OK, result)));
        } catch (IllegalArgumentException e) {
            return Mono.justOrEmpty(ResponseEntity.ok(new Response<>(StatusCode.BAD_REQUEST, e.getMessage())));
        }
    }

    @GetMapping(path = "/data_permission_by_module_code")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DataPermissionView>>> getDataPermissionByModuleCode(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "moduleCode") String moduleCode
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDataPermissionByModuleCode(moduleCode, accountId, language)));
    }

    @GetMapping(path = "/data_permissions")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<DataPermissionView>>>> getDataPermissions(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDataPermissions(accountId, language)));
    }

    @PostMapping(path = "/data_permissions")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<DataPermissionView>>>> createOrUpdateDataPermissions(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<CreateDataPermissionRequest> requests
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createOrUpdateDataPermissions(requests, accountId, language)));
    }

    @GetMapping(path = "/data_permissions/share_rules")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<DataPermissionShareRuleView>>>> getDataPermissionShareRules(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "moduleCode") String moduleCode
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getDataPermissionShareRules(moduleCode, accountId, language)));
    }

    @PostMapping(path = "/data_permissions/share_rules")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DataPermissionShareRuleView>>> createDataPermissionShareRule(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateDataPermissionShareRuleRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.createDataPermissionShareRule(request, accountId, language)));
    }

    @PutMapping(path = "/data_permissions/share_rules")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<DataPermissionShareRuleView>>> updateDataPermissionShareRule(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateDataPermissionShareRuleRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.updateDataPermissionShareRule(request, accountId, language)));
    }

/*************  ✨ Windsurf Command ⭐  *************/
/**
 * Deletes field settings for the given list of fields in the specified language.
 *
 * @param authToken the authorization token for user authentication
 * @param organizationId the ID of the organization
 * @param locale the locale to determine the language, optional
 * @param fields the list of field settings to be deleted
 * @return a Mono containing a ResponseEntity with the number of fields deleted
 */

/*******  ********-5441-4d09-8174-67ea9c5dafb2  *******/
    @DeleteMapping(path = "/data_permissions/share_rules")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> deleteDataPermissionShareRule (
        @RequestBody List<Long> ruleIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.deleteDataPermissionShareRule(ruleIds)));
    }

    @PostMapping(path = "/images/upload", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ImageUploadResponse>>> uploadImages(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestPart(name = "images", required = true) List<FilePart> imageFiles
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.uploadImages(imageFiles, accountId, language)));
    }

    @PostMapping(path = "/files/upload", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<FileUploadResponse>>> uploadFiles(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestPart(name = "files", required = true) List<FilePart> files,
        @RequestPart(name = "module", required = true) String module
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(settingService.uploadFiles(files, module, accountId, language)));
    }

    @GetMapping(path = "/medias")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<PaginatedMediaFiles>>> getMediaFiles(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "module", required = true) String module,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "20") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        Long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);

        // Use the same pattern as other working methods in this controller
        return Mono.justOrEmpty(ResponseEntity.ok(settingService.getMediaFiles(module, page, limit, accountId, language)));
    }
}
