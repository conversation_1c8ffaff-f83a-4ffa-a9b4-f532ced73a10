package com.tti.oh_crm_service.controller;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.OpportunityService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Locale;


@RestController
@Transactional
@RequestMapping("/crm/opportunity")
@SecurityRequirement(name = "ohcrm-service")
@Slf4j
public class OpportunityController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private OpportunityService opportunityService;


    @GetMapping("")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunityTableResponse>>> getOpportunitiesTable(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "groupValue", required = false) String groupValue,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);

        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getOpportunitiesTable(
                filters,
                groupBy,
                groupValue,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PostMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunityView>>> createOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody CreateOpportunityRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.createOpportunity(request, accountId, language)));
    }

    @PutMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunityView>>> updateOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateOpportunityRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.updateOpportunity(request, accountId, language)));
    }

    @DeleteMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Integer>>> deleteOpportunities(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody List<Long> opportunityIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.deleteOpportunities(opportunityIds)));
    }

    @GetMapping(path = "/details")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunityDetails>>> getOpportunityDetails(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "opportunityId", required = true) Long opportunityId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getOpportunityDetails(
                opportunityId,
                accountId,
                language
        )));
    }

    @GetMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunityKanbanResponse>>> getOpportunitiesKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getOpportunitiesKanban(
                filters,
                groupBy,
                search,
                accountId,
                language
        )));
    }

    @GetMapping(path = "split")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OpportunitySplitViewResponse>>> getOpportunitiesSplit(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getOpportunitiesSplitView(
                filters,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateOpportunityInKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateOpportunityInKanbanRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.updateKanbanOpportunity(request.getId(), request.getFieldName(), request.getFieldValue(), accountId, language)));
    }

    @PostMapping(path = "/{opportunityId}/contact-roles")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "opportunityId", description = "Opportunity ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<List<OpportunityContactRoleView>>>> bulkCreateContactRolesForOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long opportunityId,
            @RequestBody List<CreateOpportunityContactRoleAttachedRequest> contactRoleList
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.bulkCreateContactRolesForOpportunity(contactRoleList, opportunityId, accountId, language)));
    }

    @PutMapping(path = "/{opportunityId}/contact-roles")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "opportunityId", description = "Opportunity ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<List<OpportunityContactRoleView>>>> bulkUpdateContactRolesForOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long opportunityId,
            @RequestBody List<UpdateOpportunityContactRoleAttachedRequest> contactRoleList
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.bulkUpdateContactRolesForOpportunity(contactRoleList, opportunityId, accountId, language)));
    }

    @DeleteMapping(path = "/{opportunityId}/contact-roles")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "opportunityId", description = "Opportunity ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<String>>> bulkDeleteContactRoleForOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long opportunityId,
            @RequestBody List<Long> contactRoleIds
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.bulkDeleteContactRoleForOpportunity(contactRoleIds, opportunityId, accountId, language)));
    }
    
    @GetMapping(path = "/{opportunityId}/contact-roles")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "opportunityId", description = "Opportunity ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64")),
            @Parameter(name = "page", description = "Page number (0-based)", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "0")),
            @Parameter(name = "limit", description = "Number of items per page", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
    })
    public Mono<ResponseEntity<Response<GenericTableResponse<OpportunityContactRoleView>>>> getContactRolesForOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long opportunityId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getContactRolesForOpportunity(opportunityId, page, limit, accountId, language)));
    }

    @GetMapping(path = "/{opportunityId}/attached-quotes")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "opportunityId", description = "Opportunity ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64")),
            @Parameter(name = "page", description = "Page number (0-based)", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "0")),
            @Parameter(name = "limit", description = "Number of items per page", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
    })
    public Mono<ResponseEntity<Response<GenericTableResponse<QuoteAttachedView>>>> getAttachedQuoteForOpportunity(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long opportunityId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(opportunityService.getAttachedQuoteForOpportunity(opportunityId, page, limit, accountId, language)));
    }
}
