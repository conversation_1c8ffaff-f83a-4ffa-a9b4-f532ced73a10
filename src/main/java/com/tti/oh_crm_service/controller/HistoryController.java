package com.tti.oh_crm_service.controller;

import java.util.Locale;

import com.google.common.base.CaseFormat;
import com.tti.oh_crm_service.entity.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.service.HistoryService;
import com.tti.oh_crm_service.utils.LocaleUtils;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/history")
@SecurityRequirement(name = "ohcrm-service")
public class HistoryController {
    @Autowired
    private HistoryService historyService;

    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<HistoryViewWithFieldLabel>>>> getHistories(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "relatedModule", required = true) String relatedModule,
        @RequestParam(name = "relatedId", required = false) Long relatedId,
        @RequestParam(name = "sortBy", required = false, defaultValue = "history_date") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {

        String language = LocaleUtils.getLanguage(locale);
        //sortBy = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, sortBy);
        return Mono.justOrEmpty(ResponseEntity.ok(historyService.getHistoriesByRelated(relatedModule, relatedId, sortBy, sortDirection, page, limit, language)));
    }

    @GetMapping(path = "details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<HistoryViewWithFieldLabel>>> getHistoryDetail(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "id", required = true) Long id
    ) {
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(historyService.getHistoryDetail(id, language)));
    }

}
