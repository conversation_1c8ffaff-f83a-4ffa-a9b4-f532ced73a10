package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.entity.CreateFilterRequest;
import com.tti.oh_crm_service.entity.CreateTagRequest;
import com.tti.oh_crm_service.entity.FilterView;
import com.tti.oh_crm_service.entity.HomeView;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.TagView;
import com.tti.oh_crm_service.entity.UpdateFilterRequest;
import com.tti.oh_crm_service.service.HomeService;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/home")
@SecurityRequirement(name = "ohcrm-service")
public class HomeController {

    @Autowired
    private JwtService jwtService;

    @Autowired
    private HomeService homeService;

    @PostMapping(path = "/init")
    public Mono<ResponseEntity<Response<HomeView>>> initOrganization(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.iniOrg(claims, organizationId)));
    }

    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<HomeView>>> getHome(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale
    ) {
        String[] tokenParts = authToken.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.home(Long.valueOf(claims.get("accountId").toString()), locale)));
    }

    @PostMapping(path = "/tags")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<TagView>>> createTag(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestBody CreateTagRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.createTag(request, accountId)));
    }

    @GetMapping(path = "/tags")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<TagView>>>> getTags(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.getTags(search, accountId)));
    }
    
    @DeleteMapping(path = "/tags/{tagId}")
    public Mono<ResponseEntity<Response<Boolean>>> deleteTag(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @PathVariable Long tagId
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.deleteTag(tagId)));
    }

    @PostMapping(path = "/filters")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<FilterView>>> createFilter(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestBody CreateFilterRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.createFilter(request, accountId)));
    }

    @GetMapping(path = "/filters")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<FilterView>>>> getFilters(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "moduleCode", required = false) String moduleCode,
        @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.getFilters(moduleCode, search, accountId)));
    }

    @PutMapping(path = "/filters")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<FilterView>>> updateFilter(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestBody UpdateFilterRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.updateFilter(request, accountId)));
    }

    @DeleteMapping(path = "/filters/{filterId}")
    public Mono<ResponseEntity<Response<Boolean>>> deleteFilter(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @PathVariable Long filterId
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(homeService.deleteFilter(filterId)));
    }
}