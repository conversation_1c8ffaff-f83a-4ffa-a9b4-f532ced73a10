package com.tti.oh_crm_service.controller;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.ContractService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.text.ParseException;
import java.util.List;
import java.util.Locale;


@RestController
@Transactional
@RequestMapping("/crm/contract")
@SecurityRequirement(name = "ohcrm-service")
@Slf4j
public class ContractController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private ContractService contractService;


    @GetMapping("")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractTableResponse>>> getContractsTable(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "groupValue", required = false) String groupValue,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);

        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.getContractsTable(
                filters,
                groupBy,
                groupValue,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PostMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractView>>> createContract(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody CreateContractRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.createContract(request, accountId, language)));
    }

    @PutMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractView>>> updateContract(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateContractRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.updateContract(request, accountId, language)));
    }

    @DeleteMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Integer>>> deleteContracts(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody List<Long> contractIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.deleteContracts(contractIds)));
    }

    @GetMapping(path = "/details")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractDetails>>> getContractDetails(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "contractId", required = true) Long contractId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.getContractDetails(
                contractId,
                accountId,
                language
        )));
    }

    @GetMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractKanbanResponse>>> getContractsKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "search", required = false) String search
    ) throws ParseException {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
//        return Mono.justOrEmpty(ResponseEntity.ok(contractService.getContractsKanban(
//                filters,
//                groupBy,
//                search,
//                accountId,
//                language
//        )));
        try {
            return Mono.justOrEmpty(ResponseEntity.ok(contractService.getContractsKanban(
                    filters,
                    groupBy,
                    search,
                    accountId,
                    language
            )));
        } catch (ParseException e) {
            e.printStackTrace();
            throw e;
        }
    }

    @GetMapping(path = "split")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ContractSplitViewResponse>>> getContractsSplit(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.getContractsSplitView(
                filters,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateContractInKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateContractInKanbanRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.updateKanbanContract(request.getId(), request.getFieldName(), request.getFieldValue(), accountId, language)));
    }

    @GetMapping(path = "{contractId}/attached-orders")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "contractId", description = "Contract ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64")),
            @Parameter(name = "page", description = "Page number (0-based)", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "0")),
            @Parameter(name = "limit", description = "Number of items per page", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
    })
    public Mono<ResponseEntity<Response<GenericTableResponse<OrderAttachedView>>>> getAttachedOrdersForContract(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable Long contractId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(contractService.getAttachedOrdersForContract(
                contractId,
                page,
                limit,
                accountId,
                language
        )));
    }
}
