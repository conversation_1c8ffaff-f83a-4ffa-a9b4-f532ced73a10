package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.entity.CreateCampaignRequest;
import com.tti.oh_crm_service.entity.CampaignContactView;
import com.tti.oh_crm_service.entity.CampaignDetails;
import com.tti.oh_crm_service.entity.CampaignKanbanResponse;
import com.tti.oh_crm_service.entity.CampaignLeadMemberView;
import com.tti.oh_crm_service.entity.CampaignLeadView;
import com.tti.oh_crm_service.entity.CampaignRelated;
import com.tti.oh_crm_service.entity.CampaignSplitViewResponse;
import com.tti.oh_crm_service.entity.CampaignTableResponse;
import com.tti.oh_crm_service.entity.CampaignView;
import com.tti.oh_crm_service.entity.CreateCampaignContactRequest;
import com.tti.oh_crm_service.entity.CreateCampaignLeadRequest;
import com.tti.oh_crm_service.entity.Response;
import com.tti.oh_crm_service.entity.UpdateCampaignLeadRequest;
import com.tti.oh_crm_service.entity.UpdateCampaignRequest;
import com.tti.oh_crm_service.entity.UpdateItemInKanbanRequest;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.service.CampaignService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/campaign")
@SecurityRequirement(name = "ohcrm-service")
public class CampaignController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private CampaignService campaignService;

    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignTableResponse>>> getCampaignsTable(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "groupValue", required = false) String groupValue,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getCampaignsTable(
            filters,
            groupBy,
            groupValue,
            search,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }

    @PostMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignView>>> createCampaign(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateCampaignRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.createCampaign(request, accountId, language)));
    }

    @PutMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignView>>> updateCampaign(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateCampaignRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.updateCampaign(request, accountId, language)));
    }

    @DeleteMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Integer>>> deleteCampaigns(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<Long> campaignIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.deleteCampaigns(campaignIds)));
    }

    @GetMapping(path = "/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignDetails>>> getCampaignDetails(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "campaignId", required = true) Long campaignId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getCampaignDetails(
            campaignId,
            accountId,
            language
        )));
    }

    @GetMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignKanbanResponse>>> getCampaignsKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getCampaignsKanban(
            filters,
            groupBy,
            search,
            accountId,
            language
        )));
    }

    @GetMapping(path = "split")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignSplitViewResponse>>> getCampaignsSplit(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getCampaignsSplitView(
            filters,
            search,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateCampaignInKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateItemInKanbanRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.updateKanbanCampaign(request.getId(), request.getFieldName(), request.getFieldValue(), accountId, language)));
    }
    
    // @PostMapping(path = "import", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE })
    // @Parameters({
    //     @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    // })
    // public Mono<ResponseEntity<Response<ImportCampaignResponse>>> importCampaigns(
    //     @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
    //     @RequestHeader(value = "Organization-Id") Long organizationId,
    //     @RequestHeader(value = "Accept-Language", required = false) Locale locale,
    //     @RequestPart(name = "file", required = true) FilePart file,
    //     @RequestPart(name = "request") ImportCampaignRequest request
    // ) {
    //     Claims claims = TokenUtils.getToken(jwtService, authToken);
    //     long accountId = TokenUtils.getAccountId(claims);
    //     String language = LocaleUtils.getLanguage(locale);
        
    //     return campaignService.importCampaigns(file, request, accountId, language);
    // }

    @GetMapping(path = "/leads")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<CampaignLeadView>>>> getLeads(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getLeads(
            filters,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }

    @GetMapping(path = "/contacts")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<CampaignContactView>>>> getContacts(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getContacts(
            filters,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }

    @PostMapping(path = "/add_leads_to_campaign")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> addLeadsToCampaign(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateCampaignLeadRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.addLeadToCampaign(request, accountId, language)));
    }

    @PostMapping(path = "/add_contacts_to_campaign")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> addContactsToCampaign(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateCampaignContactRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.addContactToCampaign(request, accountId, language)));
    }

    @GetMapping(path = "/related")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<CampaignRelated>>> getCampaignRelated(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "campaignId", required = true) Long campaignId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getCampaignRelated(
            campaignId,
            accountId,
            language
        )));
    }

    @GetMapping(path = "/leads_of_campaign")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<CampaignLeadMemberView>>>> getLeadsOfCampaign(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "campaignId", required = true) Long campaignId,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.getLeadsOfCampaign(
            campaignId,
            page,
            limit,
            accountId,
            language
        )));
    }

    @PutMapping(path = "/update_lead_member_status")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateLeadMemberStatus(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateCampaignLeadRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(campaignService.updateLeadMemberStatus(request, accountId, language)));
    }
}
