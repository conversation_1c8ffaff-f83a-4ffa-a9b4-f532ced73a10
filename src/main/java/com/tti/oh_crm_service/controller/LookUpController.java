package com.tti.oh_crm_service.controller;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.tti.oh_crm_service.service.LookUpService;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/lookup")
@SecurityRequirement(name = "ohcrm-service")
public class LookUpController {

    @Autowired
    private LookUpService lookUpService;

    @GetMapping(path = "users")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpUserView>>>> lookUpUsers(
            @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpUsers(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "accounts")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpAccountView>>>> lookUpAccounts(
            @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpAccounts(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "contacts")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpContactView>>>> lookUpContacts(
            @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpContacts(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "contracts")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpContractView>>>> lookUpContracts(
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpContracts(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "orders")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpOrderView>>>> lookUpOrders(
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpOrders(keywordSearch, page, limit)));
    }

    @GetMapping(path = "opportunities")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpOpportunityView>>>> lookUpOpportunities(
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpOpportunities(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "leads")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookUpLeadView>>>> lookUpLeads(
            @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpLeads(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "campaigns")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<LookupModuleShortView>>>> lookUpCampaigns(
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpCampaigns(keywordSearch, page, limit)));
    }
    
    @GetMapping(path = "roles")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Page<RoleView>>>> lookUpRoles(
            @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestParam(name = "keywordSearch", required = false) String keywordSearch,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(lookUpService.lookUpRoles(keywordSearch, page, limit)));
    }
}
