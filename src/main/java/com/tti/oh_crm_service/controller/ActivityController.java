package com.tti.oh_crm_service.controller;

import java.util.List;
import java.util.Locale;

import com.tti.oh_crm_service.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tti.oh_crm_service.service.ActivityService;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;

import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import reactor.core.publisher.Mono;

@RestController
@Transactional
@RequestMapping("/crm/activity")
@SecurityRequirement(name = "ohcrm-service")
public class ActivityController {
    @Autowired
    private ActivityService activityService;

    @Autowired
    private JwtService jwtService;

    @GetMapping(path ="/details")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivityView>>> getActivityDetail(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(value = "id") Long id) 
    {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.getActivityDetail(id, accountId, language)));
    }


    @GetMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivityTableResponse>>> getActivities(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "groupValue", required = false) String groupValue,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {

        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.getActivities(filters, groupBy, groupValue, search, sortBy, sortDirection, page, limit, language)));
    }

    @PostMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivityView>>> createActivity(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody CreateActivityRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.createActivity(request, accountId, language)));
    }

    @PutMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivityView>>> updateActivity(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateActivityRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.updateActivity( request, accountId, language )));
    }

    @DeleteMapping(path = "")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<List<Long>>>> deleteActivities(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody List<Long> activityIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.deleteActivities(activityIds)));
    }

    @GetMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivityKanbanResponse>>> getActivitiesKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "groupBy", required = false) String groupBy,
        @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.findKanban(
            filters,
            groupBy,
            search,
            accountId,
            language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateProductInKanban(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestBody UpdateProductInKanbanRequest request
    ) {
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.updateKanbanActivity(request.getId(), request.getFieldName(), request.getFieldValue(), language)));
    }

    @GetMapping(path = "split")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<ActivitySplitViewResponse>>> getActivitiesSplit(
        @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
        @RequestHeader(value = "Organization-Id") Long organizationId,
        @RequestHeader(value = "Accept-Language", required = false) Locale locale,
        @RequestParam(name = "filters", required = false) String filters,
        @RequestParam(name = "search", required = false) String search,
        @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
        @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
        @RequestParam(name = "page", required = false, defaultValue = "0") int page,
        @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.findSplitViewData(
            filters,
            search,
            sortBy,
            sortDirection,
            page,
            limit,
            accountId,
            language
        )));
    }
    
    // api getAttachedActivities
    @GetMapping(path = "attached")
    @Parameters({
        @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<AttachedActivitiesViewResponse>>> getAttachedActivities(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "moduleId") Long moduleId,
            @RequestParam(name = "moduleCode") String moduleCode,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(activityService.getAttachedActivities(moduleId, moduleCode, type, page, limit, accountId, language)));
    }
}
