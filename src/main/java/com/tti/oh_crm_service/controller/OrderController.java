package com.tti.oh_crm_service.controller;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.service.OrderService;
import com.tti.oh_crm_service.service.JwtService;
import com.tti.oh_crm_service.utils.LocaleUtils;
import com.tti.oh_crm_service.utils.TokenUtils;
import io.jsonwebtoken.Claims;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Locale;


@RestController
@Transactional
@RequestMapping("/crm/order")
@SecurityRequirement(name = "ohcrm-service")
@Slf4j
public class OrderController {
    @Autowired
    private JwtService jwtService;
    @Autowired
    private OrderService orderService;


    @GetMapping("")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderTableResponse>>> getOrdersTable(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "groupValue", required = false) String groupValue,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);

        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.getOrdersTable(
                filters,
                groupBy,
                groupValue,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PostMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderView>>> createOrder(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody CreateOrderRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.createOrder(request, accountId, language)));
    }

    @PutMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderView>>> updateOrder(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateOrderRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.updateOrder(request, accountId, language)));
    }

    @DeleteMapping(path = "")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Integer>>> deleteOrders(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody List<Long> orderIds
    ) {
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.deleteOrders(orderIds)));
    }

    @GetMapping(path = "/details")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderDetails>>> getOrderDetails(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "orderId", required = true) Long orderId
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.getOrderDetails(
                orderId,
                accountId,
                language
        )));
    }

    @GetMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderKanbanResponse>>> getOrdersKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "groupBy", required = false) String groupBy,
            @RequestParam(name = "search", required = false) String search
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);

        return Mono.justOrEmpty(ResponseEntity.ok(orderService.getOrdersKanban(
                    filters,
                    groupBy,
                    search,
                    accountId,
                    language
            )));
        
    }

    @GetMapping(path = "split")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<OrderSplitViewResponse>>> getOrdersSplit(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestParam(name = "filters", required = false) String filters,
            @RequestParam(name = "search", required = false) String search,
            @RequestParam(name = "sortBy", required = false, defaultValue = "created_at") String sortBy,
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.getOrdersSplitView(
                filters,
                search,
                sortBy,
                sortDirection,
                page,
                limit,
                accountId,
                language
        )));
    }

    @PutMapping(path = "kanban")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
    })
    public Mono<ResponseEntity<Response<Boolean>>> updateOrderInKanban(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @RequestBody UpdateOrderInKanbanRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.updateKanbanOrder(request.getId(), request.getFieldName(), request.getFieldValue(), accountId, language)));
    }

    @GetMapping(path = "/{orderId}/products")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "orderId", description = "Order ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64")),
            @Parameter(name = "page", description = "Page number (0-based)", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "0")),
            @Parameter(name = "limit", description = "Number of items per page", in = ParameterIn.QUERY, schema = @Schema(type = "integer", defaultValue = "10"))
        })
        public Mono<ResponseEntity<Response<GenericTableResponse<OrderProductView>>>> getProductsForOrder(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable("orderId") Long orderId,
            @RequestParam(name = "page", required = false, defaultValue = "0") int page,
            @RequestParam(name = "limit", required = false, defaultValue = "10") int limit
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.getProductsForOrder(orderId, page, limit, accountId, language)));
    }
    

    @PostMapping(path = "{orderId}/products")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "orderId", description = "Order ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<List<OrderProductView>>>> addProductsToOrder(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable("orderId") Long orderId,
            @RequestBody List<AddProductToOrderRequest> request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.addProductsToOrder(orderId, request, accountId, language)));
    }

    @PutMapping(path = "{orderId}/products")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "orderId", description = "Order ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<OrderProductView>>> updateOrderProduct(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable("orderId") Long orderId,
            @RequestBody UpdateOrderProductRequest request
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.updateOrderProduct(orderId, request, accountId, language)));
    }

    @DeleteMapping(path = "{orderId}/products")
    @Parameters({
            @Parameter(name = "Accept-Language", description = "Language", in = ParameterIn.HEADER, schema = @Schema(type = "string")),
            @Parameter(name = "orderId", description = "Order ID", in = ParameterIn.PATH, required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    public Mono<ResponseEntity<Response<String>>> deleteOrderProducts(
            @Schema(hidden = true) @RequestHeader(value = "Authorization") String authToken,
            @RequestHeader(value = "Organization-Id") Long organizationId,
            @RequestHeader(value = "Accept-Language", required = false) Locale locale,
            @PathVariable("orderId") Long orderId,
            @RequestBody List<Long> orderProductIds
    ) {
        Claims claims = TokenUtils.getToken(jwtService, authToken);
        long accountId = TokenUtils.getAccountId(claims);
        String language = LocaleUtils.getLanguage(locale);
        return Mono.justOrEmpty(ResponseEntity.ok(orderService.deleteOrderProducts(orderId, orderProductIds, accountId, language)));
    }
}
