package com.tti.oh_crm_service.utils;

import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * Utility class for transaction management and synchronization
 * Provides reusable transaction synchronization functionality across modules
 */
@Slf4j
public class TransactionUtils {

    /**
     * Register a transaction synchronization to clear contexts after transaction completion
     * This is useful for cleaning up thread-local contexts like UserContext and EntityContext
     * 
     * @param entityName Optional entity name for logging purposes
     */
    public static void registerContextCleanupAfterTransaction(String entityName) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCompletion(int status) {
                    try {
                        UserContext.clear();
                        EntityContext.clear();
                        
                        String statusText = getTransactionStatusText(status);
                        log.info("Cleared UserContext and EntityContext after transaction completion for {} (status: {})", 
                                 entityName != null ? entityName : "entity", statusText);
                    } catch (Exception e) {
                        log.warn("Error clearing contexts after transaction completion: {}", e.getMessage());
                    }
                }
            });
            
        } else {
            log.warn("Transaction synchronization not active, cannot register context cleanup for: {}", 
                    entityName != null ? entityName : "entity");
        }
    }

    /**
     * Register a transaction synchronization to clear contexts after transaction completion
     * Overloaded method without entity name
     */
    public static void registerContextCleanupAfterTransaction() {
        registerContextCleanupAfterTransaction(null);
    }

    /**
     * Register a custom action to execute after transaction commit
     * 
     * @param action The action to execute after successful commit
     * @param actionDescription Description of the action for logging
     */
    public static void registerAfterCommitAction(Runnable action, String actionDescription) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    try {
                        action.run();
                    } catch (Exception e) {
                        log.error("Error executing after-commit action '{}': {}", actionDescription, e.getMessage(), e);
                    }
                }
            });
        } else {
            log.warn("Transaction synchronization not active, executing action immediately: {}", actionDescription);
            try {
                action.run();
            } catch (Exception e) {
                log.error("Error executing immediate action '{}': {}", actionDescription, e.getMessage(), e);
            }
        }
    }

    /**
     * Register a custom action to execute after transaction completion (regardless of success/failure)
     * 
     * @param action The action to execute after transaction completion
     * @param actionDescription Description of the action for logging
     */
    public static void registerAfterCompletionAction(Runnable action, String actionDescription) {
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCompletion(int status) {
                    try {
                        String statusText = getTransactionStatusText(status);
                        log.info("Executing after-completion action: {} (transaction status: {})", 
                                 actionDescription, statusText);
                        action.run();
                    } catch (Exception e) {
                        log.error("Error executing after-completion action '{}': {}", actionDescription, e.getMessage(), e);
                    }
                }
            });
            
        } else {
            log.warn("Transaction synchronization not active, executing action immediately: {}", actionDescription);
            try {
                action.run();
            } catch (Exception e) {
                log.error("Error executing immediate action '{}': {}", actionDescription, e.getMessage(), e);
            }
        }
    }

    /**
     * Check if transaction synchronization is currently active
     * 
     * @return true if synchronization is active, false otherwise
     */
    public static boolean isTransactionSynchronizationActive() {
        return TransactionSynchronizationManager.isSynchronizationActive();
    }

    /**
     * Get human-readable transaction status text
     */
    private static String getTransactionStatusText(int status) {
        return switch (status) {
            case TransactionSynchronization.STATUS_COMMITTED -> "COMMITTED";
            case TransactionSynchronization.STATUS_ROLLED_BACK -> "ROLLED_BACK";
            case TransactionSynchronization.STATUS_UNKNOWN -> "UNKNOWN";
            default -> "UNDEFINED(" + status + ")";
        };
    }
}
