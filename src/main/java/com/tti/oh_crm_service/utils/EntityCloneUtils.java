package com.tti.oh_crm_service.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import jakarta.persistence.ManyToOne;
import java.lang.reflect.Field;

/**
 * Utility class for cloning entities to avoid Hibernate proxy and transaction conflicts
 * Provides generic entity cloning functionality for history tracking and other use cases
 */
@Slf4j
public class EntityCloneUtils {

    /**
     * Clone an entity with proper deep cloning to avoid Hibernate conflicts
     * This creates a detached copy with manually copied relationships to prevent shared references
     * 
     * @param original The original entity to clone
     * @param entityClass The class type of the entity
     * @param excludeProperties Properties to exclude from cloning (e.g., relationships, audit fields)
     * @return Cloned entity instance
     */
    @SuppressWarnings("unchecked")
    public static <T> T cloneEntity(T original, Class<T> entityClass, String... excludeProperties) {
        if (original == null) {
            return null;
        }
        
        try {
            // Create new instance of the entity
            T clone = entityClass.getDeclaredConstructor().newInstance();
            
            // Default exclude properties for most entities
            // including audit fields and @OneToMany collections
            String[] defaultExcludes = {
                "createdBy", "lastModifiedBy", "createdAt", "lastModifiedAt",
                "orderProducts", "opportunityRoles", "contactRoles", "profilePermissionModuleLinks"
            };
            
            // Combine default excludes with custom excludes
            String[] allExcludes = combineArrays(defaultExcludes, excludeProperties);
            
            // Use BeanUtils to copy properties, excluding specified ones
            BeanUtils.copyProperties(original, clone, allExcludes);
            
            log.debug("Successfully cloned entity: {} with ID: {}", 
                     entityClass.getSimpleName(), getEntityId(clone));
            
            return clone;
            
        } catch (Exception e) {
            log.error("Error cloning entity of type: {} - {}", entityClass.getSimpleName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Clone an entity with default exclusions (relationships and audit fields)
     * 
     * @param original The original entity to clone
     * @param entityClass The class type of the entity
     * @return Cloned entity instance
     */
    public static <T> T cloneEntity(T original, Class<T> entityClass) {
        return cloneEntity(original, entityClass, new String[0]);
    }

    /**
     * Clone an entity specifically for history tracking purposes
     * Properly handles @ManyToOne relationships by ensuring they are accessible
     *
     * @param original The original entity to clone
     * @return Cloned entity instance
     */
    @SuppressWarnings("unchecked")
    public static <T> T cloneEntityForHistory(T original) {
        if (original == null) {
            return null;
        }

        Class<T> entityClass = (Class<T>) original.getClass();

        try {
            // Create new instance of the entity
            T clone = entityClass.getDeclaredConstructor().newInstance();

            // Get all fields including inherited ones
            Field[] allFields = getAllFieldsArray(entityClass);

            for (Field field : allFields) {
                // Skip static fields and audit fields
                if (java.lang.reflect.Modifier.isStatic(field.getModifiers()) ||
                    isAuditField(field.getName()) ||
                    isCollectionField(field)) {
                    continue;
                }

                field.setAccessible(true);
                Object value = field.get(original);

                // For @ManyToOne relationships, ensure we can access the entity
                // This forces Hibernate to load the proxy if needed
                if (field.isAnnotationPresent(ManyToOne.class) && value != null) {
                    try {
                        // Try to access the ID to ensure the proxy is initialized
                        Field idField = findIdField(value.getClass());
                        if (idField != null) {
                            idField.setAccessible(true);
                            Object id = idField.get(value);
                            log.debug("Cloning @ManyToOne field '{}' with ID: {}", field.getName(), id);
                        }
                    } catch (Exception e) {
                        log.debug("Could not access @ManyToOne field '{}' during cloning: {}",
                                 field.getName(), e.getMessage());
                        // If we can't access it, set to null to avoid issues
                        value = null;
                    }
                }

                // Set the value in the clone
                field.set(clone, value);
            }

            log.debug("Successfully cloned entity for history: {} with ID: {}",
                     entityClass.getSimpleName(), getEntityId(clone));

            return clone;

        } catch (Exception e) {
            log.error("Error cloning entity for history: {} - {}", entityClass.getSimpleName(), e.getMessage(), e);
            // Fallback to regular cloning
            return cloneEntity(original, entityClass);
        }
    }

    /**
     * Combine two string arrays
     */
    private static String[] combineArrays(String[] array1, String[] array2) {
        if (array1 == null) array1 = new String[0];
        if (array2 == null) array2 = new String[0];
        
        String[] combined = new String[array1.length + array2.length];
        System.arraycopy(array1, 0, combined, 0, array1.length);
        System.arraycopy(array2, 0, combined, array1.length, array2.length);
        
        return combined;
    }

    /**
     * Extract entity ID using reflection (assumes 'id' field exists)
     */
    private static Object getEntityId(Object entity) {
        if (entity == null) {
            return null;
        }
        
        try {
            java.lang.reflect.Field idField = findIdField(entity.getClass());
            if (idField != null) {
                idField.setAccessible(true);
                return idField.get(entity);
            }
        } catch (Exception e) {
            log.debug("Could not extract ID from entity: {}", entity.getClass().getSimpleName());
        }
        
        return null;
    }

    /**
     * Find the ID field in entity hierarchy
     */
    private static java.lang.reflect.Field findIdField(Class<?> clazz) {
        while (clazz != null && clazz != Object.class) {
            for (java.lang.reflect.Field field : clazz.getDeclaredFields()) {
                if (field.getName().equals("id") ||
                    field.isAnnotationPresent(jakarta.persistence.Id.class)) {
                    return field;
                }
            }
            clazz = clazz.getSuperclass();
        }
        return null;
    }

    /**
     * Get all fields from a class and its superclasses as array
     */
    private static Field[] getAllFieldsArray(Class<?> clazz) {
        java.util.List<Field> fields = new java.util.ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(java.util.Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields.toArray(new Field[0]);
    }

    /**
     * Check if a field name is an audit field
     */
    private static boolean isAuditField(String fieldName) {
        return fieldName.equals("createdBy") || fieldName.equals("lastModifiedBy") ||
               fieldName.equals("createdAt") || fieldName.equals("lastModifiedAt");
    }

    /**
     * Check if a field is a collection field that should be skipped
     */
    private static boolean isCollectionField(Field field) {
        Class<?> fieldType = field.getType();
        return java.util.Collection.class.isAssignableFrom(fieldType) ||
               field.isAnnotationPresent(jakarta.persistence.OneToMany.class) ||
               field.isAnnotationPresent(jakarta.persistence.ManyToMany.class) ||
               field.getName().endsWith("List") || field.getName().endsWith("Set") ||
               field.getName().endsWith("Collection");
    }
}
