package com.tti.oh_crm_service.utils;

public class CodeGeneratorUtils {
    public static String generateListCode(String moduleCode, String fieldName) {
        String genListCode = "LST_" + moduleCode.toUpperCase() + "_" + fieldName.toUpperCase();
        return genListCode;
    }

    public static String mappingModuleCodeToTableName(String moduleCode) {
        String tableName = "";
        switch (moduleCode) {
            case "LEAD":
                tableName = "leads";
                break;

            case "OPPORTUNITY":
                tableName = "opportunities";
                break;

            case "ACCOUNT":
                tableName = "accounts";
                break;

            case "CONTACT":
                tableName = "contacts";
                break;

            case "CAMPAIGN":
                tableName = "campaigns";
                break;
            
            case "ORDERS":
                tableName = "orders";
                break;
            
            case "CONTRACT":
                tableName = "contracts";
                break;

            case "QUOTE":
                tableName = "quotes";
                break;

            case "ACTIVITY":
                tableName = "activities";
                break;
        
            default:
                break;
        }

        return tableName;
    }
}
