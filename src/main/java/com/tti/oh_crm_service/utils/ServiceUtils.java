package com.tti.oh_crm_service.utils;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.tti.oh_crm_service.entity.LookUpView;
import com.tti.oh_crm_service.entity.UpdateLeadRequest;
import com.tti.oh_crm_service.entity.UpdateOrderRequest;
import com.tti.oh_crm_service.enumeration.EFieldType;
import com.tti.oh_crm_service.enumeration.EHistoryActionType;
import com.tti.oh_crm_service.model.Account;
import com.tti.oh_crm_service.model.Campaign;
import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.Contract;
import com.tti.oh_crm_service.model.History;
import com.tti.oh_crm_service.model.Lead;
import com.tti.oh_crm_service.model.Opportunity;
import com.tti.oh_crm_service.model.Order;
import com.tti.oh_crm_service.model.Product;
import com.tti.oh_crm_service.model.Quote;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.AccountRepository;
import com.tti.oh_crm_service.repository.CampaignRepository;
import com.tti.oh_crm_service.repository.ContactRepository;
import com.tti.oh_crm_service.repository.ContractRepository;
import com.tti.oh_crm_service.repository.LeadRepository;
import com.tti.oh_crm_service.repository.OpportunityRepository;
import com.tti.oh_crm_service.repository.OrderRepository;
import com.tti.oh_crm_service.repository.ProductRepository;
import com.tti.oh_crm_service.repository.QuoteRepository;

@Component("serviceUtils")
public class ServiceUtils {
    @Autowired
    private LeadRepository leadRepository;
    @Autowired
    private ContactRepository contactRepository;
    @Autowired
    private AccountRepository accountRepository;
    @Autowired
    private OpportunityRepository opportunityRepository;
    @Autowired
    private CampaignRepository campaignRepository;
    @Autowired
    private OrderRepository orderRepository;
    @Autowired
    private ProductRepository productRepository;
    @Autowired
    private QuoteRepository quoteRepository;
    @Autowired
    private ContractRepository contractRepository;

    public LookUpView getLookUpViewById(String moduleCode, Long id) {
        switch (moduleCode) {
            case "LEAD":
                Optional<Lead> optLead = leadRepository.findById(id);
                if (optLead.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optLead.get().getId());
                    lookUpView.setName(optLead.get().getFirstName() + " " + optLead.get().getLastName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "CONTACT":
                Optional<Contact> optContact = contactRepository.findById(id);
                if (optContact.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optContact.get().getId());
                    lookUpView.setName(optContact.get().getFirstName() + " " + optContact.get().getLastName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "ACCOUNT":
                Optional<Account> optAccount = accountRepository.findById(id);
                if (optAccount.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optAccount.get().getId());
                    lookUpView.setName(optAccount.get().getName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "OPPORTUNITY":
                Optional<Opportunity> optOpportunity = opportunityRepository.findById(id);
                if (optOpportunity.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optOpportunity.get().getId());
                    lookUpView.setName(optOpportunity.get().getName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "CAMPAIGN":
                Optional<Campaign> optCampaign = campaignRepository.findById(id);
                if (optCampaign.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optCampaign.get().getId());
                    lookUpView.setName(optCampaign.get().getName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "ORDER":
                Optional<Order> optOrder = orderRepository.findById(id);
                if (optOrder.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optOrder.get().getId());
                    lookUpView.setName(optOrder.get().getCode());
                    return lookUpView;
                } else {
                    return null;
                }
            case "PRODUCT":
                Optional<Product> optProduct = productRepository.findById(id);
                if (optProduct.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optProduct.get().getId());
                    lookUpView.setName(optProduct.get().getName());
                    return lookUpView;
                } else {
                    return null;
                }
            case "QUOTE":
                Optional<Quote> optQuote = quoteRepository.findById(id);
                if (optQuote.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optQuote.get().getId());
                    lookUpView.setName(optQuote.get().getCode());
                    return lookUpView;
                } else {
                    return null;
                }
            case "CONTRACT":
                Optional<Contract> optContract = contractRepository.findById(id);
                if (optContract.isPresent()) {
                    LookUpView lookUpView = new LookUpView();
                    lookUpView.setId(optContract.get().getId());
                    lookUpView.setName(optContract.get().getContractNumber());
                    return lookUpView;
                } else {
                    return null;
                }
            default:
                return null;
        }
    }

    public History buildLeadHistory(Lead lead, UpdateLeadRequest request, User user) {
        // Build to history
        History history = new History();
        history.setActionType(EHistoryActionType.UPDATE);
        history.setRelatedToModule("LEAD");
        history.setRelatedToId(lead.getId());
        if (user != null) {
            history.setUser(user);
        }
        if (request.getSalutation() != null) {
            history.setFieldOriginalValue(lead.getSalutation());
            history.setFieldNewValue(request.getSalutation());
            history.setFieldType(EFieldType.LIST);
            history.setFieldName("salutation");
        }
        if (request.getFirstName() != null) {
            history.setFieldOriginalValue(lead.getFirstName());
            history.setFieldNewValue(request.getFirstName());
            history.setFieldType(EFieldType.ONE_LINE);
            history.setFieldName("first_name");
        }
        if (request.getLastName() != null) {
            history.setFieldOriginalValue(lead.getLastName());
            history.setFieldNewValue(request.getLastName());
            history.setFieldType(EFieldType.ONE_LINE);
            history.setFieldName("last_name");
        }
        if (request.getEmail() != null) {
            history.setFieldOriginalValue(lead.getEmail());
            history.setFieldNewValue(request.getEmail());
            history.setFieldType(EFieldType.EMAIL);
            history.setFieldName("email");
        }
        if (request.getMobile() != null) {
            history.setFieldOriginalValue(lead.getMobile());
            history.setFieldNewValue(request.getMobile());
            history.setFieldType(EFieldType.PHONE_NUMBER);
            history.setFieldName("mobile");
        }
        if (request.getPhone() != null) {
            history.setFieldOriginalValue(lead.getPhone());
            history.setFieldNewValue(request.getPhone());
            history.setFieldType(EFieldType.PHONE_NUMBER);
            history.setFieldName("phone");
        }
        return history;
    }

    public History buildOrderHistory(Order lead, UpdateOrderRequest request, User user) {
        // Build to history
        History history = new History();
        history.setActionType(EHistoryActionType.UPDATE);
        history.setRelatedToModule("ORDER");
        history.setRelatedToId(lead.getId());
        if (user != null) {
            history.setUser(user);
        }
        if (request.getBillingDate() != null) {
            history.setFieldOriginalValue(lead.getBillingDate() != null ? lead.getBillingDate().toString() : "");
            history.setFieldNewValue(request.getBillingDate().toString());
            history.setFieldType(EFieldType.DATE);
            history.setFieldName("billing_date");
        }
        if (request.getCost() != null) {
            history.setFieldOriginalValue(lead.getCost() != null ? lead.getCost().toString() : "");
            history.setFieldNewValue(request.getCost().toString());
            history.setFieldType(EFieldType.CURRENCY);
            history.setFieldName("cost");
        }
        if (request.getDeliveryDate() != null) {
            history.setFieldOriginalValue(lead.getDeliveryDate() != null ? lead.getDeliveryDate().toString() : "");
            history.setFieldNewValue(request.getDeliveryDate().toString());
            history.setFieldType(EFieldType.DATE);
            history.setFieldName("delivery_date");
        }
        if (request.getOrderDate() != null) {
            history.setFieldOriginalValue(lead.getOrderDate() != null ? lead.getOrderDate().toString() : "");
            history.setFieldNewValue(request.getOrderDate().toString());
            history.setFieldType(EFieldType.DATE);
            history.setFieldName("order_date");
        }
        if (request.getStatus() != null) {
            history.setFieldOriginalValue(lead.getStatus());
            history.setFieldNewValue(request.getStatus());
            history.setFieldType(EFieldType.LIST);
            history.setFieldName("status");
        }
        if (request.getType() != null) {
            history.setFieldOriginalValue(lead.getType());
            history.setFieldNewValue(request.getType());
            history.setFieldType(EFieldType.LIST);
            history.setFieldName("type");
        }
        return history;
    }
}
