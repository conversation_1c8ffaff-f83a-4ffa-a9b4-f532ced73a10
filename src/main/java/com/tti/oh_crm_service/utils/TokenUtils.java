package com.tti.oh_crm_service.utils;

import com.tti.oh_crm_service.service.JwtService;

import io.jsonwebtoken.Claims;

public class TokenUtils {
    public static Claims getToken(JwtService jwtService, String authorization) {
        String[] tokenParts = authorization.split(" ");
        Claims claims = jwtService.extractClaims(tokenParts[1].trim());
        return claims;
    }

    public static long getAccountId(Claims claims) {
        long accountId = Long.valueOf(claims.get("accountId").toString());
        return accountId;
    }
}
