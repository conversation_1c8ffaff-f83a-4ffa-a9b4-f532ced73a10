package com.tti.oh_crm_service.utils;

import com.tti.oh_crm_service.entity.MediaFileInfo;
import com.tti.oh_crm_service.entity.PaginatedMediaFiles;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.codec.multipart.FilePart;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.ZoneId;
import java.util.Date;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.Comparator;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

@Slf4j
public class FileUtils {
    
    // Media file extensions (images and videos)
    private static final List<String> MEDIA_EXTENSIONS = Arrays.asList(
        ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg", ".tiff", ".tif",
        ".mp4", ".avi", ".mov", ".wmv", ".flv", ".webm", ".mkv", ".m4v", ".3gp"
    );
    
    // Media content types
    private static final List<String> MEDIA_CONTENT_TYPES = Arrays.asList(
        "image/", "video/"
    );
    
    // Maximum file size (100MB)
    private static final long MAX_FILE_SIZE = 100 * 1024 * 1024;

    /**
     * Determines if a file is a media file (image or video)
     * @param contentType The content type from the file part
     * @param fileName The original filename
     * @return true if the file is a media file
     */
    public static boolean isMediaFile(String contentType, String fileName) {
        // Check content type first
        if (contentType != null) {
            for (String mediaType : MEDIA_CONTENT_TYPES) {
                if (contentType.startsWith(mediaType)) {
                    return true;
                }
            }
        }
        
        // Fallback to file extension check
        if (fileName != null) {
            String lowerFileName = fileName.toLowerCase();
            return MEDIA_EXTENSIONS.stream()
                .anyMatch(lowerFileName::endsWith);
        }
        
        return false;
    }
    
    /**
     * Gets the file category (media or file)
     * @param contentType The content type
     * @param fileName The filename
     * @return "media" or "file"
     */
    public static String getFileCategory(String contentType, String fileName) {
        return isMediaFile(contentType, fileName) ? "media" : "file";
    }
    
    /**
     * Validates filename for security and format
     * @param fileName The filename to validate
     * @return null if valid, error message if invalid
     */
    public static String validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "Filename cannot be empty";
        }
        
        // Check for dangerous characters
        if (fileName.contains("..") || fileName.contains("/") || fileName.contains("\\")) {
            return "Filename contains invalid characters: " + fileName;
        }
        
        // Check filename length
        if (fileName.length() > 255) {
            return "Filename too long: " + fileName;
        }
        
        return null;
    }
    
    /**
     * Validates file size
     * @param fileSize The file size in bytes
     * @return null if valid, error message if invalid
     */
    public static String validateFileSize(long fileSize) {
        if (fileSize <= 0) {
            return "File is empty";
        }
        
        if (fileSize > MAX_FILE_SIZE) {
            return "File size exceeds maximum allowed size of " + formatFileSize(MAX_FILE_SIZE);
        }
        
        return null;
    }
    
    /**
     * Gets the file extension from filename
     * @param fileName The filename
     * @return The file extension including the dot, or empty string if no extension
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        
        return fileName.substring(lastDotIndex);
    }
    
    /**
     * Gets the base filename without extension
     * @param fileName The filename
     * @return The base filename without extension
     */
    public static String getBaseFileName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return fileName;
        }
        
        return fileName.substring(0, lastDotIndex);
    }
    
    /**
     * Generates a unique filename using the format: originalName_timestamp.extension
     * @param originalFileName The original filename
     * @return A unique filename
     */
    public static String generateUniqueFileName(String originalFileName) {
        String fileExtension = getFileExtension(originalFileName);
        String baseFileName = getBaseFileName(originalFileName);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
        
        return baseFileName + "_" + timestamp + fileExtension;
    }
    
    /**
     * Creates the upload directory structure for a module
     * @param module The module name (e.g., PLAYBOOK, LEAD)
     * @param category The file category ("media" or "file")
     * @return The created directory path
     */
    public static Path createModuleDirectory(String module, String category) {
        // Use absolute path resolution to handle Docker container environment
        Path uploadsDir = resolveUploadsDirectory();
        Path categoryDir = uploadsDir.resolve(category + "s"); // "medias" or "files"
        Path moduleDir = categoryDir.resolve(module.toUpperCase());

        try {
            log.info("Creating directory structure: {} (absolute: {})", moduleDir, moduleDir.toAbsolutePath());

            // Check base uploads directory permissions first
            if (!Files.exists(uploadsDir)) {
                log.error("Base uploads directory does not exist: {}", uploadsDir.toAbsolutePath());
                throw new IOException("Base uploads directory does not exist: " + uploadsDir.toAbsolutePath());
            }

            // Create category directory if it doesn't exist
            if (!Files.exists(categoryDir)) {
                log.info("Creating category directory: {}", categoryDir.toAbsolutePath());
                try {
                    Files.createDirectory(categoryDir);
                } catch (IOException e) {
                    log.error("Failed to create category directory {}: {}", categoryDir.toAbsolutePath(), e.getMessage());
                    throw new IOException("Failed to create category directory: " + e.getMessage(), e);
                }
            }

            // Create module directory if it doesn't exist
            if (!Files.exists(moduleDir)) {
                log.info("Creating module directory: {}", moduleDir.toAbsolutePath());
                try {
                    Files.createDirectory(moduleDir);
                } catch (IOException e) {
                    log.error("Failed to create module directory {}: {}", moduleDir.toAbsolutePath(), e.getMessage());
                    throw new IOException("Failed to create module directory: " + e.getMessage(), e);
                }
            }

            log.info("Successfully ensured directory structure exists: {}", moduleDir.toAbsolutePath());

            // Verify the directory is writable
            if (!Files.isWritable(moduleDir)) {
                throw new IOException("Directory is not writable: " + moduleDir.toAbsolutePath());
            }

            return moduleDir;
        } catch (IOException e) {
            log.error("Failed to create directory structure {} (absolute: {}): {}",
                moduleDir, moduleDir.toAbsolutePath(), e.getMessage(), e);
            throw new RuntimeException("Failed to create upload directory: " + e.getMessage() +
                ". Please run the deploy script to pre-create directories with proper permissions.", e);
        }
    }

    /**
     * Resolves the uploads directory path, handling both local development and Docker container environments
     * @return The resolved uploads directory path
     */
    private static Path resolveUploadsDirectory() {
        // First try the current working directory (for local development)
        Path localUploads = Paths.get("uploads");

        // Check if we're running in Docker container (working directory is /app)
        String workingDir = System.getProperty("user.dir");
        log.debug("Current working directory: {}", workingDir);

        if (workingDir != null && workingDir.equals("/app")) {
            // We're in Docker container, use the mounted volume path
            Path dockerUploads = Paths.get("/app/uploads");
            log.debug("Detected Docker environment, using: {}", dockerUploads.toAbsolutePath());
            return dockerUploads;
        } else {
            // Local development environment
            log.debug("Using local development path: {}", localUploads.toAbsolutePath());
            return localUploads;
        }
    }
    
    /**
     * Saves a FilePart to the specified path
     * @param filePart The file part to save
     * @param targetPath The target path to save to
     * @return The file size in bytes, or -1 if failed
     */
    public static long saveFilePart(FilePart filePart, Path targetPath) {
        try (OutputStream outputStream = Files.newOutputStream(targetPath, StandardOpenOption.CREATE, StandardOpenOption.WRITE)) {
            
            // Collect all data buffers and write them synchronously
            DataBuffer dataBuffer = DataBufferUtils.join(filePart.content()).block();
            if (dataBuffer != null) {
                try (InputStream inputStream = dataBuffer.asInputStream()) {
                    inputStream.transferTo(outputStream);
                } finally {
                    DataBufferUtils.release(dataBuffer);
                }
            }

            // Get and return file size
            long fileSize = Files.size(targetPath);
            log.debug("Successfully saved file: {} (size: {} bytes)", targetPath, fileSize);
            return fileSize;
        } catch (Exception e) {
            log.error("Failed to save file {}: {}", targetPath, e.getMessage());
            return -1L;
        }
    }
    
    /**
     * Gets the content type from FilePart, with fallback to empty string
     * @param filePart The file part
     * @return The content type or empty string
     */
    public static String getContentType(FilePart filePart) {
        return filePart.headers().getContentType() != null ? 
            filePart.headers().getContentType().toString() : "";
    }
    
    /**
     * Formats file size in human-readable format
     * @param bytes The file size in bytes
     * @return Formatted file size string
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
    
    /**
     * Generates the public URL for accessing the uploaded file
     * @param category The file category ("media" or "file")
     * @param module The module name
     * @param fileName The saved filename
     * @return The public URL
     */
    public static String generatePublicUrl(String category, String module, String fileName) {
        return String.format("/uploads/%ss/%s/%s", category, module.toUpperCase(), fileName);
    }

    /**
     * Lists media files from the specified module directory with pagination
     * @param module The module name
     * @param page The page number (0-based)
     * @param limit The number of items per page
     * @return A paginated list of media file information
     */
    public static PaginatedMediaFiles listMediaFiles(String module, int page, int limit) {
        if (module == null || module.trim().isEmpty()) {
            throw new IllegalArgumentException("Module cannot be null or empty");
        }

        if (page < 0) {
            throw new IllegalArgumentException("Page number cannot be negative");
        }

        if (limit <= 0) {
            throw new IllegalArgumentException("Limit must be positive");
        }

        try {
            // Get the medias directory for the module
            Path uploadsDir = resolveUploadsDirectory();
            Path mediasDir = uploadsDir.resolve("medias");
            Path moduleDir = mediasDir.resolve(module.toUpperCase());

            log.info("Looking for media files in directory: {}", moduleDir.toAbsolutePath());

            // Check if directory exists
            if (!Files.exists(moduleDir) || !Files.isDirectory(moduleDir)) {
                log.info("Module directory does not exist: {}", moduleDir.toAbsolutePath());
                return new PaginatedMediaFiles(List.of(), 0, page, limit, 0);
            }

            // List all files in the directory and filter for media files
            List<MediaFileInfo> allMediaFiles;
            try (Stream<Path> files = Files.list(moduleDir)) {
                allMediaFiles = files
                    .filter(Files::isRegularFile)
                    .filter(path -> {
                        String fileName = path.getFileName().toString();
                        return isMediaFile(null, fileName); // Use filename-based detection
                    })
                    .map(path -> createMediaFileInfo(path, module))
                    .filter(info -> info != null) // Filter out any files that couldn't be processed
                    .sorted(Comparator.comparing(MediaFileInfo::getTimestamp).reversed()) // Sort by timestamp descending (newest first)
                    .collect(Collectors.toList());
            }

            log.info("Found {} media files in module {}", allMediaFiles.size(), module);

            // Calculate pagination
            int totalElements = allMediaFiles.size();
            int totalPages = (int) Math.ceil((double) totalElements / limit);
            int startIndex = page * limit;
            int endIndex = Math.min(startIndex + limit, totalElements);

            // Get the page subset
            List<MediaFileInfo> pageContent = startIndex < totalElements ?
                allMediaFiles.subList(startIndex, endIndex) : List.of();

            return new PaginatedMediaFiles(pageContent, totalElements, page, limit, totalPages);

        } catch (IOException e) {
            log.error("Error listing media files for module {}: {}", module, e.getMessage(), e);
            throw new RuntimeException("Failed to list media files: " + e.getMessage(), e);
        }
    }

    /**
     * Creates MediaFileInfo from a file path
     * @param filePath The path to the file
     * @param module The module name
     * @return MediaFileInfo object or null if file couldn't be processed
     */
    private static MediaFileInfo createMediaFileInfo(Path filePath, String module) {
        try {
            String fileName = filePath.getFileName().toString();
            BasicFileAttributes attrs = Files.readAttributes(filePath, BasicFileAttributes.class);

            // Extract timestamp from filename (format: originalName_yyyyMMdd_HHmmss_SSS.extension)
            Date timestamp = extractTimestampFromFileName(fileName);

            // Get file size
            long fileSize = attrs.size();

            // Determine content type based on file extension
            String contentType = getContentTypeFromFileName(fileName);

            // Generate public URL
            String publicUrl = generatePublicUrl("media", module, fileName);
            String filePathString = String.format("uploads/medias/%s/%s", module.toUpperCase(), fileName);

            return new MediaFileInfo(fileName, filePathString, publicUrl, fileSize, contentType, timestamp);

        } catch (Exception e) {
            log.error("Error processing file {}: {}", filePath, e.getMessage());
            return null;
        }
    }

    /**
     * Extracts timestamp from filename using the pattern: originalName_yyyyMMdd_HHmmss_SSS.extension
     * @param fileName The filename
     * @return The extracted timestamp, or current time if extraction fails
     */
    private static Date extractTimestampFromFileName(String fileName) {
        try {
            // Pattern to match: originalName_yyyyMMdd_HHmmss_SSS.extension
            Pattern pattern = Pattern.compile(".*_(\\d{8})_(\\d{6})_(\\d{3})\\.[^.]+$");
            Matcher matcher = pattern.matcher(fileName);

            if (matcher.matches()) {
                String dateStr = matcher.group(1); // yyyyMMdd
                String timeStr = matcher.group(2); // HHmmss
                String millisStr = matcher.group(3); // SSS

                // Parse the timestamp
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS");
                LocalDateTime localDateTime = LocalDateTime.parse(dateStr + "_" + timeStr + "_" + millisStr, formatter);

                // Convert LocalDateTime to Date
                return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
            }
        } catch (Exception e) {
            log.debug("Could not extract timestamp from filename {}: {}", fileName, e.getMessage());
        }

        // Fallback to current time if extraction fails
        return new Date();
    }

    /**
     * Gets content type based on file extension
     * @param fileName The filename
     * @return The content type
     */
    private static String getContentTypeFromFileName(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();

        // Image types
        switch (extension) {
            case ".jpg":
            case ".jpeg":
                return "image/jpeg";
            case ".png":
                return "image/png";
            case ".gif":
                return "image/gif";
            case ".bmp":
                return "image/bmp";
            case ".webp":
                return "image/webp";
            case ".svg":
                return "image/svg+xml";
            case ".tiff":
            case ".tif":
                return "image/tiff";

            // Video types
            case ".mp4":
                return "video/mp4";
            case ".avi":
                return "video/x-msvideo";
            case ".mov":
                return "video/quicktime";
            case ".wmv":
                return "video/x-ms-wmv";
            case ".flv":
                return "video/x-flv";
            case ".webm":
                return "video/webm";
            case ".mkv":
                return "video/x-matroska";
            case ".m4v":
                return "video/x-m4v";
            case ".3gp":
                return "video/3gpp";

            default:
                return "application/octet-stream";
        }
    }
}
