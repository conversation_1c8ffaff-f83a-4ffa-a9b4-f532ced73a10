package com.tti.oh_crm_service.listener;

import com.tti.oh_crm_service.context.EntityContext;
import com.tti.oh_crm_service.context.UserContext;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.service.HistoryTrackingService;
import com.tti.oh_crm_service.utils.TransactionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.persistence.*;

/**
 * Generic Entity Listener for tracking field changes across all entities
 * Uses HistoryTrackingService for automatic field change detection
 */
@Slf4j
@Component
public class HistoryEntityListener {

    private static HistoryTrackingService historyTrackingService;

    @Autowired
    public void setHistoryTrackingService(HistoryTrackingService historyTrackingService) {
        HistoryEntityListener.historyTrackingService = historyTrackingService;
    }

    /**
     * Called after entity creation - use deferred tracking to avoid transaction conflicts
     */
    @PostPersist
    public void onPostPersist(Object entity) {
        try {
            User currentUser = UserContext.getCurrentUser();
            if (currentUser != null && historyTrackingService != null) {

                // Use deferred tracking for CREATE operations using TransactionUtils
                String actionDescription = "CREATE history tracking for " + entity.getClass().getSimpleName();
                TransactionUtils.registerAfterCommitAction(() -> {
                    historyTrackingService.recordCreateHistoryInNewTransaction(entity, currentUser);
                }, actionDescription);
            } else {
                log.warn("Cannot record CREATE history - missing user context or service for entity: {}",
                        entity.getClass().getSimpleName());
            }
        } catch (Exception e) {
            log.error("Error in HistoryEntityListener.onPostPersist for entity: " +
                     entity.getClass().getSimpleName(), e);
        }
    }

    /**
     * Called after entity update - use EntityContext for original/updated comparison
     * Uses transaction synchronization to defer history saving until after main transaction commits
     */
    @PostUpdate
    public void onPostUpdate(Object entity) {
        try {
            User currentUser = UserContext.getCurrentUser();

            // Check if we have both original and updated entity states in context
            if (EntityContext.hasEntityStates() && currentUser != null && historyTrackingService != null) {
                Object originalEntity = EntityContext.getOriginalEntity();
                Object updatedEntity = EntityContext.getUpdatedEntity();

                // Ensure the updated entity matches the current entity being processed
                if (updatedEntity != null && updatedEntity.getClass().equals(entity.getClass())) {

                    // Defer history tracking until after the main transaction commits using TransactionUtils
                    // This prevents transaction conflicts and ensures the main entity save succeeds first
                    String actionDescription = "UPDATE history tracking for " + entity.getClass().getSimpleName();
                    TransactionUtils.registerAfterCommitAction(() -> {
                        historyTrackingService.recordUpdateHistoryInNewTransaction(originalEntity, updatedEntity, currentUser);
                    }, actionDescription);

                    // Register context cleanup after transaction completion
                    TransactionUtils.registerContextCleanupAfterTransaction(entity.getClass().getSimpleName());
                } else {
                    log.debug("Entity context mismatch or missing for: {}", entity.getClass().getSimpleName());
                    EntityContext.clear();
                }
            } else {
                log.debug("Cannot record UPDATE history - missing context or service for entity: {}",
                        entity.getClass().getSimpleName());
                EntityContext.clear();
            }
        } catch (Exception e) {
            log.error("Error in HistoryEntityListener.onPostUpdate for entity: " +
                     entity.getClass().getSimpleName(), e);
            EntityContext.clear(); // Ensure cleanup even on error
        }
    }

}
