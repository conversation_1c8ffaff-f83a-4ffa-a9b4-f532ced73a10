package com.tti.oh_crm_service.listener;

import com.tti.oh_crm_service.model.Order;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;

public class OrderEntityListener {

    @PrePersist
    @PreUpdate
    public void calculateRemainingCost(Order order) {
        if (order.getCost() == null || order.getPaidCost() == null || order.getCost() < order.getPaidCost()) {
            return;
        }
        order.setRemainingCost(order.getCost() - order.getPaidCost());
    }
}