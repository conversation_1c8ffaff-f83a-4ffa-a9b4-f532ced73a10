package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.TblListRequest;
import com.tti.oh_crm_service.entity.TblListView;
import com.tti.oh_crm_service.model.TblList;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class TblListMapper {

    public abstract TblListView toTblListView(TblList tblList);
    public abstract List<TblListView> toTblListViews(List<TblList> tblLists);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void createOrUpdate(TblListRequest request, @MappingTarget TblList tblList);
}
