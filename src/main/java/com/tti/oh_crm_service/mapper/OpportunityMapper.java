package com.tti.oh_crm_service.mapper;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.Opportunity;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.model.ContactOpportunityRole;
import com.tti.oh_crm_service.repository.AccountRepository;

import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,uses = {UserMapper.class, AccountMapper.class, ContactMapper.class})
public abstract class OpportunityMapper {
    @Autowired
    protected AccountRepository accountRepository;
    
    @Autowired
    protected ContactMapper contactMapper;

    public abstract OpportunityView toOpportunityView(Opportunity opportunity);
    public abstract List<OpportunityView> toOpportunityView(List<Opportunity> opportunities);

    @Mapping(target = "countRelatedContacts", ignore = true)
    @Mapping(target = "countRelatedQuotes", ignore = true)
    @Mapping(target = "attachedContactRoles", ignore = true)
    @Mapping(target = "attachedQuotes", ignore = true)
    public abstract OpportunityDetails toOpportunityDetails(Opportunity opportunity);

    public abstract OpportunityShortView toOpportunityShortView(Opportunity opportunity);
    public abstract List<OpportunityShortView> toOpportunityShortView(List<Opportunity> opportunities);
    public abstract LookUpOpportunityView toLookUpOpportunityView(Opportunity opportunity);
    public abstract List<LookUpOpportunityView> toLookUpOpportunityView(List<Opportunity> opportunities);
    public abstract List<OpportunityAttachedView> toOpportunityAttachedView(List<Opportunity> opportunities);
    public abstract OpportunityAttachedView toOpportunityAttachedView(Opportunity opportunity);
    @Mapping(target = "contact", expression = "java(contactMapper.toContactAttachedView(conditionalMappingContactAttachedView(contactOpportunityRole, attachedIntoContactModule)))")
    @Mapping(target = "opportunity", expression = "java(toOpportunityAttachedView(conditionalMappingOpportunityAttachedView(contactOpportunityRole, attachedIntoContactModule)))")
    public abstract OpportunityContactRoleView toOpportunityContactRoleView(ContactOpportunityRole contactOpportunityRole, Boolean attachedIntoContactModule);
    public List<OpportunityContactRoleView> toOpportunityContactRoleView(List<ContactOpportunityRole> contactOpportunityRoles, Boolean attachedIntoContactModule) {
        return contactOpportunityRoles.stream()
                .map(contactOpportunityRole -> toOpportunityContactRoleView(contactOpportunityRole, attachedIntoContactModule))
                .toList();
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "contactRoles", ignore = true)
    public abstract void create(CreateOpportunityRequest request, @MappingTarget Opportunity opportunity, @Context UserMapStructContext context);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "contactRoles", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateOpportunityRequest request, @MappingTarget Opportunity opportunity, @Context User user);
    
    protected Contact conditionalMappingContactAttachedView(ContactOpportunityRole contactOpportunityRole, Boolean attachedIntoContactModule) {
        if (attachedIntoContactModule) {
            // do not map contact
            return null;
        }
        return contactOpportunityRole.getContact();
    }
    
    protected Opportunity conditionalMappingOpportunityAttachedView(ContactOpportunityRole contactOpportunityRole, Boolean attachedIntoContactModule) {
        if (attachedIntoContactModule) {
            return contactOpportunityRole.getOpportunity();
        }
        return null;
    }

    @AfterMapping
    protected void setCreatedByAndOwner(CreateOpportunityRequest request, @MappingTarget Opportunity opportunity, @Context UserMapStructContext context) {
        opportunity.setCreatedBy(context.getUser());
        opportunity.setLastModifiedBy(context.getUser());
        opportunity.setOwner(context.getOwner());
        if (request.getAccountId() != null) {
            opportunity.setAccount(accountRepository.findById(request.getAccountId()).orElse(null));
        }
    }

    @AfterMapping
    protected void setLastModifiedBy(UpdateOpportunityRequest request, @MappingTarget Opportunity opportunity, @Context User user) {
        opportunity.setLastModifiedBy(user);
    }
}