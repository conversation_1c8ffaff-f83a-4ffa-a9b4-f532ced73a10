package com.tti.oh_crm_service.mapper;

import java.util.List;
import java.util.stream.Collectors;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.repository.AccountRepository;
import com.tti.oh_crm_service.repository.ContactOpportunityRoleRepository;
import org.mapstruct.*;

import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.ContactOpportunityRole;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class, OpportunityMapper.class})
public abstract class ContactMapper {
    @Autowired
    protected AccountRepository accountRepository;

    public abstract ContactView toContactView(Contact contact);
    public abstract List<ContactView> toContactView(List<Contact> contacts);
    public abstract List<CampaignContactView> toCampaignContactView(List<Contact> contacts);
    @Mapping(target = "attachedActivities", ignore = true)
    @Mapping(target = "countRelatedOpportunities", ignore = true)
    @Mapping(target = "countRelatedQuotes", ignore = true)
    @Mapping(target = "attachedOpportunities", ignore = true)
    @Mapping(target = "attachedQuotes", ignore = true)
    public abstract ContactDetails toContactDetails(Contact contact);
    public abstract LookUpContactView toLookUpContactView(Contact contact);
    public abstract List<LookUpContactView> toLookUpContactView(List<Contact> contacts);
    public abstract ContactShortView toContactShortView(Contact contact);
    public abstract List<ContactShortView> toContactShortView(List<Contact> contacts);
    public abstract List<ContactAttachedView> toContactAttachedView(List<Contact> contacts);
    public abstract ContactAttachedView toContactAttachedView(Contact contact); // Added overload

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", expression = "java(concatenateNames(contact.getFirstName(), contact.getLastName()))")
    public abstract LookUpView toLookUpView(Contact contact);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "opportunityRoles", ignore = true)
    public abstract void create(CreateContactRequest request, @MappingTarget Contact contact, @Context UserMapStructContext context);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "account", ignore = true)
    @Mapping(target = "opportunityRoles", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateContactRequest request, @MappingTarget Contact contact, @Context User user);

    @AfterMapping
    protected void setCreatedByAndOwner(CreateContactRequest request, @MappingTarget Contact contact, @Context UserMapStructContext context) {
        contact.setCreatedBy(context.getUser());
        contact.setLastModifiedBy(context.getUser());
        contact.setOwner(context.getOwner());
        if (request.getAccountId() != null) {
            contact.setAccount(accountRepository.findById(request.getAccountId()).orElse(null));
        }
    }

    @AfterMapping
    protected void setLastModifiedBy(UpdateContactRequest request, @MappingTarget Contact contact, @Context User user) {
        contact.setLastModifiedBy(user);
    }

    // Helper method to handle null values and concatenate names properly
    protected String concatenateNames(String firstName, String lastName) {
        String first = firstName != null ? firstName : "";
        String last = lastName != null ? lastName : "";

        if (first.isEmpty() && last.isEmpty()) {
            return "";
        }

        return first.isEmpty()
                ? last
                : last.isEmpty()
                ? first
                : first + " " + last;

    }
}