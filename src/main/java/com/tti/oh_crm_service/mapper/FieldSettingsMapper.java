package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.UpdateFieldSettingsRequest;
import com.tti.oh_crm_service.entity.CreateFieldSettingsRequest;
import com.tti.oh_crm_service.entity.FieldSettingsView;
import com.tti.oh_crm_service.model.FieldSettings;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class FieldSettingsMapper {

    @Mapping(target = "listItems", ignore = true)
    public abstract FieldSettingsView toFieldSettingsView(FieldSettings fieldSettings);
    public abstract List<FieldSettingsView> toFieldSettingsView(List<FieldSettings> fieldSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "language", ignore = true)
    public abstract void create(CreateFieldSettingsRequest request, @MappingTarget FieldSettings fieldSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "language", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateFieldSettingsRequest request, @MappingTarget FieldSettings fieldSettings);
}
