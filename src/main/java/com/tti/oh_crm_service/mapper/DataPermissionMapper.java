package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateDataPermissionRequest;
import com.tti.oh_crm_service.entity.DataPermissionView;
import com.tti.oh_crm_service.model.DataPermission;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class DataPermissionMapper {

    public abstract DataPermissionView toDataPermissionView(DataPermission dataPermission);
    public abstract List<DataPermissionView> toDataPermissionView(List<DataPermission> dataPermissions);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(CreateDataPermissionRequest request, @MappingTarget DataPermission dataPermission);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "moduleCode", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(CreateDataPermissionRequest request, @MappingTarget DataPermission dataPermission);
}
