package com.tti.oh_crm_service.mapper;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.Quote;
import com.tti.oh_crm_service.model.User;

import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {AccountMapper.class, OpportunityMapper.class, ContactMapper.class})
public abstract class QuoteMapper {

    public abstract QuoteView toQuoteView(Quote quote);
    public abstract List<QuoteView> toQuoteView(List<Quote> quotes);
    
    public abstract QuoteDetails toQuoteDetails(Quote quote);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    public abstract LookUpView toLookUpView(Quote quote);
    public abstract List<LookUpView> toLookUpView(List<Quote> quotes);
    
    public abstract QuoteAttachedView toQuoteAttachedView(Quote quote);
    public abstract List<QuoteAttachedView> toQuoteAttachedView(List<Quote> quotes);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "account", ignore = true)
    @Mapping(target = "opportunity", ignore = true)
    @Mapping(target = "contact", ignore = true)
    public abstract void create(CreateQuoteRequest request, @MappingTarget Quote quote, @Context UserMapStructContext context);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "account", ignore = true)
    @Mapping(target = "opportunity", ignore = true)
    @Mapping(target = "contact", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateQuoteRequest request, @MappingTarget Quote quote, @Context User user);

    @AfterMapping
    protected void setCreatedByAndOwner(CreateQuoteRequest request, @MappingTarget Quote quote, @Context UserMapStructContext context) {
        quote.setCreatedBy(context.getUser());
        quote.setLastModifiedBy(context.getUser());
    }

    @AfterMapping
    protected void setLastModifiedBy(UpdateQuoteRequest request, @MappingTarget Quote quote, @Context User user) {
        quote.setLastModifiedBy(user);
    }
}