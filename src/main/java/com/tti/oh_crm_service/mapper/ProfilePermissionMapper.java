package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.ProfilePermissionView;
import com.tti.oh_crm_service.model.ProfilePermissionModuleLink;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class ProfilePermissionMapper {

    public abstract ProfilePermissionView toProfilePermissionView(ProfilePermissionModuleLink profilePermissionModuleLink);
    public abstract List<ProfilePermissionView> toProfilePermissionView(List<ProfilePermissionModuleLink> profilePermissionModuleLinks);
}
