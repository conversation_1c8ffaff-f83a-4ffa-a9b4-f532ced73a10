package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.tti.oh_crm_service.entity.CreateNoteRequest;
import com.tti.oh_crm_service.entity.NoteView;
import com.tti.oh_crm_service.entity.UpdateNoteRequest;
import com.tti.oh_crm_service.model.Note;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class NoteMapper {

    public abstract List<NoteView> toNoteView(List<Note> notes);
    public abstract NoteView toNoteView(Note note);

    @Mapping(target = "id", ignore = true)
    public abstract Note create(CreateNoteRequest request);
    public abstract void update (UpdateNoteRequest request, @MappingTarget Note note);

}