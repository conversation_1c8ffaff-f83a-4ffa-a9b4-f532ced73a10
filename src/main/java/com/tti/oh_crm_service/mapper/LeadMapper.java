package com.tti.oh_crm_service.mapper;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.model.User;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.Lead;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class LeadMapper {

    public abstract LeadView toLeadView(Lead lead);
    public abstract List<LeadView> toLeadView(List<Lead> leads);
    public abstract List<CampaignLeadView> toCampaignLeadView(List<Lead> leads);
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "memberStatus", ignore = true)
    public abstract CampaignMemberView toCampaignMemberView(Lead lead);
    @Mapping(target = "memberStatus", ignore = true)
    public abstract CampaignLeadMemberView toCampaignLeadMemberView(Lead lead);

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "memberStatus", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "status", ignore = true)
    public abstract CampaignMemberView toCampaignMemberView(Contact contact);

    @Mapping(target = "attachedActivities", ignore = true)
    @Mapping(target = "campaigns", ignore = true)
    public abstract LeadDetails toLeadDetails(Lead lead);
    
    public abstract LookUpLeadView toLookUpLeadView(Lead lead);
    public abstract List<LookUpLeadView> toLookUpLeadView(List<Lead> leads);

    @Mapping(target = "name", expression = "java(concatenateNames(lead.getFirstName(), lead.getLastName()))")
    public abstract LookUpView toLookUpView(Lead lead);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    public abstract void create(CreateLeadRequest request, @MappingTarget Lead lead);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateLeadRequest request, @MappingTarget Lead lead);


    // Helper method to handle null values and concatenate names properly
    protected String concatenateNames(String firstName, String lastName) {
        String first = firstName != null ? firstName : "";
        String last = lastName != null ? lastName : "";

        if (first.isEmpty() && last.isEmpty()) {
            return "";
        }

        return first.isEmpty()
                ? last
                : last.isEmpty()
                ? first
                : first + " " + last;

    }
}
