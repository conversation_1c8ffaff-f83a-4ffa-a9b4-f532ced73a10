package com.tti.oh_crm_service.mapper;

import java.util.List;

import com.tti.oh_crm_service.entity.AttachedActivityShortView;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.tti.oh_crm_service.entity.ActivityView;
import com.tti.oh_crm_service.entity.CreateActivityRequest;
import com.tti.oh_crm_service.entity.UpdateActivityRequest;
import com.tti.oh_crm_service.model.Activity;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class ActivityMapper {

    public abstract ActivityView toActivityView(Activity activity);
    public abstract List<ActivityView> toActivityView(List<Activity> activities);
    public abstract AttachedActivityShortView toAttachedActivityShortView(Activity activity);
    public abstract List<AttachedActivityShortView> toAttachedActivityShortViews(List<Activity> activities);

    @Mapping(target = "id", ignore = true)
    public abstract Activity create(CreateActivityRequest request);
    public abstract void update (UpdateActivityRequest request, @MappingTarget Activity activity);

}