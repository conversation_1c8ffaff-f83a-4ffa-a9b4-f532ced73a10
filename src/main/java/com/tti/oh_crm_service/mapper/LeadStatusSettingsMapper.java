package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.LeadStatusSettingsView;
import com.tti.oh_crm_service.entity.UpdateLeadStatusSettingsRequest;
import com.tti.oh_crm_service.model.LeadStatusSettings;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class LeadStatusSettingsMapper {

    public abstract LeadStatusSettingsView toLeadStatusSettingsView(LeadStatusSettings leadStatusSettings);
    public abstract List<LeadStatusSettingsView> toLeadStatusSettingsView(List<LeadStatusSettings> leadStatusSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(UpdateLeadStatusSettingsRequest request, @MappingTarget LeadStatusSettings leadStatusSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void update(UpdateLeadStatusSettingsRequest request, @MappingTarget LeadStatusSettings leadStatusSettings);
}
