package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.tti.oh_crm_service.entity.CreateHistoryRequest;
import com.tti.oh_crm_service.entity.HistoryView;
import com.tti.oh_crm_service.model.History;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class HistoryMapper {

    public abstract List<HistoryView> toHistoryView(List<History> histories);
    public abstract HistoryView toHistoryView(History history);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "user", ignore = true)
    public abstract History create(CreateHistoryRequest request);

}