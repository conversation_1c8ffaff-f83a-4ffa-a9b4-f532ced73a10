package com.tti.oh_crm_service.mapper;

import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.Contract;
import com.tti.oh_crm_service.model.User;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class, ContactMapper.class})
public abstract class ContractMapper {

    public abstract ContractView toContractView(Contract contract);
    public abstract List<ContractView> toContractView(List<Contract> contracts);
    public abstract ContractDetails toContractDetails(Contract contract);
    public abstract LookUpContractView toLookUpContractView(Contract contract);
    public abstract List<LookUpContractView> toLookUpContractView(List<Contract> contracts);

    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", expression = "java(contract.getContractNumber())")
    public abstract LookUpView toLookUpView(Contract contract);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "owner", ignore = true)
    public abstract void create(CreateContractRequest request, @MappingTarget Contract contract, @Context UserMapStructContext context);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateContractRequest request, @MappingTarget Contract contract, @Context User user);

    @AfterMapping
    protected void setCreatedByAndOwner(CreateContractRequest request, @MappingTarget Contract contract, @Context UserMapStructContext context) {
        contract.setCreatedBy(context.getUser());
        contract.setLastModifiedBy(context.getUser());
        contract.setOwner(context.getOwner());
    }

    @AfterMapping
    protected void setLastModifiedBy(UpdateContractRequest request, @MappingTarget Contract contract, @Context User user) {
        contract.setLastModifiedBy(user);
    }
}