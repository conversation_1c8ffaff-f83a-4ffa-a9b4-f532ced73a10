package com.tti.oh_crm_service.mapper;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import org.mapstruct.*;

import com.tti.oh_crm_service.model.Account;

@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE, 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        uses = {UserMapper.class}
)
public abstract class AccountMapper {


    @Mapping( target = "id", ignore = true)
    @Mapping( target = "createdAt", ignore = true)
    @Mapping( target = "lastModifiedAt", ignore = true)
    @Mapping( target = "createdBy", ignore = true)
    @Mapping( target = "lastModifiedBy", ignore = true)
    public abstract Account create(AccountView accountView);

    @Mapping( target = "id", ignore = true)
    @Mapping( target = "createdAt", ignore = true)
    @Mapping( target = "lastModifiedAt", ignore = true)
    @Mapping( target = "createdBy", ignore = true)
    @Mapping( target = "lastModifiedBy", ignore = true)
    public abstract Account create(CreateAccountRequest createAccountRequest);

    /* 
    * because AccountDetails extends AccountView,
    * we need to use @IterableMapping for toAccountViews and name the method toAccountView
    * to avoid ambiguous method error 
    * See more: https://mapstruct.org/faq/#ambiguous
    * */
    @Named("toAccountView")
    public abstract AccountView toAccountView (Account account);
    
    @IterableMapping(qualifiedByName = "toAccountView")
    public abstract List<AccountView> toAccountViews (List<Account> accounts);
    
    public abstract LookUpAccountView toLookUpAccountView(Account account);
    public abstract List<LookUpAccountView> toLookUpAccountView (List<Account> accounts);
    public abstract LookUpView toLookUpView(Account account);
    public abstract AccountShortView toAccountShortView(Account account);
    public abstract List<AccountShortView> toAccountShortViews (List<Account> accounts);
    
    //@BeanMapping(resultType = AccountDetails.class)
    public abstract AccountDetails toAccountDetails(Account account);


    public abstract void updateAccount (UpdateAccountRequest updateAccountRequest, @MappingTarget Account account);
}
