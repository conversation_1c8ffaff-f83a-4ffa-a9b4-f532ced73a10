package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateDataPermissionShareRuleRequest;
import com.tti.oh_crm_service.entity.DataPermissionShareRuleView;
import com.tti.oh_crm_service.entity.UpdateDataPermissionShareRuleRequest;
import com.tti.oh_crm_service.model.DataPermissionShareRule;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class DataPermissionShareRuleMapper {

    @Mapping(target = "shareFor", ignore = true)
    public abstract DataPermissionShareRuleView toDataPermissionShareRuleView(DataPermissionShareRule dataPermissionShareRule);
    @Mapping(target = "shareFor", ignore = true)
    public abstract List<DataPermissionShareRuleView> toDataPermissionShareRuleView(List<DataPermissionShareRuleView> dataPermissionShareRules);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(CreateDataPermissionShareRuleRequest request, @MappingTarget DataPermissionShareRule dataPermissionShareRule);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void update(UpdateDataPermissionShareRuleRequest request, @MappingTarget DataPermissionShareRule dataPermissionShareRule);
}
