package com.tti.oh_crm_service.mapper;

import java.util.List;

import com.tti.oh_crm_service.entity.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.model.Department;
import com.tti.oh_crm_service.model.User;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class UserMapper {

    @Mapping(target = "role", ignore = true)
    @Mapping(target = "profile", ignore = true)
    @Mapping(source = "department", target = "department", qualifiedByName = "departmentToDepartmentView")
    public abstract UserView toUserView(User user);
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "profile", ignore = true)
    @Mapping(source = "department", target = "department", qualifiedByName = "departmentToDepartmentView")
    public abstract List<UserView> toUserView(List<User> users);

    @Mapping(source = "department", target = "department", qualifiedByName = "departmentToDepartmentView")
    public abstract UserDetails toUserDetails(User user);

//    @Mapping(source = "profile", target = "profile", qualifiedByName = "profileToProfileView")
//    @Mapping(source = "role", target = "role", qualifiedByName = "roleToRoleView")
    public abstract LookUpUserView toLookUpUserView(User user);
//    @Mapping(source = "role", target = "role", qualifiedByName = "roleToRoleView")
//    @Mapping(source = "profile", target = "profile", qualifiedByName = "profileToProfileView")
    @Mapping(source = "department", target = "department", qualifiedByName = "departmentToDepartmentView")
    public abstract List<LookUpUserView> toLookUpUserView(List<User> users);
    
    public abstract UserShortView toUserShortView(User user);

 
    @Mapping(target = "name", expression = "java(concatenateNames(user.getFirstName(), user.getLastName()))")
    public abstract LookUpView toLookUpView(User user);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "profile", ignore = true)
    @Mapping(target = "department", ignore = true)
    public abstract void create(CreateUserRequest request, @MappingTarget User user);

    @Mapping(target = "accountId", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "role", ignore = true)
    @Mapping(target = "profile", ignore = true)
    @Mapping(target = "department", ignore = true)
    public abstract void update(UpdateUserRequest request, @MappingTarget User user);

    /*********** CUSTOME MAPPING ************/
//    @Named("roleToRoleView") 
//    public static RoleView roleToShortRole(Role role) { 
//        if (role == null) {
//            return null;
//        }
//        RoleView roleView = new RoleView();
//        roleView.setId(role.getId());
//        roleView.setName(role.getName());
//        roleView.setDescription(role.getDescription());
//        roleView.setShareWithPeers(role.getShareWithPeers());
//        return roleView;
//    }

//    @Named("profileToProfileView") 
//    public static ProfileView profileToProfileView(Profile profile) { 
//        if (profile == null) {
//            return null;
//        }
//        ProfileView profileView = new ProfileView();
//        profileView.setId(profile.getId());
//        profileView.setName(profile.getName());
//        return profileView;
//    }

    @Named("departmentToDepartmentView")
    public static DepartmentView departmentToDepartmentView(Department department) {
        if (department == null) {
            return null;
        }
        DepartmentView departmentView = new DepartmentView();
        departmentView.setId(department.getId());
        departmentView.setName(department.getName());
        departmentView.setOrganizationLevel(department.getOrganizationLevel());
        return departmentView;
    }

    // Helper method to handle null values and concatenate names properly
    protected String concatenateNames(String firstName, String lastName) {
        String first = firstName != null ? firstName : "";
        String last = lastName != null ? lastName : "";

        if (first.isEmpty() && last.isEmpty()) {
            return "";
        }

        return first.isEmpty() 
                ? last 
                : last.isEmpty() 
                    ? first 
                    : first + " " + last;

    }
}
