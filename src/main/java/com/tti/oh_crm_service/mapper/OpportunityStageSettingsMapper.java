package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.OpportunityStageSettingsView;
import com.tti.oh_crm_service.entity.UpdateOpportunityStageSettingsRequest;
import com.tti.oh_crm_service.model.OpportunityStageSettings;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class OpportunityStageSettingsMapper {

    public abstract OpportunityStageSettingsView toOpportunityStageSettingsView(OpportunityStageSettings opportunityStageSettings);
    public abstract List<OpportunityStageSettingsView> toOpportunityStageSettingsView(List<OpportunityStageSettings> opportunityStageSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(UpdateOpportunityStageSettingsRequest request, @MappingTarget OpportunityStageSettings opportunityStageSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void update(UpdateOpportunityStageSettingsRequest request, @MappingTarget OpportunityStageSettings opportunityStageSettings);
}
