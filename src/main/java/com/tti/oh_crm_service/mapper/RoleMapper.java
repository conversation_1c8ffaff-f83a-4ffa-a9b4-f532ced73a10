package com.tti.oh_crm_service.mapper;

import java.util.List;

import com.tti.oh_crm_service.entity.UpdateRoleRequest;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateRoleRequest;
import com.tti.oh_crm_service.entity.RoleShortView;
import com.tti.oh_crm_service.entity.RoleView;
import com.tti.oh_crm_service.model.Role;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class RoleMapper {

    @Mapping(source = "reportTo", target = "reportTo", qualifiedByName = "roleToShortRole")
    public abstract RoleView toRoleView(Role role);
    @Mapping(source = "reportTo", target = "reportTo", qualifiedByName = "roleToShortRole")
    public abstract List<RoleView> toRoleView(List<Role> roles);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "reportTo", ignore = true)
    public abstract void create(CreateRoleRequest request, @MappingTarget Role role);

    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "reportTo", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateRoleRequest request, @MappingTarget Role role);

    /*********** CUSTOME MAPPING ************/
    @Named("roleToShortRole") 
    public static RoleShortView roleToShortRole(Role role) { 
        if (role == null) {
            return null;
        }
        RoleShortView shortRole = new RoleShortView();
        shortRole.setId(role.getId());
        shortRole.setName(role.getName());
        return shortRole;
    }
}
