package com.tti.oh_crm_service.mapper;


import com.tti.oh_crm_service.entity.*;
import com.tti.oh_crm_service.mapstruct.UserMapStructContext;
import com.tti.oh_crm_service.model.Contact;
import com.tti.oh_crm_service.model.Order;
import com.tti.oh_crm_service.model.User;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class, ContactMapper.class, ContractMapper.class, OpportunityMapper.class, AccountMapper.class})
public abstract class OrderMapper {

    public abstract OrderView toOrderView(Order order);
    public abstract List<OrderView> toOrderView(List<Order> orders);
    public abstract OrderDetails toOrderDetails(Order order);
    public abstract OrderShortView toOrderShortView(Order order);
    public abstract List<OrderShortView> toOrderShortView(List<Order> orders);

    public abstract OrderAttachedView toOrderAttachedView(Order order);
    public abstract List<OrderAttachedView> toOrderAttachedView(List<Order> orders);

    public abstract LookUpOrderView toLookUpOrderView(Order order);
    public abstract List<LookUpOrderView> toLookUpOrderView(List<Order> orders);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "owner", ignore = true)
    public abstract void create(CreateOrderRequest request, @MappingTarget Order order, @Context UserMapStructContext context);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "remainingCost", ignore = true)
    @Mapping(target = "contact", ignore = true)
    @Mapping(target = "opportunity", ignore = true)
    @Mapping(target = "contract", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "account", ignore = true)
    @Mapping(target = "orderProducts", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateOrderRequest request, @MappingTarget Order order, @Context User user);

    @AfterMapping
    protected void setCreatedByAndOwner(CreateOrderRequest request, @MappingTarget Order order, @Context UserMapStructContext context) {
        order.setCreatedBy(context.getUser());
        order.setLastModifiedBy(context.getUser());
        order.setOwner(context.getOwner());
    }

    @AfterMapping
    protected void setLastModifiedBy(UpdateOrderRequest request, @MappingTarget Order order, @Context User user) {
        order.setLastModifiedBy(user);
    }
}