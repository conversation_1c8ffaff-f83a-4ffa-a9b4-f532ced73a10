package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CampaignView;
import com.tti.oh_crm_service.entity.CreateCampaignRequest;
import com.tti.oh_crm_service.entity.LeadCampaignView;
import com.tti.oh_crm_service.entity.UpdateCampaignRequest;
import com.tti.oh_crm_service.entity.CampaignDetails;
import com.tti.oh_crm_service.model.Campaign;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {UserMapper.class})
public abstract class CampaignMapper {

    public abstract CampaignView toCampaignView(Campaign campaign);
    public abstract LeadCampaignView toLeadCampaignView(Campaign campaign);
    public abstract List<CampaignView> toCampaignView(List<Campaign> campaigns);
    @Mapping(target = "activities", ignore = true)
    @Mapping(target = "campaigns", ignore = true)
    @Mapping(target = "numberOfResponses", ignore = true)
    @Mapping(target = "numberOfLeads", ignore = true)
    @Mapping(target = "numberOfConvertedLeads", ignore = true)
    @Mapping(target = "numberOfContacts", ignore = true)
    @Mapping(target = "numberOfOpportunities", ignore = true)
    @Mapping(target = "numberOfWonOpportunities", ignore = true)
    @Mapping(target = "valueOfOpportunities", ignore = true)
    @Mapping(target = "valueOfWonOpportunities", ignore = true)
    @Mapping(target = "numberOfDeals", ignore = true)
    @Mapping(target = "numberOfClosedDeals", ignore = true)
    public abstract CampaignDetails toCampaignDetails(Campaign campaign);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "campaignParent", ignore = true)
    public abstract void create(CreateCampaignRequest request, @MappingTarget Campaign campaign);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "owner", ignore = true)
    @Mapping(target = "campaignParent", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateCampaignRequest request, @MappingTarget Campaign campaign);
}
