package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.TblListValueView;
import com.tti.oh_crm_service.model.TblListValue;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class TblListValueMapper {

    public abstract TblListValueView toTblListValueView(TblListValue tblListValue);
    public abstract List<TblListValueView> toTblListValueViews(List<TblListValue> tblListValues);
}
