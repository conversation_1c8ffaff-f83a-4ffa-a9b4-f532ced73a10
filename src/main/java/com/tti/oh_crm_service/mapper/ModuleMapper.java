package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateModuleRequest;
import com.tti.oh_crm_service.entity.ModuleView;
import com.tti.oh_crm_service.model.Module;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class ModuleMapper {

    public abstract ModuleView toModuleView(Module module);
    public abstract List<ModuleView> toModuleView(List<Module> modules);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "code", ignore = true)
    @Mapping(target = "language", ignore = true)
    @Mapping(target = "active", ignore = true)
    @Mapping(target = "position", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(CreateModuleRequest request, @MappingTarget Module module);

    @Mapping(target = "language", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void toModule (ModuleView moduleView, @MappingTarget Module module);
}
