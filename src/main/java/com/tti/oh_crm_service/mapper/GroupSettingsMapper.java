package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateGroupSettingsRequest;
import com.tti.oh_crm_service.entity.LayoutGroupSettingsView;
import com.tti.oh_crm_service.entity.UpdateGroupSettingsRequest;
import com.tti.oh_crm_service.model.GroupSettings;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class GroupSettingsMapper {

    public abstract LayoutGroupSettingsView toLayoutGroupSettingsView(GroupSettings layoutGroupSettings);
    public abstract List<LayoutGroupSettingsView> toLayoutGroupSettingsView(List<GroupSettings> layoutGroupSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "language", ignore = true)
    public abstract void create(CreateGroupSettingsRequest request, @MappingTarget GroupSettings groupSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "language", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateGroupSettingsRequest request, @MappingTarget GroupSettings groupSettings);
}
