package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.CreateDepartmentRequest;
import com.tti.oh_crm_service.entity.DepartmentDetails;
import com.tti.oh_crm_service.entity.UpdateDepartmentRequest;
import com.tti.oh_crm_service.entity.DepartmentView;
import com.tti.oh_crm_service.entity.DepartmentViewTreeNode;
import com.tti.oh_crm_service.model.Department;
import com.tti.oh_crm_service.model.User;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = { UserMapper.class })
public abstract class DepartmentMapper {
    public abstract DepartmentView toDepartmentView(Department department);
    public abstract List<DepartmentView> toDepartmentView(List<Department> departments);

    @Mapping(target = "organizationLevelLabel", ignore = true)
    public abstract DepartmentDetails toDepartmentDetails(Department department);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "parentDepartment", ignore = true)
    @Mapping(target = "departmentHead", ignore = true)
    public abstract void create(CreateDepartmentRequest request, @MappingTarget Department department, @Context User user);

    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @Mapping(target = "parentDepartment", ignore = true)
    @Mapping(target = "departmentHead", ignore = true)
    public abstract void update(UpdateDepartmentRequest request, @MappingTarget Department department, @Context User user);

    @Mapping(target = "children", ignore = true)
    @Mapping(target = "parentDepartmentId", source = "parentDepartment.id")
    public abstract DepartmentViewTreeNode toDepartmentViewTreeNode(Department department);

    @AfterMapping
    protected void setAuditUser(CreateDepartmentRequest request, @MappingTarget Department department, @Context User user) {
        department.setCreatedBy(user);
        department.setLastModifiedBy(user);
    }

    @AfterMapping
    protected void setAuditUser(UpdateDepartmentRequest request, @MappingTarget Department department, @Context User user) {
        department.setLastModifiedBy(user);
    }
}
