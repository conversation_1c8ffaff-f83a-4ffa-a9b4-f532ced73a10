package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import com.tti.oh_crm_service.entity.UpdateLayoutSettingsRequest;
import com.tti.oh_crm_service.entity.CreateLayoutSettingsRequest;
import com.tti.oh_crm_service.entity.LayoutSettingsView;
import com.tti.oh_crm_service.model.LayoutSettings;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class LayoutSettingsMapper {

    public abstract LayoutSettingsView toLayoutSettingsView(LayoutSettings layoutSettings);
    public abstract List<LayoutSettingsView> toLayoutSettingsView(List<LayoutSettings> layoutSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void create(CreateLayoutSettingsRequest request, @MappingTarget LayoutSettings layoutSettings);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    public abstract void update(UpdateLayoutSettingsRequest request, @MappingTarget LayoutSettings layoutSettings);
}
