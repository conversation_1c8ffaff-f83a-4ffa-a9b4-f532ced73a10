package com.tti.oh_crm_service.mapper;

import com.tti.oh_crm_service.entity.OrderProductView;
import com.tti.oh_crm_service.model.OrderProduct;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, uses = {ProductMapper.class})
public abstract class OrderProductMapper {
    @Mapping(target = "orderId", source = "order.id")
    public abstract OrderProductView toOrderProductView(OrderProduct orderProduct);

    public abstract List<OrderProductView> toOrderProductView(List<OrderProduct> orderProducts);
}
