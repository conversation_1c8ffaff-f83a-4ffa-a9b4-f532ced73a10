package com.tti.oh_crm_service.mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.tti.oh_crm_service.entity.ProductConversionView;
import com.tti.oh_crm_service.model.ProductConversion;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class ProductConversionMapper {

    
    public abstract List<ProductConversion> toProductConversion(List<ProductConversionView> productConversionViews);

    public abstract List<ProductConversionView> toProductConversionView(List<ProductConversion> productConversions);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "productId", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    public abstract ProductConversion toProductConversion(ProductConversionView productConversionView);
}