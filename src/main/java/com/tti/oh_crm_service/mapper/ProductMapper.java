package com.tti.oh_crm_service.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import com.tti.oh_crm_service.entity.CreateProductRequest;
import com.tti.oh_crm_service.entity.ProductAttachedView;
import com.tti.oh_crm_service.entity.ProductView;
import com.tti.oh_crm_service.entity.UpdateProductRequest;
import com.tti.oh_crm_service.model.Product;

@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public abstract class ProductMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract Product toProduct(CreateProductRequest request);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    public abstract List<ProductView> toProductView(List<Product> products);

    public abstract ProductView toProductView( Product product);

    public abstract ProductAttachedView toProductAttachedView(Product product);


    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastModifiedAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "lastModifiedBy", ignore = true)
    public abstract void updateProduct(UpdateProductRequest request, @MappingTarget Product product);
}
