package com.tti.oh_crm_service.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.OpportunityStageSettings;

@Repository
public interface OpportunityStageSettingsRepository extends JpaRepository<OpportunityStageSettings, Long> {
    Optional<OpportunityStageSettings> findByOpportunityStageValue(String opportunityStageValue);
}