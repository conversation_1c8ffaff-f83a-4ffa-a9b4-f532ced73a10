package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.FieldSettings;

@Repository
public interface FieldSettingsRepository extends CoreStoreProcedureRepository<FieldSettings> {
    List<FieldSettings> findByModuleCodeAndLanguage(String moduleCode, String language);
    Optional<FieldSettings> findByModuleCodeAndNameAndLanguage(String moduleCode, String name, String language);
    Integer deleteByModuleCodeAndName(String moduleCode, String name);
}