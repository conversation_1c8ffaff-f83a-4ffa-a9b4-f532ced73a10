package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.PlaybookToolSection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PlaybookToolSectionRepository extends JpaRepository<PlaybookToolSection, Long> {
    List<PlaybookToolSection> findByPlaybookToolIdAndIsInTrashFalse(Long playbookToolId);
    List<PlaybookToolSection> findByPlaybookToolId(Long playbookToolId);
}
