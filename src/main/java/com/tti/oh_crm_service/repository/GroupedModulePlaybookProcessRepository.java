package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.GroupedModulePlaybookProcess;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface GroupedModulePlaybookProcessRepository extends JpaRepository<GroupedModulePlaybookProcess, Long> {
    Optional<GroupedModulePlaybookProcess> findByModuleCode(String moduleCode);
}
