package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.Opportunity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface OpportunityRepository extends CoreStoreProcedureRepository<Opportunity> {
    Integer deleteByIdIn(List<Long> ids);
    List<Opportunity> findByAccountId(Long accountId);
    Page<Opportunity> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
    Long countByAccountId(Long accountId);
    Page<Opportunity> findByAccountId(Long accountId, Pageable pageable);
}
