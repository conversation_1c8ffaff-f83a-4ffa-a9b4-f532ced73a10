package com.tti.oh_crm_service.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.DataPermission;

@Repository
public interface DataPermissionRepository extends JpaRepository<DataPermission, Long> {
    Optional<DataPermission> findByModuleCode(String moduleCode);
}
