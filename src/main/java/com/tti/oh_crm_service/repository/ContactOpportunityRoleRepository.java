package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.ContactOpportunityRole;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContactOpportunityRoleRepository extends JpaRepository<ContactOpportunityRole, Long> {

    long countByContactId(Long contactId);

    long countByOpportunityId(Long opportunityId);

    @Query("SELECT cor FROM ContactOpportunityRole cor JOIN FETCH cor.opportunity o WHERE cor.contactId = :contactId")
    List<ContactOpportunityRole> findByContactIdWithOpportunity(@Param("contactId") Long contactId, Pageable pageable);

    @Query("SELECT cor FROM ContactOpportunityRole cor JOIN FETCH cor.contact c WHERE cor.opportunityId = :opportunityId")
    List<ContactOpportunityRole> findByOpportunityIdWithContact(@Param("opportunityId") Long opportunityId, Pageable pageable);

    // check if (contactId + opportunityId + role) exists
    Boolean existsByContactIdAndOpportunityIdAndRole(Long contactId, Long opportunityId, String role);
}
