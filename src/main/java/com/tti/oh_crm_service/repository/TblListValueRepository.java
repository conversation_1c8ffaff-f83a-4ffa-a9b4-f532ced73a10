package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.TblListValue;

@Repository
public interface TblListValueRepository extends JpaRepository<TblListValue, Long> {
    Optional<TblListValue> findByListCodeAndValueAndLanguage(String listCode, String value, String language);
    Optional<TblListValue> findByListCodeAndLabelAndLanguage(String listCode, String label, String language);
    List<TblListValue> findByListCodeAndLanguageOrderByPositionAsc(String listCode, String language);
    List<TblListValue> findByListCodeAndLanguageOrderByLabelAsc(String listCode, String language);
    Boolean existsByListCodeAndLanguage(String listCode, String language);
    Integer deleteByListCode(String listCode);
    @Query("SELECT st.id as id, \n" + //
                "\ttbl_list.label as lead_status_label, \n" + //
                "\ttbl_list.value as lead_status_value, \n" + //
                "\ttbl_list.position as lead_status_position,\n" + //
                "    st.winRate,\n" + //
                "    st.forecastType,\n" + //
                "    st.forecastCategory,\n" + //
                "    st.color,\n" + //
                "    st.keyFields\n" + //
                "FROM TblListValue as tbl_list JOIN LeadStatusSettings as st ON tbl_list.value = st.leadStatusValue where listCode=\"LST_LEAD_STATUS\"")
    List<Object[]> findLeadStatusSettings();
    @Query("SELECT st.id as id, \n" + //
                "\ttbl_list.label as opportunity_stage_label, \n" + //
                "\ttbl_list.value as opportunity_stage_value, \n" + //
                "\ttbl_list.position as opportunity_stage_position,\n" + //
                "    st.winRate,\n" + //
                "    st.forecastType,\n" + //
                "    st.forecastCategory,\n" + //
                "    st.color,\n" + //
                "    st.keyFields\n" + //
                "FROM TblListValue as tbl_list JOIN OpportunityStageSettings as st ON tbl_list.value = st.opportunityStageValue where listCode=\"LST_OPPORTUNITY_STAGE\"")
    List<Object[]> findOpportunityStageSettings();
}