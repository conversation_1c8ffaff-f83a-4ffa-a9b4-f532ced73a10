package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.PlaybookTool;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PlaybookToolRepository extends JpaRepository<PlaybookTool, Long> {
    Page<PlaybookTool> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
}
