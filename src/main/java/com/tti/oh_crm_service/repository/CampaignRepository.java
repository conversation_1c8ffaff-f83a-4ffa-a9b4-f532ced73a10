package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.entity.ICampaignShortView;
import com.tti.oh_crm_service.model.Campaign;

@Repository
public interface CampaignRepository extends CoreStoreProcedureRepository<Campaign> {
    Integer deleteByIdIn(List<Long> ids);
    List<ICampaignShortView> getCampaignById(Long id);
    Page<Campaign> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
}
