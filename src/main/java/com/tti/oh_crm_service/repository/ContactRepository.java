package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.Contact;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ContactRepository extends CoreStoreProcedureRepository<Contact> {
    Integer deleteByIdIn(List<Long> ids);

    // find by full name ignore case
    @Query("SELECT c FROM Contact c WHERE CONCAT(c.lastName, ' ', c.firstName) ILIKE %:keyword%")
    Page<Contact> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
    List<Contact> findByAccountId(Long accountId);
    Long countByAccountId(Long accountId);
    Page<Contact> findByAccountId(Long accountId, Pageable pageable);
    
}
