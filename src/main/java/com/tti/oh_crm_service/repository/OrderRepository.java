package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.Order;


import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface OrderRepository extends CoreStoreProcedureRepository<Order> {
    Integer deleteByIdIn(List<Long> ids);
    List<Order> findByAccountId(Long accountId);
    Page<Order> findByCodeContainingIgnoreCase(String keyword, Pageable pageable);

    Long countByContractId(Long contractId);
    Page<Order> findByContractIdOrderByLastModifiedAtDesc(Long contractId, Pageable pageable);
}
