package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.GroupSettings;

@Repository
public interface GroupSettingsRepository extends JpaRepository<GroupSettings, Long> {
    List<GroupSettings> findByModuleCodeAndLanguage(String moduleCode, String language);
    Optional<GroupSettings> findByModuleCodeAndCodeAndLanguage(String moduleCode, String code, String language);
}