package com.tti.oh_crm_service.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.query.Procedure;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.data.repository.query.Param;

import java.util.List;

@NoRepositoryBean
public interface CoreStoreProcedureRepository<T> extends JpaRepository<T, Long> {
    @Procedure("GET_GROUP_VALUES")
    List<Object[]> getGroupValues(@Param("PARAMS_JSON") String json);

    @Procedure("GET_ALLS_BY_FILTER")
    List<T> findByFilter(@Param("FILTERS_JSON") String json);

    @Procedure("COUNT_ALLS_BY_FILTER")
    List<Long> countByFilter(@Param("FILTERS_JSON") String json);

    @Procedure("GET_KANBAN_DATA")
    List<Object[]> findKanban(@Param("DATA_JSON") String json);

    @Procedure("GET_SPLIT_VIEW_DATA")
    List<Object[]> findSplitViewData(@Param("DATA_JSON") String json);

    @Procedure("UPDATE_KANBAN_DATA")
    void updateKanbanData(@Param("DATA_JSON") String json);

    @Procedure("COUNT_DATA_RELATED_LIST_VALUE")
    List<Long> countDataRelatedListValue(@Param("PARAMS_JSON") String json);

    @Procedure("GROUP_BY_ACTIVITY")
    List<Object[]> groupByActivity(@Param("DATA_JSON") String json);

    @Procedure("COUNT_ALL")
    List<Long> countAll(@Param("FILTERS_JSON") String json);
}
