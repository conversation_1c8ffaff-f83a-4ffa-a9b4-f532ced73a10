package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Filter;

@Repository
public interface FilterRepository extends JpaRepository<Filter, Long> {
    List<Filter> findByModuleCodeAndUserId(String moduleCode, Long userId);
    List<Filter> findByNameLikeAndModuleCodeAndUserId(String search, String moduleCode, Long userId);
}
