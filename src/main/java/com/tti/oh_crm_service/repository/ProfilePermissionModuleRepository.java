package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.enumeration.EPermission;
import com.tti.oh_crm_service.model.ProfilePermissionModuleLink;

@Repository
public interface ProfilePermissionModuleRepository extends JpaRepository<ProfilePermissionModuleLink, Long> {
    List<ProfilePermissionModuleLink> findByProfileId (Long profileId);
    Optional<ProfilePermissionModuleLink> findByProfileIdAndModuleCodeAndPermission(Long profileId, String moduleCode, EPermission permission);
}