package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.PlaybookToolHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PlaybookToolHistoryRepository extends JpaRepository<PlaybookToolHistory, Long> {
    List<PlaybookToolHistory> findByPlaybookToolIdOrderByHistoryDateDesc(Long playbookToolId);
    Page<PlaybookToolHistory> findByPlaybookToolIdOrderByHistoryDateDesc(Long playbookToolId, Pageable pageable);
}
