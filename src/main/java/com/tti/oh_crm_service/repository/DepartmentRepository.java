package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Department;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long> {

    List<Department> findByOrganizationLevel(Integer organizationLevel);

    List<Department> findByOrganizationLevelOrderByName(Integer organizationLevel);

    List<Department> findByNameContainingIgnoreCase(String name);
    
    List<Department> findByParentDepartment(Department parentDepartment);
    
    boolean existsByParentDepartment(Department parentDepartment);
}
