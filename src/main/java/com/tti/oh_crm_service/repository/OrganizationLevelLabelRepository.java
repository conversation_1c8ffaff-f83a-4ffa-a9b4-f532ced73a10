package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.OrganizationLevelLabel;
import com.tti.oh_crm_service.model.OrganizationLevelLabelId;

@Repository
public interface OrganizationLevelLabelRepository extends JpaRepository<OrganizationLevelLabel, OrganizationLevelLabelId> {
    // When using @EmbeddedId, we need to reference the embedded fields using the correct path.
    // To find by language, we use findByIdLanguage.
    List<OrganizationLevelLabel> findByIdLanguage(String language);

    // To find by level, we use findByIdLevel.
    List<OrganizationLevelLabel> findByIdLevel(Integer level);

    Optional<OrganizationLevelLabel> findByIdLevelAndIdLanguage(Integer level, String language);

    List<OrganizationLevelLabel> findByIdLanguageOrderByIdLevel(String language);
}
