package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Role;
import com.tti.oh_crm_service.model.User;
import com.tti.oh_crm_service.model.Department;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Boolean existsByAccountId(Long accountId);
    Optional<User> findByAccountId(Long accountId);
    List<User> findByRole(Role role);
    
    @Query("SELECT u FROM User u WHERE CONCAT(u.lastName, ' ', u.firstName) LIKE %:keyword%")
    Page<User> findByKeyword(@Param("keyword") String keyword, Pageable pageable);

    Page<User> findByRoleId(Long roleId, Pageable pageable);
    
    boolean existsByDepartment(Department department);
    
    List<User> findByDepartment(Department department);
}
