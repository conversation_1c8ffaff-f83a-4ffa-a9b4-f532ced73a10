package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.Account;
import com.tti.oh_crm_service.model.Contract;
import com.tti.oh_crm_service.model.Opportunity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ContractRepository extends CoreStoreProcedureRepository<Contract> {
    Integer deleteByIdIn(List<Long> ids);
    List<Contract> findByAccountId(Long accountId);
    Page<Contract> findByContractNumberContainingIgnoreCase(String keyword, Pageable pageable);
}
