package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.enumeration.EListType;
import com.tti.oh_crm_service.model.TblList;

@Repository
public interface TblListRepository extends JpaRepository<TblList, Long> {
    List<TblList> findByTypeAndLanguage(EListType type, String language);
    List<TblList> findByLanguage(String language);
    Boolean existsByTypeAndLanguage(EListType type, String language);
    Boolean existsByTypeAndCodeAndLanguage(EListType type, String code, String language);
    Integer deleteByTypeAndCode(EListType type, String code);
}