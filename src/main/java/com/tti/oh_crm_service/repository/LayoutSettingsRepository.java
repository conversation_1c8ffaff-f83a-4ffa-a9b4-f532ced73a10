package com.tti.oh_crm_service.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.LayoutSettings;

@Repository
public interface LayoutSettingsRepository extends JpaRepository<LayoutSettings, Long> {
    Optional<LayoutSettings> findByModuleCode(String moduleCode);
}