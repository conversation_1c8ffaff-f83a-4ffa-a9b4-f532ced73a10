package com.tti.oh_crm_service.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.LeadStatusSettings;

@Repository
public interface LeadStatusSettingsRepository extends JpaRepository<LeadStatusSettings, Long> {
    Optional<LeadStatusSettings> findByLeadStatusValue(String leadStatusValue);
}