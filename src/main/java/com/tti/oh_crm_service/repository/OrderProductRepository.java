package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.OrderProduct;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderProductRepository extends JpaRepository<OrderProduct, Long> {
    Long countByOrderId(Long orderId);

    @Query("SELECT op FROM OrderProduct op JOIN FETCH op.product o WHERE op.orderId = :orderId")
    List<OrderProduct> findByOrderIdWithProduct(@Param("orderId") Long orderId, Pageable pageable);

    @Query("SELECT COUNT(op) > 0 FROM OrderProduct op WHERE op.order.id = :orderId AND op.product.id = :productId")
    boolean existsByOrderIdAndProductId(@Param("orderId") Long orderId, @Param("productId") Long productId);
}
