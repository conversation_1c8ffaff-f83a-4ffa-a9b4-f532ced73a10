package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.tti.oh_crm_service.model.History;


public interface HistoryRepository extends CoreStoreProcedureRepository<History> {
    Integer deleteByIdIn(List<Long> ids);
    Page<History> findByRelatedToIdAndRelatedToModule(Long relatedToId, String relatedToModule, Pageable pageable);

    @Query(value = """
        SELECT
            h.id,
            h.field_name,
            h.field_type,
            h.field_original_value,
            h.field_new_value,
            h.related_to_module,
            h.action_type,
            h.history_date,
            u.id as user_id,
            u.first_name as user_first_name,
            u.last_name as user_last_name,
            orig_val.label as field_original_value_label,
            new_val.label as field_new_value_label
        FROM histories h
        LEFT JOIN users u ON h.user_id = u.id
        LEFT JOIN field_settings fs ON (
            fs.module_code = h.related_to_module
            AND fs.name = h.field_name
            AND fs.field_type = h.field_type
            AND fs.language = :language
        )
        LEFT JOIN tbl_list_values orig_val ON (
            h.field_type = 'LIST'
            AND fs.list_code = orig_val.list_code
            AND h.field_original_value = orig_val.value
            AND orig_val.language = :language
        )
        LEFT JOIN tbl_list_values new_val ON (
            h.field_type = 'LIST'
            AND fs.list_code = new_val.list_code
            AND h.field_new_value = new_val.value
            AND new_val.language = :language
        )
        WHERE h.related_to_id = :relatedToId
        AND h.related_to_module = :relatedToModule
        ORDER BY :sortBy :sortDirection
        """,
        countQuery = """
        SELECT COUNT(h.id)
        FROM histories h
        WHERE h.related_to_id = :relatedToId
        AND h.related_to_module = :relatedToModule
        """,
        nativeQuery = true)
    Page<Object[]> findHistoriesWithFieldLabels(
        @Param("relatedToId") Long relatedToId,
        @Param("relatedToModule") String relatedToModule,
        @Param("language") String language,
        @Param("sortBy") String sortBy,
        @Param("sortDirection") String sortDirection,
        Pageable pageable
    );

    @Query(value = """
        SELECT
            h.id,
            h.field_name,
            h.field_type,
            h.field_original_value,
            h.field_new_value,
            h.related_to_module,
            h.action_type,
            h.history_date,
            u.id as user_id,
            u.first_name as user_first_name,
            u.last_name as user_last_name,
            orig_val.label as field_original_value_label,
            new_val.label as field_new_value_label
        FROM histories h
        LEFT JOIN users u ON h.user_id = u.id
        LEFT JOIN field_settings fs ON (
            fs.module_code = h.related_to_module
            AND fs.name = h.field_name
            AND fs.field_type = h.field_type
            AND fs.language = :language
        )
        LEFT JOIN tbl_list_values orig_val ON (
            h.field_type = 'LIST'
            AND fs.list_code = orig_val.list_code
            AND h.field_original_value = orig_val.value
            AND orig_val.language = :language
        )
        LEFT JOIN tbl_list_values new_val ON (
            h.field_type = 'LIST'
            AND fs.list_code = new_val.list_code
            AND h.field_new_value = new_val.value
            AND new_val.language = :language
        )
        WHERE h.id = :historyId
        """,
        nativeQuery = true)
    List<Object[]> findHistoryWithFieldLabelsById(
        @Param("historyId") Long historyId,
        @Param("language") String language
    );
}
