package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Module;

@Repository
public interface ModuleRepository extends JpaRepository<Module, Long> {
    List<Module> findByLanguage(String language);

    Optional<Module> findByLanguageAndId(String language, Long id);
    
}
