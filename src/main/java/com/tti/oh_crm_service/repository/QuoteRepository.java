package com.tti.oh_crm_service.repository;

import com.tti.oh_crm_service.model.Quote;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface QuoteRepository extends CoreStoreProcedureRepository<Quote> {
    Integer deleteByIdIn(List<Long> ids);
    List<Quote> findByAccountId(Long accountId);
    Page<Quote> findByNameContainingIgnoreCase(String keyword, Pageable pageable);

    // Added for fetching quotes related to an Opportunity
    long countByOpportunityId(Long opportunityId);
    Page<Quote> findByOpportunityIdOrderByLastModifiedAtDesc(Long opportunityId, Pageable pageable);

    // Added for fetching quotes related to a Contact (as contact)
    long countByContactId(Long contactId);
    Page<Quote> findByContactIdOrderByLastModifiedAtDesc(Long contactId, Pageable pageable);
}
