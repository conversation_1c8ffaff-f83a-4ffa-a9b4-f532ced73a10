package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import com.tti.oh_crm_service.model.Note;


public interface NoteRepository extends CoreStoreProcedureRepository<Note> {
    Integer deleteByIdIn(List<Long> ids);
    Page<Note> findByRelatedToIdAndRelatedToModule(Long relatedToId, String relatedToModule, Pageable pageable);
}
