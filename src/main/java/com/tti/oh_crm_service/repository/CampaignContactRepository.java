package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.CampaignContact;

@Repository
public interface CampaignContactRepository extends JpaRepository<CampaignContact, Long> {
    Optional<CampaignContact> findByCampaignIdAndContactId(Long campaignId, Long contactId);
    List<CampaignContact> findByCampaignId(Long campaignId);
    List<CampaignContact> findByContactId(Long contactId);
}
