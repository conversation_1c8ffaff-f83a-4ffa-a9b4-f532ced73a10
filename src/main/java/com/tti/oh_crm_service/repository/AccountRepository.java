package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Account;

@Repository
public interface AccountRepository extends CoreStoreProcedureRepository<Account> {
    Page<Account> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
    @Query("SELECT COUNT(*) FROM CampaignContact cc WHERE cc.contactId in (SELECT c.id FROM Contact c WHERE c.account.id = :account_id)")
    List<Long> countCampaignsByAccountId(@Param("account_id") Long accountId);
}
