package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Lead;

@Repository
public interface LeadRepository extends CoreStoreProcedureRepository<Lead> {
   Integer deleteByIdIn(List<Long> ids);
   // find by full name ignore case
   @Query("SELECT c FROM Lead c WHERE CONCAT(c.lastName, ' ', c.firstName) ILIKE %:keyword%")
   Page<Lead> findByKeyword(@Param("keyword") String keyword, Pageable pageable);
}