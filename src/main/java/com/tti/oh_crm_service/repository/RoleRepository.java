package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.Role;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    Boolean existsByName(String name);
    Page<Role> findByNameContainingIgnoreCase(String keyword, Pageable pageable);
    List<Role> findByReportTo(Role role);
}
