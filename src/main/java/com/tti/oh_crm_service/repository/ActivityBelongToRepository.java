package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.ActivityBelongTo;

@Repository
public interface ActivityBelongToRepository extends JpaRepository<ActivityBelongTo, Long> {
    List<ActivityBelongTo> findByActivityId(Long activityId);

    Optional<ActivityBelongTo> findByActivityIdAndBelongToModuleAndBelongToId(Long activityId, String belongToModule, Long belongToId);

    List<ActivityBelongTo> findByActivityIdAndBelongToModule(Long activityId, String belongToModule);
}
