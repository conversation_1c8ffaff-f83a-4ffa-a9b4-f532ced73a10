package com.tti.oh_crm_service.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.DataPermissionShareFor;

@Repository
public interface DataPermissionShareForRepository extends JpaRepository<DataPermissionShareFor, Long> {
    List<DataPermissionShareFor> findByRuleId(Long ruleId);
}
