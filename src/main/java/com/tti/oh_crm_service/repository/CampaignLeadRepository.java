package com.tti.oh_crm_service.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.tti.oh_crm_service.model.CampaignLead;

@Repository
public interface CampaignLeadRepository extends JpaRepository<CampaignLead, Long> {
    Optional<CampaignLead> findByCampaignIdAndLeadId(Long campaignId, Long leadId);
    List<CampaignLead> findByCampaignId(Long campaignId);
    List<CampaignLead> findByLeadId(Long leadId);
    Page<CampaignLead> findByCampaignId(Long campaignId, Pageable pageable);
}
