package com.tti.oh_crm_service.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.tti.oh_crm_service.model.Activity;

import java.util.List;

public interface ActivityRepository extends CoreStoreProcedureRepository<Activity> {
    // Page<Activity> findByBelongToIdAndBelongToModule(Long belongToId, String belongToModule, Pageable pageable);

    Page<Activity> findByRelatedToIdAndRelatedToModule(Long relatedToId, String relatedToModule, Pageable pageable);

    // Page<Activity> findByBelongToIdAndBelongToModuleAndType(Long belongToId, String belongToModule, String type, Pageable pageable);
    Page<Activity> findByRelatedToIdAndRelatedToModuleAndType(Long relatedToId, String relatedToModule, String type, Pageable pageable);
    
    // overload for 4 methods above with return type List<Activity>
    @Query("SELECT act \n" + //
                "FROM Activity as act \n" + //
                "JOIN ActivityBelongTo act_bl \n" + //
                "ON act.id = act_bl.activityId\n" + //
                "WHERE act_bl.belongToModule = :belongToModule \n" + //
                "    AND act_bl.belongToId = :belongToId")
    List<Activity> findByBelongToIdAndBelongToModule(@Param("belongToId") Long belongToId, @Param("belongToModule") String belongToModule);
    List<Activity> findByRelatedToIdAndRelatedToModule(Long relatedToId, String relatedToModule);
    @Query("SELECT act \n" + //
                "FROM Activity as act \n" + //
                "JOIN ActivityBelongTo act_bl \n" + //
                "ON act.id = act_bl.activityId\n" + //
                "WHERE act.type = :type \n" + //
                "\tAND act_bl.belongToModule = :belongToModule\n" + //
                "    AND act_bl.belongToId = :belongToId")
    List<Activity> findByBelongToIdAndBelongToModuleAndType(
        @Param("belongToId") Long belongToId, 
        @Param("belongToModule") String belongToModule, 
        @Param("type") String type
    );
    List<Activity> findByRelatedToIdAndRelatedToModuleAndType(Long relatedToId, String relatedToModule, String type);
}
