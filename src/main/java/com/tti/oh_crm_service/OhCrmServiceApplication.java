package com.tti.oh_crm_service;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;

import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import reactivefeign.spring.config.EnableReactiveFeignClients;

@SecurityScheme(name = "ohcrm-service", scheme = "bearer", type = SecuritySchemeType.HTTP, in = SecuritySchemeIn.HEADER, bearerFormat = "JWT")
@EnableReactiveFeignClients(basePackages = {"com.tti.oh_crm_service"})
@EnableFeignClients(basePackages = {"com.tti.oh_crm_service"})
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
public class OhCrmServiceApplication {

	public static void main(String[] args) {
		SpringApplication.run(OhCrmServiceApplication.class, args);
	}

}
