server {
        listen 80;
        server_name id-dev.ttis.vn;
        location / {
            proxy_pass   http://id-web:8000;
        }
}

server {
        listen 80;
        server_name ohcrm-dev.ttis.vn;
        location / {
            proxy_pass   http://ohcrm-web:8001;
        }
}

server {
        listen 80;
        server_name api-dev.ttis.vn;

        # Serve uploaded files (images, videos, documents, etc.) directly from nginx
        location /uploads/ {
            root /var/www;
            try_files $uri =404;

            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";

            # Handle CORS preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, OPTIONS";
                add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type "text/plain charset=UTF-8";
                add_header Content-Length 0;
                return 204;
            }
        }

        # Proxy all other requests to API Gateway
        location / {
            proxy_pass   http://api-gateway:4001;
        }
}
