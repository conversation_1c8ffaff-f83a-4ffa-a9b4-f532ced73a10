spring.application.name=api-gateway

server.port=4001

spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
spring.servlet.multipart.max-file-size=300MB

# Max request size.
spring.servlet.multipart.max-request-size=300MB
spring.cloud.gateway.httpclient.connect-timeout=900000
spring.cloud.gateway.httpclient.response-timeout=900s

spring.cloud.gateway.globalcors.add-to-simple-url-handler-mapping=true
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowedOriginPatterns=*
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowedMethods=*
spring.cloud.gateway.globalcors.corsConfigurations.[/**].allowCredentials=true

management.security.enabled=false
management.endpoints.web.exposure.include=*

eureka.instance.preferIpAddress=true
eureka.client.register-with-eureka=true
eureka.client.fetch-registry=true
eureka.client.service-url.defaultZone=http://services-discovery:4000/eureka/

tti.security.jwt.secret-key=24dd6d4e44ed1832d6f60acc7dc92eb047647602bb5a1cb3642d43807b43efe0
# 1 day in millisecond
tti.security.jwt.expiration-time=********

# Routes configuration
spring.cloud.gateway.routes[0].id=tti_id_service
spring.cloud.gateway.routes[0].uri=lb://ID-SERVICE/
spring.cloud.gateway.routes[0].predicates[0]=Path=/account/**

spring.cloud.gateway.routes[1].id=oh_crm_service
spring.cloud.gateway.routes[1].uri=lb://OH-CRM-SERVICE/
spring.cloud.gateway.routes[1].predicates[0]=Path=/crm/**

spring.cloud.gateway.routes[2].id=common_service
spring.cloud.gateway.routes[2].uri=lb://COMMON-SERVICE/
spring.cloud.gateway.routes[2].predicates[0]=Path=/common/**
