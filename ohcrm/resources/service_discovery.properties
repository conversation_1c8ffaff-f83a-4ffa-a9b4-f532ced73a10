spring.application.name=services-discovery
server.port=4000

eureka.instance.hostname=**************

eureka.client.register-with-eureka=false
eureka.client.fetch-registry=false
eureka.client.service-url.defaultZone=http://${eureka.instance.hostname}:${server.port}/eureka/
eureka.client.enabled=true
eureka.server.wait-time-in-ms-when-sync-empty=5

logging.level.com.netflix.eureka=OFF
logging.level.com.netflix.discovery=OFF
