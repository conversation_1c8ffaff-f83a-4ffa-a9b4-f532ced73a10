CREATE DEFINER=`ohcrm`@`%` PROCEDURE `GROUP_BY_ACTIVITY`(IN DATA_JSON JSON)
BEGIN
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE MODULE_CODE VARCHAR(50);
    DECLARE NO_ACTIVITY_SQL TEXT;
    DECLARE IDLE_SQL TEXT;
    DECLARE UPCOMING_SQL TEXT;
    DECLARE NO_UPCOMING_SQL TEXT;
    DECLARE OVERDUE_SQL TEXT;
    DECLARE DUE_TODAY_SQL TEXT;
    DECLARE MAIN_SQL TEXT;

    -- SET INPUT = '{"module_code":"PRODUCT", "table_name":"products"}';

    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.table_name'));
    SET MODULE_CODE = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.module_code'));
    
    -- No Activity: Module object with no completed activities.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @NO_ACTIVITY_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status = 'FINISH'
                    )
            ) = 0");
    ELSE
        SET @NO_ACTIVITY_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status = 'FINISH'
            ) = 0");
    END IF;

    -- Idle: Module object with past activity but no completed activities in the last 30 days.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @IDLE_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status = 'FINISH'
                            AND deadline IS NOT NULL
                            AND DATE(created_at) >= DATE(NOW()) - INTERVAL 30 DAY
                    )
            ) = 0");
    ELSE
        SET @IDLE_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status = 'FINISH'
                    AND deadline IS NOT NULL
                    AND DATE(created_at) >= DATE(NOW()) - INTERVAL 30 DAY
            ) = 0");
    END IF;
    
    -- Upcoming: Leads with activities due in the next 30 days.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @UPCOMING_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status <> 'FINISH'
                            AND deadline IS NOT NULL
                            AND DATE(deadline) >= DATE(NOW()) 
                            AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                    )
            ) <> 0");
    ELSE
        SET @UPCOMING_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status <> 'FINISH'
                    AND deadline IS NOT NULL
                    AND DATE(deadline) >= DATE(NOW()) 
                    AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
            ) <> 0");
    END IF;

    -- No Upcoming: Leads with activities due in the next 30 days.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @NO_UPCOMING_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status <> 'FINISH'
                            AND deadline IS NOT NULL
                            AND DATE(deadline) >= DATE(NOW()) 
                            AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                    )
            ) = 0");
    ELSE
        SET @NO_UPCOMING_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status <> 'FINISH'
                    AND deadline IS NOT NULL
                    AND DATE(deadline) >= DATE(NOW()) 
                    AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
            ) = 0");
    END IF;

    -- Overdue: Leads with no completed activities in after deadline.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @OVERDUE_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status <> 'FINISH'
                            AND deadline IS NOT NULL
                            AND DATE(deadline) < DATE(NOW())
                    )
            ) <> 0");
    ELSE
        SET @OVERDUE_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status <> 'FINISH'
                    AND deadline IS NOT NULL
                    AND DATE(deadline) < DATE(NOW())
            ) <> 0");
    END IF;

    -- Due Today: Leads with no completed activities with deadline is today.
    IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
        SET @DUE_TODAY_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity_belong_to 
                WHERE belong_to_module='", MODULE_CODE, "' 
                    AND belong_to_id = ", table_name, ".id 
                    AND 
                    (
                        SELECT id 
                        FROM activity 
                        WHERE id = activity_id 
                            AND status <> 'FINISH'
                            AND deadline IS NOT NULL
                            AND DATE(deadline) = DATE(NOW())
                    )
            ) <> 0");
    ELSE
        SET @DUE_TODAY_SQL = CONCAT("SELECT count(id) 
            FROM ", TABLE_NAME, " 
            WHERE 
            (
                SELECT COUNT(*) 
                FROM activity 
                WHERE related_to_module='", MODULE_CODE, "' 
                    AND related_to_id = ", table_name, ".id 
                    AND status <> 'FINISH'
                    AND deadline IS NOT NULL
                    AND DATE(deadline) = DATE(NOW())
            ) <> 0");
    END IF;

    -- Build Main SQL
    SET @MAIN_SQL = CONCAT("SELECT (", @NO_ACTIVITY_SQL, ") as no_activity, (", @IDLE_SQL, ") as idle, (", @UPCOMING_SQL, ") as upcoming, (", @NO_UPCOMING_SQL, ") as no_upcoming, (", @OVERDUE_SQL, ") as overdue, (", @DUE_TODAY_SQL, ") as due_today");

	PREPARE stmt FROM @MAIN_SQL;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END