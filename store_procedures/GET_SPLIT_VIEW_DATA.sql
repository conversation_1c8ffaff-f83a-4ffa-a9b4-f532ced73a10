CREATE DEFINER=`ohcrm`@`%` PROCEDURE `GET_SPLIT_VIEW_DATA`(IN DATA_JSON JSON)
BEGIN
    DECLARE FILTERS_LENGTH INT;
    DECLARE SEARCH_FIELDS_LENGTH INT;
    DECLARE VIEW_FIELDS_LENGTH INT;
    DECLARE I INT DEFAULT 0;
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE SEARCH_TEXT VARCHAR(50);
    DECLARE SORT_BY VARCHAR(50);
    DECLARE SORT_DIRECTION VARCHAR(10);
    DECLARE PAGE INT;
    DECLARE _LIMIT INT;
    DECLARE FILTERS JSON;
    DECLARE SEARCH_FIELDS JSON;
    DECLARE VIEW_FIELDS JSON;
    DECLARE VIEW_SELECT TEXT;
    DECLARE WHERE_CONDITION TEXT;
    DECLARE FILTER_CONDITION TEXT;
    DECLARE SEARCH_CONDITION TEXT;
    DECLARE ORDER_CONDITION TEXT;
    DECLARE PAGE_CONDITION TEXT;
    DECLARE LIMIT_CONDITION TEXT;
    DECLARE NAME_COLUMN VARCHAR(50);
    DECLARE SYMBOL VARCHAR(30);
    DECLARE _VALUE VARCHAR(100);
    DECLARE SEARCH_COLUMN VARCHAR(50);
    DECLARE VIEW_COLUMN VARCHAR(50);

    -- SET DATA_JSON = '{"module_code":"product", "filters":[{"name":"name","symbol":"IS", "value":"a"},{"name":"description","symbol":"CONTAIN", "value":"a"}]}';

    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.table_name'));
    SET SEARCH_TEXT = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.search_text'));
    SET SORT_BY = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.sort_by'));
    SET SORT_DIRECTION = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.sort_direction'));
    SET PAGE = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.page'));
    SET _LIMIT = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.limit'));
    SET FILTERS = JSON_EXTRACT(DATA_JSON, '$.filters');
    SET SEARCH_FIELDS = JSON_EXTRACT(DATA_JSON, '$.search_fields');
    SET VIEW_FIELDS = JSON_EXTRACT(DATA_JSON, '$.view_fields');

    SET FILTERS_LENGTH = JSON_LENGTH(FILTERS);
    SET SEARCH_FIELDS_LENGTH = JSON_LENGTH(SEARCH_FIELDS);
    SET VIEW_FIELDS_LENGTH = JSON_LENGTH(VIEW_FIELDS);
    
    WHILE I < FILTERS_LENGTH
    DO
    
        SET NAME_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].name')));
        
        SET SYMBOL = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].symbol')));

        SET _VALUE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].value')));
		
        SET FILTER_CONDITION = "";
        IF SYMBOL = 'IS' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_IS' THEN
			SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        ELSEIF SYMBOL = 'CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'NOT_CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " NOT LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'GREATER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " > '", _VALUE, "'");
		ELSEIF SYMBOL = 'SMALLER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " < '", _VALUE, "'");
		ELSEIF SYMBOL = 'EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        END IF;

        -- Thêm AND nếu không phải phần tử cuối
        IF I < FILTERS_LENGTH - 1 THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, ' AND ');
        END IF;

        SET I = I + 1;
    END WHILE;
    
    -- Build WHERE condition
    IF FILTERS_LENGTH > 0 THEN
        SET WHERE_CONDITION = CONCAT("WHERE ", FILTER_CONDITION);
    END IF;

    -- Add search condition
    SET SEARCH_CONDITION = "";
    SET I = 0;
    WHILE I < SEARCH_FIELDS_LENGTH
    DO
    
      SET SEARCH_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(SEARCH_FIELDS, CONCAT('$[', I, ']')));	
      SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, SEARCH_COLUMN, " LIKE '%",SEARCH_TEXT,"%'");

        -- Thêm AND nếu không phải phần tử cuối
        IF I < SEARCH_FIELDS_LENGTH - 1 THEN
            SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, ' OR ');
        END IF;

        SET I = I + 1;
    END WHILE;

    -- Build SELECT text
    SET VIEW_SELECT = "";
    SET I = 0;
    WHILE I < VIEW_FIELDS_LENGTH
    DO
    
      SET VIEW_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(VIEW_FIELDS, CONCAT('$[', I, ']')));	
      SET VIEW_SELECT = CONCAT(VIEW_SELECT, VIEW_COLUMN);

        -- Thêm , nếu không phải phần tử cuối
        IF I < VIEW_FIELDS_LENGTH - 1 THEN
            SET VIEW_SELECT = CONCAT(VIEW_SELECT, ', ');
        END IF;

        SET I = I + 1;
    END WHILE;

    -- Build WHERE condition
    IF SEARCH_FIELDS_LENGTH > 0 THEN
        IF WHERE_CONDITION IS NULL THEN
            SET WHERE_CONDITION = CONCAT("WHERE (", SEARCH_CONDITION, ")");
        ELSE
            SET WHERE_CONDITION = CONCAT(WHERE_CONDITION, " AND (", SEARCH_CONDITION, ")");
        END IF;
    END IF;

    -- Order By
    SET ORDER_CONDITION = CONCAT("ORDER BY ", SORT_BY, " ", SORT_DIRECTION);
    -- Page
    SET PAGE_CONDITION = CONCAT("OFFSET ", (PAGE * _LIMIT));
    -- Limit
    SET LIMIT_CONDITION = CONCAT("LIMIT ", _LIMIT);
    
    IF WHERE_CONDITION IS NULL THEN
        SET @SQL_STRING = CONCAT("SELECT ", VIEW_SELECT, " FROM ", TABLE_NAME, " ", ORDER_CONDITION, " ", LIMIT_CONDITION, " ", PAGE_CONDITION);
    ELSE
        SET @SQL_STRING = CONCAT("SELECT ", VIEW_SELECT, " FROM ", TABLE_NAME, " ", WHERE_CONDITION, " " , ORDER_CONDITION, " ", LIMIT_CONDITION, " ", PAGE_CONDITION);
    END IF;
   
	PREPARE stmt FROM @SQL_STRING;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END