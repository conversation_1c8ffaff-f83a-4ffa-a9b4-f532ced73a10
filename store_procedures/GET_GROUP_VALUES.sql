CREATE DEFINER=`ohcrm`@`%` PROCEDURE `GET_GROUP_VALUES`(IN FILTERS_JSON JSON)
BEGIN
    DECLARE FIELD_NAME VARCHAR(50);
    DECLARE FILTERS_LENGTH INT;
    DECLARE SEARCH_FIELDS_LENGTH INT;
    DECLARE I INT DEFAULT 0;
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE SEARCH_TEXT VARCHAR(50);
    DECLARE FILTERS JSON;
    DECLARE SEARCH_FIELDS JSON;
    DECLARE WHERE_CONDITION TEXT;
    DECLARE FILTER_CONDITION TEXT;
    DECLARE SEARCH_CONDITION TEXT;
    DECLARE NAME_COLUMN VARCHAR(50);
    DECLARE SYMBOL VARCHAR(30);
    DECLARE _VALUE VARCHAR(100);
    DECLARE SEARCH_COLUMN VARCHAR(50);

    -- SET FILTERS_JSON = '{"module_code":"product", "filters":[{"name":"name","symbol":"IS", "value":"a"},{"name":"description","symbol":"CONTAIN", "value":"a"}]}';

    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.table_name'));
    SET FIELD_NAME = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.field_name'));    
    SET FILTERS =JSON_EXTRACT(FILTERS_JSON, '$.filters');
    SET SEARCH_TEXT = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.search_text'));
    SET SEARCH_FIELDS =JSON_EXTRACT(FILTERS_JSON, '$.search_fields');

    SET FILTERS_LENGTH = JSON_LENGTH(FILTERS);
    SET SEARCH_FIELDS_LENGTH = JSON_LENGTH(SEARCH_FIELDS);
    
    WHILE I < FILTERS_LENGTH
    DO
    
        SET NAME_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].name')));
        
        SET SYMBOL = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].symbol')));

        SET _VALUE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].value')));
		
        SET FILTER_CONDITION = "";
        IF SYMBOL = 'IS' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_IS' THEN
			SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        ELSEIF SYMBOL = 'CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'NOT_CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " NOT LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'GREATER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " > '", _VALUE, "'");
		ELSEIF SYMBOL = 'SMALLER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " < '", _VALUE, "'");
		ELSEIF SYMBOL = 'EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        END IF;

        -- Thêm AND nếu không phải phần tử cuối
        IF I < FILTERS_LENGTH - 1 THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, ' AND ');
        END IF;

        SET I = I + 1;
    END WHILE;

    -- Build WHERE condition
    IF FILTERS_LENGTH > 0 THEN
        SET WHERE_CONDITION = CONCAT("WHERE ", FILTER_CONDITION);
    END IF;
    
    -- Add search condition
    SET SEARCH_CONDITION = "";
    SET I = 0;
    WHILE I < SEARCH_FIELDS_LENGTH
    DO
    
      SET SEARCH_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(SEARCH_FIELDS, CONCAT('$[', I, ']')));	
      SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, SEARCH_COLUMN, " LIKE '%",SEARCH_TEXT,"%'");

        -- Thêm AND nếu không phải phần tử cuối
        IF I < SEARCH_FIELDS_LENGTH - 1 THEN
            SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, ' OR ');
        END IF;

        SET I = I + 1;
    END WHILE;
    
    -- Build WHERE condition
    IF SEARCH_FIELDS_LENGTH > 0 THEN
        IF WHERE_CONDITION IS NULL THEN
            SET WHERE_CONDITION = CONCAT("WHERE (", SEARCH_CONDITION, ")");
        ELSE
            SET WHERE_CONDITION = CONCAT(WHERE_CONDITION, " AND (", SEARCH_CONDITION, ")");
        END IF;
    END IF;

    IF WHERE_CONDITION IS NULL THEN
        SET @SQL_STRING = CONCAT("SELECT ", FIELD_NAME, ", COUNT(*) FROM ",TABLE_NAME, " GROUP BY ", FIELD_NAME);
    ELSE
        SET @SQL_STRING = CONCAT("SELECT ", FIELD_NAME, ", COUNT(*) FROM ",TABLE_NAME," ", WHERE_CONDITION, " GROUP BY ", FIELD_NAME);
    END IF;
   
	PREPARE stmt FROM @SQL_STRING;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END