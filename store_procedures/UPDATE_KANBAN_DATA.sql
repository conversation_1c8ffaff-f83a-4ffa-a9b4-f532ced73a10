CREATE DEFINER=`ohcrm`@`%` PROCEDURE `UPDATE_KANBAN_DATA`(IN DATA_JSON JSON)
BEGIN
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE FIELD_NAME VARCHAR(50);
    DECLARE FIELD_VALUE VARCHAR(50);
    DECLARE _ID INT;

    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.table_name'));
    SET FIELD_NAME = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.field_name'));
    SET FIELD_VALUE = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.field_value'));
    SET _ID = JSON_UNQUOTE(JSON_EXTRACT(DATA_JSON, '$.id'));
    
    SET @SQL_STRING = CONCAT("UPDATE ", TABLE_NAME," SET ", FIELD_NAME, " = '", FIELD_VALUE, "' WHERE id = ", _ID);
   
	PREPARE stmt FROM @SQL_STRING;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END