CREATE DEFINER=`ohcrm`@`%` PROCEDURE `GET_ALLS_BY_FILTER`(IN FILTERS_JSON JSON)
BEGIN
    DECLARE FILTERS_LENGTH INT;
    DECLARE SEARCH_FIELDS_LENGTH INT;
    DECLARE I INT DEFAULT 0;
    DECLARE MODULE_CODE VARCHAR(50);
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE SEARCH_TEXT VARCHAR(50);
    DECLARE GROUP_FIELD VARCHAR(50);
    DECLARE GROUP_VALUE VARCHAR(50);
    DECLARE SORT_BY VARCHAR(50);
    DECLARE SORT_DIRECTION VARCHAR(10);
    DECLARE PAGE INT;
    DECLARE _LIMIT INT;
    DECLARE FILTERS JSON;
    DECLARE SEARCH_FIELDS JSON;
    DECLARE GROUP_ACTIVITY_CONDITION TEXT;
    DECLARE WHERE_CONDITION TEXT;
    DECLARE FILTER_CONDITION TEXT;
    DECLARE SEARCH_CONDITION TEXT;
    DECLARE ORDER_CONDITION TEXT;
    DECLARE PAGE_CONDITION TEXT;
    DECLARE LIMIT_CONDITION TEXT;
    DECLARE NAME_COLUMN VARCHAR(50);
    DECLARE SYMBOL VARCHAR(30);
    DECLARE _VALUE VARCHAR(100);
    DECLARE SEARCH_COLUMN VARCHAR(50);

    -- SET FILTERS_JSON = '{"module_code":"product", "filters":[{"name":"name","symbol":"IS", "value":"a"},{"name":"description","symbol":"CONTAIN", "value":"a"}]}';

    SET MODULE_CODE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.module_code'));
    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.table_name'));
    SET SEARCH_TEXT = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.search_text'));
    SET GROUP_FIELD = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.group_field'));
    SET GROUP_VALUE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.group_value'));
    SET SORT_BY = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.sort_by'));
    SET SORT_DIRECTION = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.sort_direction'));
    SET PAGE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.page'));
    SET _LIMIT = JSON_UNQUOTE(JSON_EXTRACT(FILTERS_JSON, '$.limit'));
    SET FILTERS =JSON_EXTRACT(FILTERS_JSON, '$.filters');
    SET SEARCH_FIELDS =JSON_EXTRACT(FILTERS_JSON, '$.search_fields');
    
    SET FILTERS_LENGTH = JSON_LENGTH(FILTERS);
    SET SEARCH_FIELDS_LENGTH = JSON_LENGTH(SEARCH_FIELDS);
    
    WHILE I < FILTERS_LENGTH
    DO
    
        SET NAME_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].name')));
        
        SET SYMBOL = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].symbol')));

        SET _VALUE = JSON_UNQUOTE(JSON_EXTRACT(FILTERS, CONCAT('$[', I, '].value')));
		
        SET FILTER_CONDITION = "";
        IF SYMBOL = 'IS' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_IS' THEN
			SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        ELSEIF SYMBOL = 'CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'NOT_CONTAIN' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " NOT LIKE '%",_VALUE,"%'");
		ELSEIF SYMBOL = 'GREATER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " > '", _VALUE, "'");
		ELSEIF SYMBOL = 'SMALLER' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " < '", _VALUE, "'");
		ELSEIF SYMBOL = 'EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " = '", _VALUE, "'");
		ELSEIF SYMBOL = 'NOT_EQUAL' THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, NAME_COLUMN, " != '", _VALUE, "'");
        END IF;

        -- Thêm AND nếu không phải phần tử cuối
        IF I < FILTERS_LENGTH - 1 THEN
            SET FILTER_CONDITION = CONCAT(FILTER_CONDITION, ' AND ');
        END IF;

        SET I = I + 1;
    END WHILE;
    
    -- Build WHERE condition
    IF FILTERS_LENGTH > 0 THEN
        SET WHERE_CONDITION = CONCAT("WHERE ", FILTER_CONDITION);
    END IF;

    -- Add search condition
    SET SEARCH_CONDITION = "";
    SET I = 0;
    WHILE I < SEARCH_FIELDS_LENGTH
    DO
    
      SET SEARCH_COLUMN = JSON_UNQUOTE(JSON_EXTRACT(SEARCH_FIELDS, CONCAT('$[', I, ']')));	
      SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, SEARCH_COLUMN, " LIKE '%",SEARCH_TEXT,"%'");

        -- Thêm AND nếu không phải phần tử cuối
        IF I < SEARCH_FIELDS_LENGTH - 1 THEN
            SET SEARCH_CONDITION = CONCAT(SEARCH_CONDITION, ' OR ');
        END IF;

        SET I = I + 1;
    END WHILE;

    -- Build WHERE condition
    IF SEARCH_FIELDS_LENGTH > 0 THEN
        IF WHERE_CONDITION IS NULL THEN
            SET WHERE_CONDITION = CONCAT("WHERE (", SEARCH_CONDITION, ")");
        ELSE
            SET WHERE_CONDITION = CONCAT(WHERE_CONDITION, " AND (", SEARCH_CONDITION, ")");
        END IF;
    END IF;
    
    -- Add group by condition
    IF GROUP_FIELD IS NOT NULL THEN
        IF GROUP_FIELD = 'GROUP_BY_ACTIVITY' THEN
            IF GROUP_VALUE = 'no_activity' THEN
                -- No Activity: Module object with no completed activities.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status = 'FINISH'
                                )
                        ) = 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT(" 
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status = 'FINISH'
                        ) = 0");
                END IF;
            ELSEIF GROUP_VALUE = 'idle' THEN
                -- Idle: Module object with past activity but no completed activities in the last 30 days.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT(" 
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status = 'FINISH'
                                        AND deadline IS NOT NULL
                                        AND DATE(created_at) >= DATE(NOW()) - INTERVAL 30 DAY
                                )
                        ) = 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT(" 
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status = 'FINISH'
                                AND deadline IS NOT NULL
                                AND DATE(created_at) >= DATE(NOW()) - INTERVAL 30 DAY
                        ) = 0");
                END IF;
            ELSEIF GROUP_VALUE = 'upcoming' THEN
                -- Upcoming: Leads with activities due in the next 30 days.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status <> 'FINISH'
                                        AND deadline IS NOT NULL
                                        AND DATE(deadline) >= DATE(NOW()) 
                                        AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                                )
                        ) <> 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status <> 'FINISH'
                                AND deadline IS NOT NULL
                                AND DATE(deadline) >= DATE(NOW()) 
                                AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                        ) <> 0");
                END IF;
            ELSEIF GROUP_VALUE = 'no_upcoming' THEN
                -- No Upcoming: Leads with recently completed activity but no future activities scheduled.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT(" 
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status <> 'FINISH'
                                        AND deadline IS NOT NULL
                                        AND DATE(deadline) >= DATE(NOW()) 
                                        AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                                )
                        ) = 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status <> 'FINISH'
                                AND deadline IS NOT NULL
                                AND DATE(deadline) >= DATE(NOW()) 
                                AND DATE(deadline) <= DATE(NOW()) + INTERVAL 30 DAY
                        ) = 0");
                END IF;
            ELSEIF GROUP_VALUE = 'overdue' THEN
                -- Overdue: Leads with no completed activities in after deadline.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status <> 'FINISH'
                                        AND deadline IS NOT NULL
                                        AND DATE(deadline) < DATE(NOW())
                                )
                        ) <> 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status <> 'FINISH'
                                AND deadline IS NOT NULL
                                AND DATE(deadline) < DATE(NOW())
                        ) <> 0");
                END IF;
            ELSEIF GROUP_VALUE = 'due_today' THEN
                -- Due Today: Leads with no completed activities with deadline is today.
                IF MODULE_CODE = 'CONTACT' OR MODULE_CODE = 'LEAD' THEN 
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity_belong_to 
                            WHERE belong_to_module='", MODULE_CODE, "' 
                                AND belong_to_id = ", table_name, ".id 
                                AND 
                                (
                                    SELECT id 
                                    FROM activity 
                                    WHERE id = activity_id 
                                        AND status <> 'FINISH'
                                        AND deadline IS NOT NULL
                                        AND DATE(deadline) = DATE(NOW())
                                )
                        ) <> 0");
                ELSE
                    SET GROUP_ACTIVITY_CONDITION = CONCAT("
                        (
                            SELECT COUNT(*) 
                            FROM activity 
                            WHERE related_to_module='", MODULE_CODE, "' 
                                AND related_to_id = ", table_name, ".id 
                                AND status <> 'FINISH'
                                AND deadline IS NOT NULL
                                AND DATE(deadline) = DATE(NOW())
                        ) <> 0");
                END IF;
            ELSE
                SET GROUP_ACTIVITY_CONDITION = NULL;
            END IF;

            IF WHERE_CONDITION IS NULL THEN
                IF GROUP_ACTIVITY_CONDITION IS NOT NULL THEN
                    SET WHERE_CONDITION = CONCAT("WHERE ", GROUP_ACTIVITY_CONDITION);
                END IF;
            ELSE
                IF GROUP_ACTIVITY_CONDITION IS NOT NULL THEN
                    SET WHERE_CONDITION = CONCAT(WHERE_CONDITION, " AND ", GROUP_ACTIVITY_CONDITION);
                END IF;
            END IF;
        ELSE
            IF WHERE_CONDITION IS NULL THEN
                SET WHERE_CONDITION = CONCAT("WHERE ", GROUP_FIELD, " = '", GROUP_VALUE, "'");
            ELSE
                SET WHERE_CONDITION = CONCAT(WHERE_CONDITION, " AND ", GROUP_FIELD, " = '", GROUP_VALUE, "'");
            END IF;
        END IF;
    END IF;
    
    -- Order By
    SET ORDER_CONDITION = CONCAT("ORDER BY ", SORT_BY, " ", SORT_DIRECTION);
    -- Page
    SET PAGE_CONDITION = CONCAT("OFFSET ", (PAGE * _LIMIT));
    -- Limit
    SET LIMIT_CONDITION = CONCAT("LIMIT ", _LIMIT);
    
    IF WHERE_CONDITION IS NULL THEN
        SET @SQL_STRING = CONCAT("SELECT * FROM ",TABLE_NAME, " ", ORDER_CONDITION, " ", LIMIT_CONDITION, " ", PAGE_CONDITION);
    ELSE
        SET @SQL_STRING = CONCAT("SELECT * FROM ",TABLE_NAME," ", WHERE_CONDITION, " " , ORDER_CONDITION, " ", LIMIT_CONDITION, " ", PAGE_CONDITION);
    END IF;
   
	PREPARE stmt FROM @SQL_STRING;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END