CREATE DEFINER=`ohcrm`@`%` PROCEDURE `COUNT_DATA_RELATED_LIST_VALUE`(IN PARAMS_JSON JSON)
BEGIN
    DECLARE I INT DEFAULT 0;
    DECLARE TABLE_NAME VARCHAR(50);
    DECLARE COLUMN_NAME VARCHAR(50);
    DECLARE COLUMN_VALUE VARCHAR(50);

    -- SET PARAMS_JSON = '{"table_name":"leads", "column_name":"industry", "column_value":"HARDWARE"}';
    -- call COUNT_DATA_RELATED_LIST_VALUE('{"table_name":"leads", "column_name":"industry", "column_value":"HARDWARE"}');
    
    SET TABLE_NAME = JSON_UNQUOTE(JSON_EXTRACT(PARAMS_JSON, '$.table_name'));
    SET COLUMN_NAME = JSON_UNQUOTE(JSON_EXTRACT(PARAMS_JSON, '$.column_name'));
    SET COLUMN_VALUE = JSON_UNQUOTE(JSON_EXTRACT(PARAMS_JSON, '$.column_value'));

    SET @SQL_STRING = CONCAT("SELECT count(*) FROM ", TABLE_NAME," ", "WHERE ", COLUMN_NAME, " = '", COLUMN_VALUE, "'");
   
	PREPARE stmt FROM @SQL_STRING;
	EXECUTE stmt;
	DEALLOCATE PREPARE stmt; 
		
END