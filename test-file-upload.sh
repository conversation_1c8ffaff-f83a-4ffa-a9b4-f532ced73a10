#!/bin/bash

# Test script for file upload API
# This script tests the new file upload functionality with different file types and modules

API_BASE_URL="http://api-dev.ttis.vn"
UPLOAD_ENDPOINT="/api/settings/files/upload"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Testing File Upload API${NC}"
echo "=================================="

# Function to test file upload
test_upload() {
    local file_path="$1"
    local module="$2"
    local description="$3"
    local auth_token="$4"
    local org_id="$5"
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "File: $file_path"
    echo "Module: $module"
    
    if [ ! -f "$file_path" ]; then
        echo -e "${RED}❌ File not found: $file_path${NC}"
        return 1
    fi
    
    # Make the API call
    response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Authorization: Bearer $auth_token" \
        -H "Organization-Id: $org_id" \
        -H "Accept-Language: en" \
        -F "files=@$file_path" \
        -F "module=$module" \
        "$API_BASE_URL$UPLOAD_ENDPOINT")
    
    # Extract HTTP status code
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [ "$http_code" -eq 200 ]; then
        echo -e "${GREEN}✅ Upload successful (HTTP $http_code)${NC}"
        echo "Response: $response_body" | jq '.' 2>/dev/null || echo "$response_body"
        
        # Extract file path from response and test access
        file_path_from_response=$(echo "$response_body" | jq -r '.data.uploadedFiles[0].filePath' 2>/dev/null)
        if [ "$file_path_from_response" != "null" ] && [ "$file_path_from_response" != "" ]; then
            echo -e "\n${YELLOW}Testing file access...${NC}"
            access_url="$API_BASE_URL/$file_path_from_response"
            echo "URL: $access_url"
            
            access_response=$(curl -s -w "%{http_code}" -o /dev/null "$access_url")
            if [ "$access_response" -eq 200 ]; then
                echo -e "${GREEN}✅ File accessible via HTTP${NC}"
            else
                echo -e "${RED}❌ File not accessible (HTTP $access_response)${NC}"
            fi
        fi
    else
        echo -e "${RED}❌ Upload failed (HTTP $http_code)${NC}"
        echo "Response: $response_body"
    fi
}

# Check if required parameters are provided
if [ $# -lt 2 ]; then
    echo "Usage: $0 <auth_token> <organization_id> [test_files_directory]"
    echo ""
    echo "Example:"
    echo "  $0 'your-jwt-token' 123 /path/to/test/files"
    echo ""
    echo "If test_files_directory is not provided, the script will create sample test files."
    exit 1
fi

AUTH_TOKEN="$1"
ORG_ID="$2"
TEST_FILES_DIR="${3:-./test_files}"

# Create test files directory if it doesn't exist
mkdir -p "$TEST_FILES_DIR"

# Create sample test files if they don't exist
create_test_files() {
    echo -e "${YELLOW}Creating sample test files...${NC}"
    
    # Create a sample image (simple PNG)
    if [ ! -f "$TEST_FILES_DIR/test_image.png" ]; then
        # Create a minimal PNG file (1x1 pixel)
        echo -e '\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\tpHYs\x00\x00\x0b\x13\x00\x00\x0b\x13\x01\x00\x9a\x9c\x18\x00\x00\x00\nIDATx\x9cc\xf8\x00\x00\x00\x01\x00\x01\x00\x00\x00\x00IEND\xaeB`\x82' > "$TEST_FILES_DIR/test_image.png"
    fi
    
    # Create a sample PDF
    if [ ! -f "$TEST_FILES_DIR/test_document.pdf" ]; then
        echo "%PDF-1.4
1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj
2 0 obj<</Type/Pages/Kids[3 0 R]/Count 1>>endobj
3 0 obj<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>endobj
xref
0 4
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
trailer<</Size 4/Root 1 0 R>>
startxref
190
%%EOF" > "$TEST_FILES_DIR/test_document.pdf"
    fi
    
    # Create a sample JavaScript file
    if [ ! -f "$TEST_FILES_DIR/test_script.js" ]; then
        echo "// Sample JavaScript file for testing
console.log('Hello, World!');

function testFunction() {
    return 'This is a test file upload';
}

// Export for testing
module.exports = { testFunction };" > "$TEST_FILES_DIR/test_script.js"
    fi
    
    # Create a sample CSS file
    if [ ! -f "$TEST_FILES_DIR/test_styles.css" ]; then
        echo "/* Sample CSS file for testing */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f5f5;
}

.test-class {
    color: #333;
    font-size: 16px;
    line-height: 1.5;
}" > "$TEST_FILES_DIR/test_styles.css"
    fi
    
    # Create a sample HTML file
    if [ ! -f "$TEST_FILES_DIR/test_page.html" ]; then
        echo "<!DOCTYPE html>
<html lang=\"en\">
<head>
    <meta charset=\"UTF-8\">
    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
    <title>Test HTML File</title>
</head>
<body>
    <h1>Test HTML File</h1>
    <p>This is a sample HTML file for testing file upload functionality.</p>
</body>
</html>" > "$TEST_FILES_DIR/test_page.html"
    fi
    
    # Create a sample text file
    if [ ! -f "$TEST_FILES_DIR/test_document.txt" ]; then
        echo "This is a sample text file for testing the file upload API.

It contains multiple lines and various characters to test the upload functionality.

Features to test:
- File upload with different modules
- File categorization (media vs files)
- Directory structure creation
- File accessibility via HTTP

Test completed successfully!" > "$TEST_FILES_DIR/test_document.txt"
    fi
    
    echo -e "${GREEN}✅ Test files created in $TEST_FILES_DIR${NC}"
}

# Create test files if needed
create_test_files

echo -e "\n${YELLOW}Starting file upload tests...${NC}"

# Test different file types with different modules
test_upload "$TEST_FILES_DIR/test_image.png" "PLAYBOOK" "Image file to PLAYBOOK module" "$AUTH_TOKEN" "$ORG_ID"
test_upload "$TEST_FILES_DIR/test_document.pdf" "LEAD" "PDF document to LEAD module" "$AUTH_TOKEN" "$ORG_ID"
test_upload "$TEST_FILES_DIR/test_script.js" "OPPORTUNITY" "JavaScript file to OPPORTUNITY module" "$AUTH_TOKEN" "$ORG_ID"
test_upload "$TEST_FILES_DIR/test_styles.css" "PLAYBOOK" "CSS file to PLAYBOOK module" "$AUTH_TOKEN" "$ORG_ID"
test_upload "$TEST_FILES_DIR/test_page.html" "LEAD" "HTML file to LEAD module" "$AUTH_TOKEN" "$ORG_ID"
test_upload "$TEST_FILES_DIR/test_document.txt" "OPPORTUNITY" "Text file to OPPORTUNITY module" "$AUTH_TOKEN" "$ORG_ID"

echo -e "\n${YELLOW}Testing validation...${NC}"

# Test invalid module
test_upload "$TEST_FILES_DIR/test_image.png" "invalid-module" "Invalid module name (should fail)" "$AUTH_TOKEN" "$ORG_ID"

echo -e "\n${YELLOW}File upload tests completed!${NC}"
echo "=================================="
