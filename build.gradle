plugins {
	id 'java'
	id 'org.springframework.boot' version '3.3.5'
	id 'io.spring.dependency-management' version '1.1.6'
}

group = 'com.tti'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(17)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

ext {
	set('springCloudVersion', "2023.0.3")
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-webflux'
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.cloud:spring-cloud-starter-config'
	implementation 'com.playtika.reactivefeign:feign-reactor-spring-cloud-starter:4.2.1'
	implementation 'com.playtika.reactivefeign:feign-reactor-spring-configuration:4.2.1'
	implementation 'com.playtika.reactivefeign:feign-reactor-core:4.2.1'
	implementation 'org.springframework.cloud:spring-cloud-starter-netflix-eureka-client'
	implementation 'org.springdoc:springdoc-openapi-starter-webflux-ui:2.0.2'
	implementation 'io.github.openfeign:feign-slf4j:9.3.1'
	implementation 'com.google.guava:guava:33.4.4-jre'
	implementation 'com.opencsv:opencsv:5.10'
	implementation 'org.json:json:20200518'
	implementation 'org.apache.poi:poi:5.3.0'
	implementation 'org.apache.poi:poi-ooxml:5.3.0'
	implementation 'org.jxls:jxls-jexcel:1.0.9'
	implementation 'org.dhatim:fastexcel-reader:0.18.1'
	implementation 'org.dhatim:fastexcel:0.18.1'
	implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-impl:0.11.5'
	implementation 'io.jsonwebtoken:jjwt-jackson:0.11.5'
	implementation 'org.mapstruct:mapstruct:1.4.2.Final', 'org.projectlombok:lombok:1.18.20'
	annotationProcessor "org.mapstruct:mapstruct-processor:1.4.2.Final", "org.projectlombok:lombok:1.18.20", "org.projectlombok:lombok-mapstruct-binding:0.2.0"
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'com.mysql:mysql-connector-j'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testImplementation 'io.projectreactor:reactor-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
}

dependencyManagement {
	imports {
		mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
	}
}

tasks.named('test') {
	useJUnitPlatform()
}
