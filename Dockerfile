FROM openjdk:17-jdk-alpine

RUN addgroup -S tti && adduser -S oh_crm -G tti

ARG DEPENDENCY=build/dependency
COPY ${DEPENDENCY}/BOOT-INF/lib /app/lib
COPY ${DEPENDENCY}/META-INF /app/META-INF
COPY ${DEPENDENCY}/BOOT-INF/classes /app

# JRE fails to load fonts if there are no standard fonts in the image; DejaVu is a good choice,
# see https://github.com/docker-library/openjdk/issues/73#issuecomment-207816707
RUN apk add --update ttf-dejavu && rm -rf /var/cache/apk/*

USER oh_crm:tti
WORKDIR /app
ENTRYPOINT ["java","-Xmx1024m","-cp",".:./lib/*","com.tti.oh_crm_service.OhCrmServiceApplication"]